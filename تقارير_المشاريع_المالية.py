#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
import mysql.connector
from datetime import datetime, date
from متغيرات import *
from الطباعة import print_project_financial_report

class ProjectFinancialReportsWindow(QMainWindow):
    def __init__(self, parent=None, project_id=None, project_code=None, all_projects_mode=False):
        super().__init__(parent)
        self.parent = parent
        self.project_id = project_id
        self.project_code = project_code
        self.all_projects_mode = all_projects_mode
        self.project_name = ""
        self.client_name = ""
        
        # إعداد النافذة
        if self.all_projects_mode:
            self.setWindowTitle("التقارير المالية - جميع المشاريع")
        else:
            self.setWindowTitle(f"التقارير المالية للمشروع - {project_code}")
        self.setGeometry(100, 100, 1400, 800)
        self.setWindowIcon(QIcon("icons/financial_reports.png"))
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e1e1e1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #0078d4;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        self.init_ui()
        self.load_project_info()
        self.load_all_data()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # معلومات المشروع
        self.create_project_info_section(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        # أزرار التحكم
        self.create_control_buttons(main_layout)

    def create_project_info_section(self, layout):
        """إنشاء قسم معلومات المشروع أو الفلاتر"""
        if self.all_projects_mode:
            # إنشاء قسم الفلاتر لجميع المشاريع
            self.create_global_filters_section(layout)
        else:
            # إنشاء قسم معلومات المشروع المحدد
            info_group = QGroupBox("معلومات المشروع")
            info_layout = QHBoxLayout(info_group)

            # معلومات المشروع
            self.project_info_label = QLabel("جاري تحميل معلومات المشروع...")
            self.project_info_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #2c3e50;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 5px;
                }
            """)
            info_layout.addWidget(self.project_info_label)

            layout.addWidget(info_group)

    def create_global_filters_section(self, layout):
        """إنشاء قسم الفلاتر العامة لجميع المشاريع"""
        filters_group = QGroupBox("فلاتر البحث - جميع المشاريع")
        filters_layout = QGridLayout(filters_group)

        # فلتر التصنيف
        filters_layout.addWidget(QLabel("التصنيف:"), 0, 0)
        self.global_classification_filter = QComboBox()
        self.global_classification_filter.addItem("جميع التصنيفات", None)
        filters_layout.addWidget(self.global_classification_filter, 0, 1)

        # فلتر اسم المشروع
        filters_layout.addWidget(QLabel("اسم المشروع:"), 0, 2)
        self.global_project_filter = QComboBox()
        self.global_project_filter.setEditable(True)
        self.global_project_filter.addItem("جميع المشاريع", None)
        filters_layout.addWidget(self.global_project_filter, 0, 3)

        # فلتر العميل
        filters_layout.addWidget(QLabel("العميل:"), 1, 0)
        self.global_client_filter = QComboBox()
        self.global_client_filter.addItem("جميع العملاء", None)
        filters_layout.addWidget(self.global_client_filter, 1, 1)

        # فلتر التاريخ من
        filters_layout.addWidget(QLabel("من تاريخ:"), 1, 2)
        self.global_from_date = QDateEdit()
        self.global_from_date.setDate(QDate.currentDate().addMonths(-12))
        self.global_from_date.setCalendarPopup(True)
        filters_layout.addWidget(self.global_from_date, 1, 3)

        # فلتر التاريخ إلى
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 2, 0)
        self.global_to_date = QDateEdit()
        self.global_to_date.setDate(QDate.currentDate())
        self.global_to_date.setCalendarPopup(True)
        filters_layout.addWidget(self.global_to_date, 2, 1)

        # زر التحديث
        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        refresh_btn.clicked.connect(self.load_all_data)
        filters_layout.addWidget(refresh_btn, 2, 2)

        # زر إعادة تعيين الفلاتر
        reset_btn = QPushButton("إعادة تعيين")
        reset_btn.clicked.connect(self.reset_global_filters)
        filters_layout.addWidget(reset_btn, 2, 3)

        layout.addWidget(filters_group)

        # تحميل بيانات الفلاتر
        self.load_filter_data()

    def create_tabs(self, layout):
        """إنشاء التبويبات"""
        self.tab_widget = QTabWidget()
        
        # تبويب دفعات المشروع
        self.payments_tab = self.create_payments_tab()
        self.tab_widget.addTab(self.payments_tab, "دفعات المشروع")
        
        # تبويب العهد المالية
        self.custody_tab = self.create_custody_tab()
        self.tab_widget.addTab(self.custody_tab, "العهد المالية")
        
        # تبويب مصروفات العهد
        self.custody_expenses_tab = self.create_custody_expenses_tab()
        self.tab_widget.addTab(self.custody_expenses_tab, "مصروفات العهد")
        
        # تبويب التقرير الشامل
        self.comprehensive_tab = self.create_comprehensive_tab()
        self.tab_widget.addTab(self.comprehensive_tab, "التقرير الشامل")
        
        layout.addWidget(self.tab_widget)

    def create_payments_tab(self):
        """إنشاء تبويب دفعات المشروع"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # فلاتر
        filters_group = QGroupBox("فلاتر البحث")
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر التاريخ
        filters_layout.addWidget(QLabel("من تاريخ:"))
        self.payments_from_date = QDateEdit()
        self.payments_from_date.setDate(QDate.currentDate().addMonths(-12))
        self.payments_from_date.setCalendarPopup(True)
        filters_layout.addWidget(self.payments_from_date)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"))
        self.payments_to_date = QDateEdit()
        self.payments_to_date.setDate(QDate.currentDate())
        self.payments_to_date.setCalendarPopup(True)
        filters_layout.addWidget(self.payments_to_date)
        
        # زر التحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.load_payments_data)
        filters_layout.addWidget(refresh_btn)
        
        filters_layout.addStretch()
        layout.addWidget(filters_group)
        
        # جدول الدفعات
        self.payments_table = QTableWidget()
        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        layout.addWidget(self.payments_table)
        
        # إحصائيات الدفعات
        stats_group = QGroupBox("إحصائيات الدفعات")
        stats_layout = QHBoxLayout(stats_group)
        
        self.payments_total_label = QLabel("إجمالي الدفعات: 0")
        self.payments_count_label = QLabel("عدد الدفعات: 0")
        
        stats_layout.addWidget(self.payments_total_label)
        stats_layout.addWidget(self.payments_count_label)
        stats_layout.addStretch()
        
        layout.addWidget(stats_group)
        
        return tab

    def create_custody_tab(self):
        """إنشاء تبويب العهد المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول العهد
        self.custody_table = QTableWidget()
        self.custody_table.setAlternatingRowColors(True)
        self.custody_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        layout.addWidget(self.custody_table)
        
        # إحصائيات العهد
        stats_group = QGroupBox("إحصائيات العهد المالية")
        stats_layout = QHBoxLayout(stats_group)
        
        self.custody_total_label = QLabel("إجمالي العهد: 0")
        self.custody_expenses_total_label = QLabel("إجمالي المصروفات: 0")
        self.custody_remaining_label = QLabel("إجمالي المتبقي: 0")
        
        stats_layout.addWidget(self.custody_total_label)
        stats_layout.addWidget(self.custody_expenses_total_label)
        stats_layout.addWidget(self.custody_remaining_label)
        stats_layout.addStretch()
        
        layout.addWidget(stats_group)
        
        return tab

    def create_custody_expenses_tab(self):
        """إنشاء تبويب مصروفات العهد"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # فلاتر
        filters_group = QGroupBox("فلاتر البحث")
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر العهدة
        filters_layout.addWidget(QLabel("العهدة:"))
        self.custody_filter = QComboBox()
        self.custody_filter.addItem("جميع العهد", None)
        filters_layout.addWidget(self.custody_filter)
        
        # فلتر فئة المصروف
        filters_layout.addWidget(QLabel("فئة المصروف:"))
        self.expense_category_filter = QComboBox()
        self.expense_category_filter.addItem("جميع الفئات", None)
        filters_layout.addWidget(self.expense_category_filter)
        
        # زر التحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.load_custody_expenses_data)
        filters_layout.addWidget(refresh_btn)
        
        filters_layout.addStretch()
        layout.addWidget(filters_group)
        
        # جدول مصروفات العهد
        self.custody_expenses_table = QTableWidget()
        self.custody_expenses_table.setAlternatingRowColors(True)
        self.custody_expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        layout.addWidget(self.custody_expenses_table)
        
        # إحصائيات مصروفات العهد
        stats_group = QGroupBox("إحصائيات مصروفات العهد")
        stats_layout = QHBoxLayout(stats_group)
        
        self.custody_expenses_total_amount_label = QLabel("إجمالي المصروفات: 0")
        self.custody_expenses_count_label = QLabel("عدد المصروفات: 0")
        
        stats_layout.addWidget(self.custody_expenses_total_amount_label)
        stats_layout.addWidget(self.custody_expenses_count_label)
        stats_layout.addStretch()
        
        layout.addWidget(stats_group)
        
        return tab

    def create_comprehensive_tab(self):
        """إنشاء تبويب التقرير الشامل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # ملخص مالي
        summary_group = QGroupBox("الملخص المالي")
        summary_layout = QGridLayout(summary_group)
        
        # بيانات المشروع
        summary_layout.addWidget(QLabel("مبلغ المشروع:"), 0, 0)
        self.project_amount_label = QLabel("0")
        summary_layout.addWidget(self.project_amount_label, 0, 1)
        
        summary_layout.addWidget(QLabel("المدفوع:"), 0, 2)
        self.project_paid_label = QLabel("0")
        summary_layout.addWidget(self.project_paid_label, 0, 3)
        
        summary_layout.addWidget(QLabel("الباقي:"), 0, 4)
        self.project_remaining_label = QLabel("0")
        summary_layout.addWidget(self.project_remaining_label, 0, 5)
        
        # بيانات العهد
        summary_layout.addWidget(QLabel("إجمالي العهد:"), 1, 0)
        self.total_custody_label = QLabel("0")
        summary_layout.addWidget(self.total_custody_label, 1, 1)
        
        summary_layout.addWidget(QLabel("مصروفات العهد:"), 1, 2)
        self.total_custody_expenses_label = QLabel("0")
        summary_layout.addWidget(self.total_custody_expenses_label, 1, 3)
        
        summary_layout.addWidget(QLabel("متبقي العهد:"), 1, 4)
        self.total_custody_remaining_label = QLabel("0")
        summary_layout.addWidget(self.total_custody_remaining_label, 1, 5)
        
        layout.addWidget(summary_group)
        
        # جدول التقرير الشامل
        self.comprehensive_table = QTableWidget()
        self.comprehensive_table.setAlternatingRowColors(True)
        layout.addWidget(self.comprehensive_table)
        
        return tab

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        buttons_layout = QHBoxLayout()
        
        # زر الطباعة
        print_btn = QPushButton("طباعة التقرير")
        print_btn.setIcon(QIcon("icons/print.png"))
        print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(print_btn)
        
        # زر التصدير
        export_btn = QPushButton("تصدير إلى Excel")
        export_btn.setIcon(QIcon("icons/export.png"))
        export_btn.clicked.connect(self.export_to_excel)
        buttons_layout.addWidget(export_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)

    def load_filter_data(self):
        """تحميل بيانات الفلاتر للوضع العام"""
        if not self.all_projects_mode:
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # تحميل التصنيفات
            cursor.execute("SELECT DISTINCT التصنيف FROM المشاريع WHERE التصنيف IS NOT NULL ORDER BY التصنيف")
            classifications = cursor.fetchall()
            for classification in classifications:
                self.global_classification_filter.addItem(classification[0], classification[0])

            # تحميل أسماء المشاريع
            cursor.execute("SELECT DISTINCT اسم_المشروع FROM المشاريع WHERE اسم_المشروع IS NOT NULL ORDER BY اسم_المشروع")
            projects = cursor.fetchall()
            for project in projects:
                self.global_project_filter.addItem(project[0], project[0])

            # تحميل أسماء العملاء
            cursor.execute("SELECT DISTINCT اسم_العميل FROM العملاء WHERE اسم_العميل IS NOT NULL ORDER BY اسم_العميل")
            clients = cursor.fetchall()
            for client in clients:
                self.global_client_filter.addItem(client[0], client[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الفلاتر:\n{str(e)}")

    def reset_global_filters(self):
        """إعادة تعيين الفلاتر العامة"""
        if not self.all_projects_mode:
            return

        self.global_classification_filter.setCurrentIndex(0)
        self.global_project_filter.setCurrentIndex(0)
        self.global_client_filter.setCurrentIndex(0)
        self.global_from_date.setDate(QDate.currentDate().addMonths(-12))
        self.global_to_date.setDate(QDate.currentDate())

        # إعادة تحميل البيانات
        self.load_all_data()

    def load_project_info(self):
        """تحميل معلومات المشروع"""
        if self.all_projects_mode:
            # في وضع جميع المشاريع، لا نحتاج لتحميل معلومات مشروع محدد
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.اسم_المشروع, c.اسم_العميل, p.المبلغ, p.المدفوع, p.الباقي, p.الحالة
                FROM المشاريع p
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                WHERE p.id = %s
            """, (self.project_id,))

            result = cursor.fetchone()
            if result:
                self.project_name, self.client_name, project_amount, project_paid, project_remaining, project_status = result

                info_text = f"المشروع: {self.project_name} | العميل: {self.client_name} | "
                info_text += f"المبلغ: {project_amount:,.0f} | المدفوع: {project_paid:,.0f} | "
                info_text += f"الباقي: {project_remaining:,.0f} | الحالة: {project_status}"

                self.project_info_label.setText(info_text)

                # تحديث الملخص المالي
                self.project_amount_label.setText(f"{project_amount:,.0f}")
                self.project_paid_label.setText(f"{project_paid:,.0f}")
                self.project_remaining_label.setText(f"{project_remaining:,.0f}")

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل معلومات المشروع:\n{str(e)}")

    def load_all_data(self):
        """تحميل جميع البيانات"""
        self.load_payments_data()
        self.load_custody_data()
        self.load_custody_expenses_data()
        self.load_comprehensive_data()

    def load_payments_data(self):
        """تحميل بيانات دفعات المشروع أو جميع المشاريع"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            if self.all_projects_mode:
                # تحميل دفعات جميع المشاريع مع الفلاتر
                from_date = self.global_from_date.date().toPyDate()
                to_date = self.global_to_date.date().toPyDate()

                # بناء الاستعلام مع الفلاتر
                query = """
                    SELECT dp.id, p.اسم_المشروع, c.اسم_العميل, dp.وصف_المدفوع,
                           dp.المبلغ_المدفوع, dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم, dp.المستخدم
                    FROM المشاريع_المدفوعات dp
                    JOIN المشاريع p ON dp.معرف_المشروع = p.id
                    LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                    WHERE dp.تاريخ_الدفع BETWEEN %s AND %s
                """
                params = [from_date, to_date]

                # إضافة فلاتر إضافية
                classification = self.global_classification_filter.currentData()
                if classification:
                    query += " AND p.التصنيف = %s"
                    params.append(classification)

                project_name = self.global_project_filter.currentData()
                if project_name:
                    query += " AND p.اسم_المشروع = %s"
                    params.append(project_name)

                client_name = self.global_client_filter.currentData()
                if client_name:
                    query += " AND c.اسم_العميل = %s"
                    params.append(client_name)

                query += " ORDER BY dp.تاريخ_الدفع DESC"

                cursor.execute(query, params)
                payments = cursor.fetchall()

                # إعداد الجدول للوضع العام
                headers = ["id", "اسم المشروع", "العميل", "وصف الدفعة", "المبلغ", "تاريخ الدفع", "طريقة الدفع", "المستلم", "المستخدم"]

            else:
                # تحميل دفعات المشروع المحدد
                from_date = self.payments_from_date.date().toPyDate()
                to_date = self.payments_to_date.date().toPyDate()

                cursor.execute("""
                    SELECT id, وصف_المدفوع, المبلغ_المدفوع, تاريخ_الدفع,
                           طريقة_الدفع, المستلم, المستخدم
                    FROM المشاريع_المدفوعات
                    WHERE معرف_المشروع = %s AND تاريخ_الدفع BETWEEN %s AND %s
                    ORDER BY تاريخ_الدفع DESC
                """, (self.project_id, from_date, to_date))

                payments = cursor.fetchall()

                # إعداد الجدول للمشروع المحدد
                headers = ["id", "وصف الدفعة", "المبلغ", "تاريخ الدفع", "طريقة الدفع", "المستلم", "المستخدم"]

            self.payments_table.setColumnCount(len(headers))
            self.payments_table.setHorizontalHeaderLabels(headers)
            self.payments_table.setRowCount(len(payments))

            total_amount = 0
            amount_col = 4 if self.all_projects_mode else 2  # عمود المبلغ

            for row, payment in enumerate(payments):
                for col, value in enumerate(payment):
                    if col == amount_col:  # عمود المبلغ
                        total_amount += float(value or 0)
                        item = QTableWidgetItem(f"{float(value or 0):,.0f}")
                    elif col == amount_col + 1:  # عمود التاريخ
                        item = QTableWidgetItem(str(value) if value else "")
                    else:
                        item = QTableWidgetItem(str(value) if value else "")

                    self.payments_table.setItem(row, col, item)

            # تحديث الإحصائيات
            self.payments_total_label.setText(f"إجمالي الدفعات: {total_amount:,.0f}")
            self.payments_count_label.setText(f"عدد الدفعات: {len(payments)}")

            # تنسيق الجدول
            self.payments_table.resizeColumnsToContents()
            self.payments_table.hideColumn(0)  # إخفاء عمود id

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الدفعات:\n{str(e)}")

    def load_custody_data(self):
        """تحميل بيانات العهد المالية"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            if self.all_projects_mode:
                # تحميل العهد المالية لجميع المشاريع مع الفلاتر
                query = """
                    SELECT em.id, em.رقم_العهدة, p.اسم_المشروع, c.اسم_العميل, em.مبلغ_العهدة,
                           em.نسبة_المكتب, em.مبلغ_نسبة_المكتب, em.المبلغ_الصافي, em.المصروف,
                           em.المتبقي, em.تاريخ_العهدة, em.حالة_العهدة, em.ملاحظات
                    FROM المقاولات_العهد em
                    JOIN المشاريع p ON em.معرف_المشروع = p.id
                    LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                    WHERE 1=1
                """
                params = []

                # إضافة فلاتر
                classification = self.global_classification_filter.currentData()
                if classification:
                    query += " AND p.التصنيف = %s"
                    params.append(classification)

                project_name = self.global_project_filter.currentData()
                if project_name:
                    query += " AND p.اسم_المشروع = %s"
                    params.append(project_name)

                client_name = self.global_client_filter.currentData()
                if client_name:
                    query += " AND c.اسم_العميل = %s"
                    params.append(client_name)

                query += " ORDER BY em.تاريخ_العهدة DESC"

                cursor.execute(query, params)
                custody_data = cursor.fetchall()

                # إعداد الجدول للوضع العام
                headers = ["id", "رقم العهدة", "اسم المشروع", "العميل", "مبلغ العهدة", "نسبة المكتب %",
                          "مبلغ المكتب", "المبلغ الصافي", "المصروف", "المتبقي", "تاريخ العهدة", "الحالة", "ملاحظات"]

            else:
                # تحميل العهد المالية للمشروع المحدد
                cursor.execute("""
                    SELECT id, رقم_العهدة, مبلغ_العهدة, نسبة_المكتب,
                           مبلغ_نسبة_المكتب, المبلغ_الصافي, المصروف,
                           المتبقي, تاريخ_العهدة, حالة_العهدة, ملاحظات
                    FROM المقاولات_العهد
                    WHERE معرف_المشروع = %s
                    ORDER BY تاريخ_العهدة DESC
                """, (self.project_id,))

                custody_data = cursor.fetchall()

                # إعداد الجدول للمشروع المحدد
                headers = ["id", "رقم العهدة", "مبلغ العهدة", "نسبة المكتب %",
                          "مبلغ المكتب", "المبلغ الصافي", "المصروف",
                          "المتبقي", "تاريخ العهدة", "الحالة", "ملاحظات"]

            # إعداد الجدول
            self.custody_table.setColumnCount(len(headers))
            self.custody_table.setHorizontalHeaderLabels(headers)
            self.custody_table.setRowCount(len(custody_data))

            total_custody = 0
            total_expenses = 0
            total_remaining = 0

            # تحديد مواقع الأعمدة حسب الوضع
            if self.all_projects_mode:
                amount_cols = [4, 6, 7, 8, 9]  # مبلغ العهدة، مبلغ المكتب، المبلغ الصافي، المصروف، المتبقي
                custody_col, office_col, net_col, expense_col, remaining_col = amount_cols
                percentage_col = 5  # نسبة المكتب
                date_col = 10  # تاريخ العهدة
            else:
                amount_cols = [2, 4, 5, 6, 7]  # مبلغ العهدة، مبلغ المكتب، المبلغ الصافي، المصروف، المتبقي
                custody_col, office_col, net_col, expense_col, remaining_col = amount_cols
                percentage_col = 3  # نسبة المكتب
                date_col = 8  # تاريخ العهدة

            for row, custody in enumerate(custody_data):
                for col, value in enumerate(custody):
                    if col in amount_cols:  # أعمدة المبالغ
                        amount = float(value or 0)
                        if col == custody_col:  # مبلغ العهدة
                            total_custody += amount
                        elif col == expense_col:  # المصروف
                            total_expenses += amount
                        elif col == remaining_col:  # المتبقي
                            total_remaining += amount
                        item = QTableWidgetItem(f"{amount:,.0f}")
                    elif col == percentage_col:  # نسبة المكتب
                        item = QTableWidgetItem(f"{float(value or 0):.1f}%")
                    elif col == date_col:  # تاريخ العهدة
                        item = QTableWidgetItem(str(value) if value else "")
                    else:
                        item = QTableWidgetItem(str(value) if value else "")

                    self.custody_table.setItem(row, col, item)

            # تحديث الإحصائيات
            self.custody_total_label.setText(f"إجمالي العهد: {total_custody:,.0f}")
            self.custody_expenses_total_label.setText(f"إجمالي المصروفات: {total_expenses:,.0f}")
            self.custody_remaining_label.setText(f"إجمالي المتبقي: {total_remaining:,.0f}")

            # تحديث الملخص الشامل
            self.total_custody_label.setText(f"{total_custody:,.0f}")
            self.total_custody_expenses_label.setText(f"{total_expenses:,.0f}")
            self.total_custody_remaining_label.setText(f"{total_remaining:,.0f}")

            # تنسيق الجدول
            self.custody_table.resizeColumnsToContents()
            self.custody_table.hideColumn(0)  # إخفاء عمود id

            # تحديث فلتر العهد في تبويب مصروفات العهد
            self.custody_filter.clear()
            self.custody_filter.addItem("جميع العهد", None)
            for custody in custody_data:
                self.custody_filter.addItem(f"{custody[1]} - {custody[2]:,.0f}", custody[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات العهد المالية:\n{str(e)}")

    def load_custody_expenses_data(self):
        """تحميل بيانات مصروفات العهد"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # بناء الاستعلام مع الفلاتر
            query = """
                SELECT me.id, me.رقم_العهدة, me.وصف_المصروف, me.المبلغ,
                       me.تاريخ_المصروف, me.المستلم, me.طريقة_الدفع,
                       me.رقم_الفاتورة, me.المورد, me.فئة_المصروف, me.ملاحظات
                FROM مصروفات_العهد me
                JOIN المقاولات_العهد em ON me.معرف_العهدة = em.id
                WHERE em.معرف_المشروع = %s
            """
            params = [self.project_id]

            # فلتر العهدة
            custody_id = self.custody_filter.currentData()
            if custody_id:
                query += " AND me.معرف_العهدة = %s"
                params.append(custody_id)

            # فلتر فئة المصروف
            expense_category = self.expense_category_filter.currentData()
            if expense_category:
                query += " AND me.فئة_المصروف = %s"
                params.append(expense_category)

            query += " ORDER BY me.تاريخ_المصروف DESC"

            cursor.execute(query, params)
            expenses = cursor.fetchall()

            # إعداد الجدول
            headers = ["id", "رقم العهدة", "وصف المصروف", "المبلغ", "تاريخ المصروف",
                      "المستلم", "طريقة الدفع", "رقم الفاتورة", "المورد", "فئة المصروف", "ملاحظات"]
            self.custody_expenses_table.setColumnCount(len(headers))
            self.custody_expenses_table.setHorizontalHeaderLabels(headers)
            self.custody_expenses_table.setRowCount(len(expenses))

            total_amount = 0
            expense_categories = set()

            for row, expense in enumerate(expenses):
                for col, value in enumerate(expense):
                    if col == 3:  # عمود المبلغ
                        amount = float(value or 0)
                        total_amount += amount
                        item = QTableWidgetItem(f"{amount:,.0f}")
                    elif col == 4:  # عمود التاريخ
                        item = QTableWidgetItem(str(value) if value else "")
                    else:
                        item = QTableWidgetItem(str(value) if value else "")

                    # جمع فئات المصروفات للفلتر
                    if col == 9 and value:  # فئة المصروف
                        expense_categories.add(value)

                    self.custody_expenses_table.setItem(row, col, item)

            # تحديث فلتر فئات المصروفات
            current_category = self.expense_category_filter.currentData()
            self.expense_category_filter.clear()
            self.expense_category_filter.addItem("جميع الفئات", None)
            for category in sorted(expense_categories):
                self.expense_category_filter.addItem(category, category)

            # استعادة الفلتر المحدد
            if current_category:
                index = self.expense_category_filter.findData(current_category)
                if index >= 0:
                    self.expense_category_filter.setCurrentIndex(index)

            # تحديث الإحصائيات
            self.custody_expenses_total_amount_label.setText(f"إجمالي المصروفات: {total_amount:,.0f}")
            self.custody_expenses_count_label.setText(f"عدد المصروفات: {len(expenses)}")

            # تنسيق الجدول
            self.custody_expenses_table.resizeColumnsToContents()
            self.custody_expenses_table.hideColumn(0)  # إخفاء عمود id

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات مصروفات العهد:\n{str(e)}")

    def load_comprehensive_data(self):
        """تحميل بيانات التقرير الشامل"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # جمع البيانات الشاملة
            comprehensive_data = []

            # إضافة دفعات المشروع
            cursor.execute("""
                SELECT 'دفعة مشروع' as النوع, وصف_المدفوع as الوصف,
                       المبلغ_المدفوع as المبلغ, تاريخ_الدفع as التاريخ,
                       طريقة_الدفع, المستلم, 'إيراد' as التصنيف
                FROM المشاريع_المدفوعات
                WHERE معرف_المشروع = %s
            """, (self.project_id,))

            payments = cursor.fetchall()
            comprehensive_data.extend(payments)

            # إضافة العهد المالية
            cursor.execute("""
                SELECT 'عهدة مالية' as النوع,
                       CONCAT('عهدة رقم ', رقم_العهدة) as الوصف,
                       مبلغ_العهدة as المبلغ, تاريخ_العهدة as التاريخ,
                       'عهدة' as طريقة_الدفع, '' as المستلم, 'عهدة' as التصنيف
                FROM المقاولات_العهد
                WHERE معرف_المشروع = %s
            """, (self.project_id,))

            custody = cursor.fetchall()
            comprehensive_data.extend(custody)

            # إضافة مصروفات العهد
            cursor.execute("""
                SELECT 'مصروف عهدة' as النوع, me.وصف_المصروف as الوصف,
                       me.المبلغ as المبلغ, me.تاريخ_المصروف as التاريخ,
                       me.طريقة_الدفع, me.المستلم, 'مصروف' as التصنيف
                FROM مصروفات_العهد me
                JOIN المقاولات_العهد em ON me.معرف_العهدة = em.id
                WHERE em.معرف_المشروع = %s
            """, (self.project_id,))

            expenses = cursor.fetchall()
            comprehensive_data.extend(expenses)

            # ترتيب البيانات حسب التاريخ
            comprehensive_data.sort(key=lambda x: x[3] if x[3] else date.min, reverse=True)

            # إعداد الجدول
            headers = ["النوع", "الوصف", "المبلغ", "التاريخ", "طريقة الدفع", "المستلم", "التصنيف"]
            self.comprehensive_table.setColumnCount(len(headers))
            self.comprehensive_table.setHorizontalHeaderLabels(headers)
            self.comprehensive_table.setRowCount(len(comprehensive_data))

            for row, data in enumerate(comprehensive_data):
                for col, value in enumerate(data):
                    if col == 2:  # عمود المبلغ
                        amount = float(value or 0)
                        item = QTableWidgetItem(f"{amount:,.0f}")

                        # تلوين حسب النوع
                        if data[6] == 'إيراد':  # إيراد
                            item.setForeground(QColor("#d4edda"))  # أخضر فاتح
                        elif data[6] == 'عهدة':  # عهدة
                            item.setForeground(QColor("#d1ecf1"))  # أزرق فاتح
                        elif data[6] == 'مصروف':  # مصروف
                            item.setForeground(QColor("#f8d7da"))  # أحمر فاتح
                    elif col == 3:  # عمود التاريخ
                        item = QTableWidgetItem(str(value) if value else "")
                    else:
                        item = QTableWidgetItem(str(value) if value else "")

                    self.comprehensive_table.setItem(row, col, item)

            # تنسيق الجدول
            self.comprehensive_table.resizeColumnsToContents()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الشاملة:\n{str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            current_tab = self.tab_widget.currentIndex()

            if current_tab == 0:  # تبويب الدفعات
                self.print_payments_report()
            elif current_tab == 1:  # تبويب العهد
                self.print_custody_report()
            elif current_tab == 2:  # تبويب مصروفات العهد
                self.print_custody_expenses_report()
            elif current_tab == 3:  # تبويب التقرير الشامل
                self.print_comprehensive_report()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير:\n{str(e)}")

    def print_payments_report(self):
        """طباعة تقرير الدفعات"""
        print_project_financial_report(
            self,
            table=self.payments_table,
            title=f"تقرير دفعات المشروع - {self.project_name}",
            project_info=self.project_info_label.text(),
            summary_data={
                "total": self.payments_total_label.text(),
                "count": self.payments_count_label.text()
            }
        )

    def print_custody_report(self):
        """طباعة تقرير العهد المالية"""
        print_project_financial_report(
            self,
            table=self.custody_table,
            title=f"تقرير العهد المالية - {self.project_name}",
            project_info=self.project_info_label.text(),
            summary_data={
                "total_custody": self.custody_total_label.text(),
                "total_expenses": self.custody_expenses_total_label.text(),
                "total_remaining": self.custody_remaining_label.text()
            }
        )

    def print_custody_expenses_report(self):
        """طباعة تقرير مصروفات العهد"""
        print_project_financial_report(
            self,
            table=self.custody_expenses_table,
            title=f"تقرير مصروفات العهد - {self.project_name}",
            project_info=self.project_info_label.text(),
            summary_data={
                "total": self.custody_expenses_total_amount_label.text(),
                "count": self.custody_expenses_count_label.text()
            }
        )

    def print_comprehensive_report(self):
        """طباعة التقرير الشامل"""
        print_project_financial_report(
            self,
            table=self.comprehensive_table,
            title=f"التقرير المالي الشامل - {self.project_name}",
            project_info=self.project_info_label.text(),
            summary_data={
                "project_amount": self.project_amount_label.text(),
                "project_paid": self.project_paid_label.text(),
                "project_remaining": self.project_remaining_label.text(),
                "total_custody": self.total_custody_label.text(),
                "total_custody_expenses": self.total_custody_expenses_label.text(),
                "total_custody_remaining": self.total_custody_remaining_label.text()
            }
        )

    def export_to_excel(self):
        """تصدير إلى Excel"""
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill

            # إنشاء ملف Excel جديد
            wb = Workbook()

            # حذف الورقة الافتراضية
            wb.remove(wb.active)

            # إضافة ورقة لكل تبويب
            self.export_payments_to_excel(wb)
            self.export_custody_to_excel(wb)
            self.export_custody_expenses_to_excel(wb)
            self.export_comprehensive_to_excel(wb)

            # حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير",
                f"تقرير_مالي_{self.project_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                wb.save(file_path)
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except ImportError:
            QMessageBox.warning(self, "تحذير", "مكتبة openpyxl غير مثبتة. يرجى تثبيتها لتصدير ملفات Excel.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def export_payments_to_excel(self, workbook):
        """تصدير دفعات المشروع إلى Excel"""
        ws = workbook.create_sheet("دفعات المشروع")

        # إضافة العناوين
        headers = []
        for col in range(self.payments_table.columnCount()):
            if not self.payments_table.isColumnHidden(col):
                headers.append(self.payments_table.horizontalHeaderItem(col).text())

        ws.append(headers)

        # إضافة البيانات
        for row in range(self.payments_table.rowCount()):
            row_data = []
            for col in range(self.payments_table.columnCount()):
                if not self.payments_table.isColumnHidden(col):
                    item = self.payments_table.item(row, col)
                    row_data.append(item.text() if item else "")
            ws.append(row_data)

    def export_custody_to_excel(self, workbook):
        """تصدير العهد المالية إلى Excel"""
        ws = workbook.create_sheet("العهد المالية")

        # إضافة العناوين
        headers = []
        for col in range(self.custody_table.columnCount()):
            if not self.custody_table.isColumnHidden(col):
                headers.append(self.custody_table.horizontalHeaderItem(col).text())

        ws.append(headers)

        # إضافة البيانات
        for row in range(self.custody_table.rowCount()):
            row_data = []
            for col in range(self.custody_table.columnCount()):
                if not self.custody_table.isColumnHidden(col):
                    item = self.custody_table.item(row, col)
                    row_data.append(item.text() if item else "")
            ws.append(row_data)

    def export_custody_expenses_to_excel(self, workbook):
        """تصدير مصروفات العهد إلى Excel"""
        ws = workbook.create_sheet("مصروفات العهد")

        # إضافة العناوين
        headers = []
        for col in range(self.custody_expenses_table.columnCount()):
            if not self.custody_expenses_table.isColumnHidden(col):
                headers.append(self.custody_expenses_table.horizontalHeaderItem(col).text())

        ws.append(headers)

        # إضافة البيانات
        for row in range(self.custody_expenses_table.rowCount()):
            row_data = []
            for col in range(self.custody_expenses_table.columnCount()):
                if not self.custody_expenses_table.isColumnHidden(col):
                    item = self.custody_expenses_table.item(row, col)
                    row_data.append(item.text() if item else "")
            ws.append(row_data)

    def export_comprehensive_to_excel(self, workbook):
        """تصدير التقرير الشامل إلى Excel"""
        ws = workbook.create_sheet("التقرير الشامل")

        # إضافة العناوين
        headers = []
        for col in range(self.comprehensive_table.columnCount()):
            headers.append(self.comprehensive_table.horizontalHeaderItem(col).text())

        ws.append(headers)

        # إضافة البيانات
        for row in range(self.comprehensive_table.rowCount()):
            row_data = []
            for col in range(self.comprehensive_table.columnCount()):
                item = self.comprehensive_table.item(row, col)
                row_data.append(item.text() if item else "")
            ws.append(row_data)
