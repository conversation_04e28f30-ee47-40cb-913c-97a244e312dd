#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لإصلاح مشكلة عمود اسم_العميل في دفعات المشروع
"""

import mysql.connector
from متغيرات import host, user, password
from datetime import datetime

def test_complete_payment_workflow():
    """اختبار شامل لسير عمل الدفعات"""
    print("🧪 اختبار شامل لسير عمل الدفعات...")
    
    try:
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # 1. اختبار وجود الجداول المطلوبة
        print("\n📋 التحقق من وجود الجداول:")
        tables_to_check = ['المشاريع', 'المشاريع_المدفوعات', 'العملاء']
        for table in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                print(f"✅ جدول {table} موجود")
            else:
                print(f"❌ جدول {table} غير موجود")
                return False
        
        # 2. اختبار استعلامات التقارير المُصلحة
        print("\n📋 اختبار استعلامات التقارير:")
        
        # الاستعلام الأول
        cursor.execute("""
            SELECT dp.id, c.اسم_العميل, p.اسم_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع, dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم 
            FROM المشاريع_المدفوعات dp
            LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
            LEFT JOIN العملاء c ON dp.معرف_العميل = c.id
        """)
        results1 = cursor.fetchall()
        print(f"✅ استعلام التقارير الأول نجح: {len(results1)} سجل")
        
        # الاستعلام الثاني
        cursor.execute("""
            SELECT dp.id, c.اسم_العميل, p.اسم_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع,
                   dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم, p.التصنيف
            FROM المشاريع_المدفوعات dp
            LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
            LEFT JOIN العملاء c ON dp.معرف_العميل = c.id
        """)
        results2 = cursor.fetchall()
        print(f"✅ استعلام التقارير الثاني نجح: {len(results2)} سجل")
        
        # 3. اختبار جلب اسم العميل من معرف العميل
        print("\n📋 اختبار جلب أسماء العملاء:")
        cursor.execute("SELECT id FROM العملاء LIMIT 1")
        client_result = cursor.fetchone()
        
        if client_result:
            client_id = client_result[0]
            cursor.execute("SELECT c.اسم_العميل FROM العملاء c WHERE c.id = %s", (client_id,))
            name_result = cursor.fetchone()
            if name_result:
                print(f"✅ جلب اسم العميل نجح: {name_result[0]}")
            else:
                print("❌ فشل في جلب اسم العميل")
                return False
        
        # 4. اختبار إدراج دفعة تجريبية
        print("\n📋 اختبار إدراج دفعة تجريبية:")
        cursor.execute("SELECT id, معرف_العميل FROM المشاريع LIMIT 1")
        project_result = cursor.fetchone()
        
        if project_result:
            project_id, client_id = project_result
            
            # إدراج دفعة تجريبية
            test_payment_data = (
                client_id,
                project_id,
                100.0,
                "دفعة اختبار شاملة",
                datetime.now().strftime("%Y-%m-%d"),
                "دفع نقدًا",
                "اختبار النظام"
            )
            
            cursor.execute("""
                INSERT INTO المشاريع_المدفوعات 
                (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, المستلم)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, test_payment_data)
            
            test_payment_id = cursor.lastrowid
            print(f"✅ تم إدراج دفعة تجريبية بنجاح: ID {test_payment_id}")
            
            # 5. اختبار استعلام الدفعة المدرجة مع أسماء العملاء
            print("\n📋 اختبار استعلام الدفعة مع أسماء العملاء:")
            cursor.execute("""
                SELECT dp.id, c.اسم_العميل, p.اسم_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع
                FROM المشاريع_المدفوعات dp
                LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
                LEFT JOIN العملاء c ON dp.معرف_العميل = c.id
                WHERE dp.id = %s
            """, (test_payment_id,))
            
            payment_with_names = cursor.fetchone()
            if payment_with_names:
                print(f"✅ استعلام الدفعة مع الأسماء نجح:")
                print(f"   ID: {payment_with_names[0]}")
                print(f"   العميل: {payment_with_names[1]}")
                print(f"   المشروع: {payment_with_names[2]}")
                print(f"   الوصف: {payment_with_names[3]}")
                print(f"   المبلغ: {payment_with_names[4]}")
            else:
                print("❌ فشل في استعلام الدفعة مع الأسماء")
                return False
            
            # 6. حذف الدفعة التجريبية
            cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (test_payment_id,))
            print("✅ تم حذف الدفعة التجريبية بنجاح")
            
            conn.commit()
        else:
            print("⚠️ لا توجد مشاريع للاختبار عليها")
        
        # 7. اختبار تحديث المشروع بعد الدفعة (التحقق من عمل الـ triggers)
        print("\n📋 اختبار تحديث المشروع:")
        cursor.execute("SELECT id, المبلغ, المدفوع, الباقي FROM المشاريع LIMIT 1")
        project_status = cursor.fetchone()
        if project_status:
            print(f"✅ حالة المشروع: المبلغ={project_status[1]}, المدفوع={project_status[2]}, الباقي={project_status[3]}")
        
        conn.close()
        print("\n✅ تم إغلاق الاتصال بنجاح")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        print(f"Error Code: {err.errno}")
        if hasattr(err, 'sqlstate'):
            print(f"SQL State: {err.sqlstate}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("اختبار شامل لإصلاح مشكلة عمود اسم_العميل في دفعات المشروع")
    print("=" * 70)
    
    success = test_complete_payment_workflow()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 جميع الاختبارات نجحت بامتياز!")
        print("✅ تم إصلاح مشكلة MySQL column reference error بنجاح")
        print("✅ استعلامات التقارير تعمل بشكل صحيح مع JOIN")
        print("✅ جلب أسماء العملاء يعمل بشكل صحيح")
        print("✅ إدراج وحذف الدفعات يعمل بشكل صحيح")
        print("✅ الـ triggers تعمل بشكل صحيح")
        print("\n🚀 دفعات المشروع جاهزة للاستخدام!")
    else:
        print("❌ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")
    print("=" * 70)
