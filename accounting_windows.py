#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نوافذ المحاسبة: شجرة الحسابات والعقود
"""

import sys
import os
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QMessageBox, QWidget
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QFont

# متغيرات عامة لتخزين النوافذ
account_tree_window = None
contracts_window = None

# استيراد شجرة الحسابات
try:
    from شجرة_الحسابات import AccountTreeApp
except ImportError:
    AccountTreeApp = None

# دالة لفتح نافذة شجرة الحسابات
def open_account_tree_window(parent=None):
    """
    فتح نافذة شجرة الحسابات
    """
    if AccountTreeApp is None:
        QMessageBox.critical(parent, "خطأ", "تعذر تحميل وحدة شجرة الحسابات. تأكد من وجود الملف شجرة_الحسابات.py")
        return

    try:
        # إنشاء نسخة من النافذة كمتغير عام لمنع حذفها بواسطة جامع النفايات
        global account_tree_window
        account_tree_window = AccountTreeApp()

        # جعل النافذة مستقلة بدلاً من مودال
        account_tree_window.setWindowModality(Qt.NonModal)

        # إظهار النافذة
        account_tree_window.show()
        account_tree_window.raise_()  # رفع النافذة إلى المقدمة

        return account_tree_window
    except Exception as e:
        import traceback
        traceback.print_exc()
        QMessageBox.critical(parent, "خطأ", f"حدث خطأ أثناء فتح نافذة شجرة الحسابات: {str(e)}")
        return None

# فئة نافذة العقود
class ContractsWindow(QMainWindow):
    """
    نافذة العقود
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("نظام العقود")
        self.setGeometry(100, 100, 1000, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # إعداد واجهة المستخدم
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # عنوان النافذة
        title_label = QLabel("نظام العقود")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24pt; font-weight: bold; margin: 20px;")
        layout.addWidget(title_label)

        # رسالة قيد التطوير
        under_development_label = QLabel("هذه الميزة قيد التطوير")
        under_development_label.setAlignment(Qt.AlignCenter)
        under_development_label.setStyleSheet("font-size: 18pt; color: #888; margin: 40px;")
        layout.addWidget(under_development_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        close_button = QPushButton("إغلاق")
        close_button.setMinimumSize(120, 40)
        close_button.clicked.connect(self.close)
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)
        layout.addStretch()

# دالة لفتح نافذة العقود
def open_contracts_window(parent=None):
    """
    فتح نافذة العقود
    """
    try:
        # إنشاء نسخة من النافذة كمتغير عام لمنع حذفها بواسطة جامع النفايات
        global contracts_window
        contracts_window = ContractsWindow(parent)

        # جعل النافذة مستقلة بدلاً من مودال
        contracts_window.setWindowModality(Qt.NonModal)

        # إظهار النافذة
        contracts_window.show()
        contracts_window.raise_()  # رفع النافذة إلى المقدمة

        return contracts_window
    except Exception as e:
        import traceback
        traceback.print_exc()
        QMessageBox.critical(parent, "خطأ", f"حدث خطأ أثناء فتح نافذة العقود: {str(e)}")
        return None

# للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # اختبار نافذة شجرة الحسابات
    # account_tree_window = open_account_tree_window()

    # اختبار نافذة العقود
    contracts_window = open_contracts_window()

    sys.exit(app.exec_())
