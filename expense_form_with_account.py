#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج إضافة المصروفات مع ربط بشجرة الحسابات
"""

import sys
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit, QPushButton, QMessageBox
)
from PySide6.QtCore import Qt, QDate

# استيراد مكون اختيار الحساب
from account_selector import AccountSelector

# استيراد دوال ربط الحسابات
from account_linking import link_expense_to_account

# استيراد دوال قاعدة البيانات
from db import get_db_connection

class ExpenseFormWithAccount(QDialog):
    """
    نموذج إضافة المصروفات مع ربط بشجرة الحسابات
    """
    
    def __init__(self, parent=None, expense_id=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة مصروف جديد")
        self.setGeometry(300, 300, 500, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        
        self.expense_id = expense_id
        self.conn, self.cursor = get_db_connection()
        
        self.setup_ui()
        
        if expense_id:
            self.load_expense_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان النموذج
        title_label = QLabel("إضافة مصروف جديد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # الكود
        self.code_edit = QLineEdit()
        form_layout.addRow("الكود:", self.code_edit)
        
        # المصروف
        self.expense_edit = QLineEdit()
        form_layout.addRow("المصروف:", self.expense_edit)
        
        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 1000000000)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSingleStep(100)
        form_layout.addRow("المبلغ:", self.amount_spin)
        
        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("التاريخ:", self.date_edit)
        
        # المستلم
        self.recipient_edit = QLineEdit()
        form_layout.addRow("المستلم:", self.recipient_edit)
        
        # رقم الهاتف
        self.phone_edit = QLineEdit()
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        # رقم الفاتورة
        self.invoice_edit = QLineEdit()
        form_layout.addRow("رقم الفاتورة:", self.invoice_edit)
        
        # الحساب في شجرة الحسابات
        self.account_selector = AccountSelector(self, self.cursor)
        form_layout.addRow("الحساب:", self.account_selector)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_expense)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_expense_data(self):
        """تحميل بيانات المصروف للتعديل"""
        try:
            self.cursor.execute("""
                SELECT الكود, المصروف, المبلغ, التاريخ, المستلم, رقم_الهاتف, رقم_الفاتورة, ملاحظات, كود_الحساب_الشجرة
                FROM الحسابات
                WHERE id = %s
            """, (self.expense_id,))
            
            expense = self.cursor.fetchone()
            if expense:
                code, expense_name, amount, date_val, recipient, phone, invoice, notes, account_code = expense
                
                self.code_edit.setText(code)
                self.expense_edit.setText(expense_name)
                self.amount_spin.setValue(float(amount) if amount else 0)
                self.date_edit.setDate(QDate.fromString(date_val.strftime("%Y-%m-%d"), "yyyy-MM-dd") if date_val else QDate.currentDate())
                self.recipient_edit.setText(recipient or "")
                self.phone_edit.setText(phone or "")
                self.invoice_edit.setText(invoice or "")
                self.notes_edit.setText(notes or "")
                
                if account_code:
                    self.account_selector.set_selected_account(account_code)
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات المصروف: {str(e)}")
    
    def save_expense(self):
        """حفظ بيانات المصروف"""
        code = self.code_edit.text().strip()
        expense_name = self.expense_edit.text().strip()
        amount = self.amount_spin.value()
        date_val = self.date_edit.date().toString("yyyy-MM-dd")
        recipient = self.recipient_edit.text().strip()
        phone = self.phone_edit.text().strip()
        invoice = self.invoice_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()
        
        account_code, _ = self.account_selector.get_selected_account()
        
        # التحقق من صحة البيانات
        if not code:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال الكود")
            return
        
        if not expense_name:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال اسم المصروف")
            return
        
        if amount <= 0:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال مبلغ أكبر من صفر")
            return
        
        try:
            year = datetime.now().year
            username = "النظام"  # يمكن تغييره لاستخدام اسم المستخدم الحالي
            
            if self.expense_id:  # تعديل مصروف موجود
                self.cursor.execute("""
                    UPDATE الحسابات
                    SET الكود = %s, المصروف = %s, المبلغ = %s, التاريخ = %s,
                        المستلم = %s, رقم_الهاتف = %s, رقم_الفاتورة = %s, ملاحظات = %s,
                        كود_الحساب_الشجرة = %s, المستخدم = %s, السنة = %s
                    WHERE id = %s
                """, (
                    code, expense_name, amount, date_val, recipient, phone, invoice, notes,
                    account_code, username, year, self.expense_id
                ))
            else:  # إضافة مصروف جديد
                self.cursor.execute("""
                    INSERT INTO الحسابات
                    (الكود, المصروف, المبلغ, التاريخ, المستلم, رقم_الهاتف, رقم_الفاتورة, ملاحظات,
                     كود_الحساب_الشجرة, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    code, expense_name, amount, date_val, recipient, phone, invoice, notes,
                    account_code, username, year
                ))
                
                # الحصول على id المصروف الجديد
                self.expense_id = self.cursor.lastrowid
            
            # ربط المصروف بالحساب في شجرة الحسابات
            if account_code:
                link_expense_to_account(self.cursor, self.expense_id, account_code, username)

            self.conn.commit()

            # ===== الربط المحاسبي =====
            try:
                # إنشاء نظام الربط المحاسبي
                from accounting_integration import AccountingIntegration
                accounting = AccountingIntegration()

                # إعداد بيانات المصروف للنظام المحاسبي
                expense_data = {
                    'id': self.expense_id,
                    'التصنيف': expense_name,  # استخدام اسم المصروف كتصنيف
                    'المصروف': expense_name,
                    'المبلغ': amount,
                    'تاريخ_المصروف': date_val,
                    'المستلم': recipient,
                    'رقم_الفاتورة': invoice,
                    'ملاحظات': notes,
                    'المستخدم': username
                }

                # تسجيل المصروف محاسبياً
                success, message = accounting.record_expense(expense_data)

                if success:
                    print(f"تم تسجيل المصروف محاسبياً: {message}")
                else:
                    print(f"خطأ في التسجيل المحاسبي: {message}")
                    # يمكن إضافة تحذير للمستخدم هنا إذا أردت

                accounting.close_connection()

            except Exception as e:
                print(f"خطأ في الربط المحاسبي: {e}")
                # الاستمرار حتى لو فشل الربط المحاسبي
            # ===== نهاية الربط المحاسبي =====

            QMessageBox.information(self, "نجاح", "تم حفظ المصروف بنجاح")
            self.accept()
            
        except Exception as e:
            self.conn.rollback()
            QMessageBox.critical(self, "خطأ في حفظ البيانات", f"حدث خطأ أثناء حفظ بيانات المصروف: {str(e)}")
    
    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        if self.conn:
            self.cursor.close()
            self.conn.close()
        event.accept()


# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ExpenseFormWithAccount()
    window.show()
    sys.exit(app.exec_())
