#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
التحقق من تحسينات نظام إدارة المشاريع
Verification script for project management enhancements
"""

import sys
import os

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def verify_tools_py_changes():
    """التحقق من التغييرات في ملف tools.py"""
    print("🔍 التحقق من التغييرات في tools.py...")
    
    try:
        with open('tools.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود دالة تحميل المهندسين
        if 'def load_engineers_to_combo(self):' in content:
            print("   ✅ دالة تحميل المهندسين موجودة")
        else:
            print("   ❌ دالة تحميل المهندسين غير موجودة")
        
        # التحقق من وجود حقل المهندس المسؤول في المشاريع
        if 'responsible_engineer' in content and 'المهندس المسؤول' in content:
            print("   ✅ حقل المهندس المسؤول موجود")
        else:
            print("   ❌ حقل المهندس المسؤول غير موجود")
        
        # التحقق من وجود قسم المقاولات
        if 'elif self.section_name == "المقاولات":' in content:
            print("   ✅ قسم المقاولات موجود في النماذج")
        else:
            print("   ❌ قسم المقاولات غير موجود في النماذج")
        
        # التحقق من إزالة الحقول غير المرغوب فيها
        projects_section_start = content.find('if self.section_name == "المشاريع":')
        projects_section_end = content.find('elif self.section_name == "المقاولات":', projects_section_start)
        if projects_section_end == -1:
            projects_section_end = content.find('elif self.section_name == "العملاء":', projects_section_start)
        
        if projects_section_start != -1 and projects_section_end != -1:
            projects_section = content[projects_section_start:projects_section_end]
            
            if 'client_code' not in projects_section and 'نوع العميل' not in projects_section:
                print("   ✅ حقل نوع العميل تم إزالته من المشاريع")
            else:
                print("   ❌ حقل نوع العميل ما زال موجود في المشاريع")
            
            if 'address' not in projects_section and 'العنوان' not in projects_section:
                print("   ✅ حقل العنوان تم إزالته من المشاريع")
            else:
                print("   ❌ حقل العنوان ما زال موجود في المشاريع")
        
        # التحقق من تحديث قاعدة البيانات
        if 'معرف_المهندس' in content:
            print("   ✅ حقل المهندس المسؤول موجود في تطابق قاعدة البيانات")
        else:
            print("   ❌ حقل المهندس المسؤول غير موجود في تطابق قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قراءة ملف tools.py: {e}")
        return False

def verify_main_app_changes():
    """التحقق من التغييرات في الملف الرئيسي"""
    print("\n🔍 التحقق من التغييرات في منظومة_المهندس.py...")
    
    try:
        with open('منظومة_المهندس.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود معالج المقاولات
        if 'def handle_contracting_action(self, action, section, year, table, selected_row_data):' in content:
            print("   ✅ معالج إجراءات المقاولات موجود")
        else:
            print("   ❌ معالج إجراءات المقاولات غير موجود")
        
        # التحقق من دعم المقاولات في الفلاتر
        if 'section_name in ["المشاريع", "المقاولات"]' in content:
            print("   ✅ دعم المقاولات في نظام الفلاتر موجود")
        else:
            print("   ❌ دعم المقاولات في نظام الفلاتر غير موجود")
        
        # التحقق من دعم المقاولات في الإحصائيات
        if 'elif section_name == "المقاولات":' in content:
            print("   ✅ دعم المقاولات في الإحصائيات موجود")
        else:
            print("   ❌ دعم المقاولات في الإحصائيات غير موجود")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قراءة ملف منظومة_المهندس.py: {e}")
        return False

def verify_cards_system_changes():
    """التحقق من التغييرات في نظام البطاقات"""
    print("\n🔍 التحقق من التغييرات في نظام_البطاقات.py...")
    
    try:
        with open('نظام_البطاقات.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود دوال الفلترة الموحدة
        if 'def sync_filters_from_main(self' in content:
            print("   ✅ دالة مزامنة الفلاتر موجودة")
        else:
            print("   ❌ دالة مزامنة الفلاتر غير موجودة")
        
        if 'def apply_unified_filters(self' in content:
            print("   ✅ دالة الفلاتر الموحدة موجودة")
        else:
            print("   ❌ دالة الفلاتر الموحدة غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قراءة ملف نظام_البطاقات.py: {e}")
        return False

def check_file_syntax():
    """التحقق من صحة بناء الجملة في الملفات"""
    print("\n🔍 التحقق من صحة بناء الجملة...")
    
    files_to_check = ['tools.py', 'منظومة_المهندس.py', 'نظام_البطاقات.py']
    
    for file_name in files_to_check:
        try:
            with open(file_name, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # محاولة تجميع الملف للتحقق من صحة بناء الجملة
            compile(content, file_name, 'exec')
            print(f"   ✅ بناء الجملة صحيح في {file_name}")
            
        except SyntaxError as e:
            print(f"   ❌ خطأ في بناء الجملة في {file_name}: {e}")
            return False
        except Exception as e:
            print(f"   ⚠️ تحذير في {file_name}: {e}")
    
    return True

def main():
    """الدالة الرئيسية للتحقق"""
    print("🔧 التحقق من تحسينات نظام إدارة المشاريع")
    print("=" * 60)
    
    checks = [
        ("ملف tools.py", verify_tools_py_changes),
        ("الملف الرئيسي", verify_main_app_changes),
        ("نظام البطاقات", verify_cards_system_changes),
        ("صحة بناء الجملة", check_file_syntax)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n🔍 فحص: {check_name}")
        try:
            if check_func():
                passed_checks += 1
                print(f"✅ نجح فحص: {check_name}")
            else:
                print(f"❌ فشل فحص: {check_name}")
        except Exception as e:
            print(f"💥 خطأ في فحص {check_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الفحص: {passed_checks}/{total_checks} نجح")
    
    if passed_checks == total_checks:
        print("🎉 جميع الفحوصات نجحت!")
        print("\n📋 ملخص التحسينات المطبقة:")
        print("   ✅ إضافة حقل المهندس المسؤول للمشاريع والمقاولات")
        print("   ✅ إزالة حقول نوع العميل والعنوان من النماذج")
        print("   ✅ تطبيق عمليات CRUD كاملة للمقاولات")
        print("   ✅ تحسين نظام الفلترة الموحد")
        print("   ✅ تحديث تطابق قاعدة البيانات")
        return True
    else:
        print(f"⚠️ فشل {total_checks - passed_checks} فحص(وصات)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
