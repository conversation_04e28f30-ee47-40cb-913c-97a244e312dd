import sys
import os
from PySide6.QtWidgets import (
    QA<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFileDialog,
    QDateEdit, QMessageBox, QFormLayout, QGroupBox,
    QSplitter, QFrame, QComboBox, QColorDialog, QScrollArea,
    QRadioButton
)
from PySide6.QtGui import (
    QPainter, QPixmap, QFont, QColor, QDesktopServices,
    QPageLayout, QPageSize, QPen, QLinearGradient, QBrush,
    QRadialGradient, QImage
)
from PySide6.QtCore import Qt, QDate, QUrl, QRectF, QMarginsF, QSize
from PySide6.QtPrintSupport import QPrinter, QPrintPreviewDialog


class CertificatePreview(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(600, 400)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("border: 1px solid #cccccc; background-color: #f9f9f9;")
        self.setText("معاينة الشهادة ستظهر هنا")
        self.setScaledContents(True)

    def update_preview(self, student_name, course_name, instructor_name, date, logo_path=None, company_name=None, is_landscape=True, intro_text=None):
        # Create a blank image with A4 proportions (scaled down)
        preview_width = self.width()

        # Use default intro text if none provided
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"

        if is_landscape:
            # A4 landscape ratio is 1.4142 (width/height)
            preview_height = int(preview_width / 1.4142)
        else:
            # A4 portrait ratio is 0.7071 (width/height)
            preview_height = int(preview_width / 0.7071)
            # Swap dimensions for portrait
            preview_width, preview_height = preview_height, preview_width

        # Create a QPixmap to draw on
        pixmap = QPixmap(preview_width, preview_height)
        pixmap.fill(Qt.GlobalColor.white)

        # Create painter
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Background with gradient
        background_gradient = QLinearGradient(0, 0, 0, preview_height)
        background_gradient.setColorAt(0, QColor(255, 255, 255))  # White at top
        background_gradient.setColorAt(1, QColor(240, 240, 255))  # Light blue-ish at bottom
        painter.fillRect(0, 0, preview_width, preview_height, QBrush(background_gradient))

        # Draw border
        painter.setPen(QPen(QColor("#003366"), 5))
        border_rect = QRectF(10, 10, preview_width - 20, preview_height - 20)
        painter.drawRoundedRect(border_rect, 15.0, 15.0)

        # Draw logo if available
        if logo_path and os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            if not logo_pixmap.isNull():
                logo_max_width = int(preview_width * 0.15)
                logo_max_height = int(preview_height * 0.15)
                scaled_logo = logo_pixmap.scaled(logo_max_width, logo_max_height,
                                               Qt.AspectRatioMode.KeepAspectRatio,
                                               Qt.TransformationMode.SmoothTransformation)
                painter.drawPixmap(20, 20, scaled_logo)

        # Draw company name if available (new)
        if company_name:
            company_font = QFont("Arial", int(preview_width * 0.025))
            company_font.setBold(True)
            painter.setFont(company_font)
            painter.setPen(QColor("#005A9C"))
            company_rect = QRectF(0, preview_height * 0.08, preview_width, preview_height * 0.08)
            painter.drawText(company_rect, Qt.AlignmentFlag.AlignCenter, company_name)

        # Draw title
        title_font = QFont("Arial", int(preview_width * 0.05))
        title_font.setBold(True)  # Set bold style
        painter.setFont(title_font)
        painter.setPen(QColor("#005A9C"))
        title_rect = QRectF(0, preview_height * 0.18, preview_width, preview_height * 0.1)
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, "شهادة مشاركة")

        # Draw intro text
        intro_font = QFont("Arial", int(preview_width * 0.025))
        painter.setFont(intro_font)
        painter.setPen(QColor("#333333"))
        intro_rect = QRectF(0, preview_height * 0.30, preview_width, preview_height * 0.08)
        painter.drawText(intro_rect, Qt.AlignmentFlag.AlignCenter, intro_text)

        # Draw student name
        name_font = QFont("Arial", int(preview_width * 0.035))
        name_font.setBold(True)  # Set bold style
        painter.setFont(name_font)
        painter.setPen(QColor("#003366"))
        name_rect = QRectF(0, preview_height * 0.38, preview_width, preview_height * 0.1)
        painter.drawText(name_rect, Qt.AlignmentFlag.AlignCenter, student_name if student_name else "اسم الطالب")

        # Draw course name
        course_font = QFont("Arial", int(preview_width * 0.03))
        painter.setFont(course_font)
        painter.setPen(QColor("#D2691E"))
        course_rect = QRectF(0, preview_height * 0.5, preview_width, preview_height * 0.1)
        painter.drawText(course_rect, Qt.AlignmentFlag.AlignCenter, course_name if course_name else "اسم الكورس")

        # Draw instructor name
        painter.setPen(QColor("#333333"))
        instructor_rect = QRectF(0, preview_height * 0.6, preview_width, preview_height * 0.1)
        painter.drawText(instructor_rect, Qt.AlignmentFlag.AlignCenter,
                        f"المدرب: {instructor_name}" if instructor_name else "اسم المدرب")

        # Draw date
        date_rect = QRectF(0, preview_height * 0.7, preview_width, preview_height * 0.1)
        date_str = date.toString("dd/MM/yyyy") if date else QDate.currentDate().toString("dd/MM/yyyy")
        painter.drawText(date_rect, Qt.AlignmentFlag.AlignCenter, f"التاريخ: {date_str}")

        # End painting
        painter.end()

        # Set the pixmap to the label
        self.setPixmap(pixmap)


class CertificateApp(QWidget):
    def __init__(self):
        super().__init__()
        self.logo_path = None
        self.signature_path = None  # New: path to signature image
        self.is_landscape = True  # Default to landscape orientation
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("مولد شهادات المشاركة")
        self.setGeometry(100, 100, 1000, 700)  # Larger window
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)  # RTL layout for Arabic

        # Main layout
        main_layout = QHBoxLayout()

        # Left side - Form inputs
        form_group = QGroupBox("بيانات الشهادة")
        form_layout = QVBoxLayout()

        # Input fields with styling
        input_style = "QLineEdit { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }"
        label_style = "QLabel { font-weight: bold; }"

        # Student name
        student_layout = QFormLayout()
        student_label = QLabel("اسم الطالب:")
        student_label.setStyleSheet(label_style)
        self.student_name_edit = QLineEdit()
        self.student_name_edit.setStyleSheet(input_style)
        self.student_name_edit.setPlaceholderText("أدخل اسم الطالب")
        self.student_name_edit.textChanged.connect(self.update_preview)
        student_layout.addRow(student_label, self.student_name_edit)
        form_layout.addLayout(student_layout)

        # Course name
        course_layout = QFormLayout()
        course_label = QLabel("اسم الكورس:")
        course_label.setStyleSheet(label_style)
        self.course_name_edit = QLineEdit()
        self.course_name_edit.setStyleSheet(input_style)
        self.course_name_edit.setPlaceholderText("أدخل اسم الكورس")
        self.course_name_edit.textChanged.connect(self.update_preview)
        course_layout.addRow(course_label, self.course_name_edit)
        form_layout.addLayout(course_layout)

        # Company name (new field)
        company_layout = QFormLayout()
        company_label = QLabel("اسم الشركة:")
        company_label.setStyleSheet(label_style)
        self.company_name_edit = QLineEdit()
        self.company_name_edit.setStyleSheet(input_style)
        self.company_name_edit.setPlaceholderText("أدخل اسم الشركة أو المؤسسة")
        self.company_name_edit.textChanged.connect(self.update_preview)
        company_layout.addRow(company_label, self.company_name_edit)
        form_layout.addLayout(company_layout)

        # Certificate intro text (new field)
        intro_layout = QFormLayout()
        intro_label = QLabel("نص الشهادة:")
        intro_label.setStyleSheet(label_style)
        self.intro_text_edit = QLineEdit()
        self.intro_text_edit.setStyleSheet(input_style)
        self.intro_text_edit.setPlaceholderText("مثال: تشهد أكاديمية التطوير الاحترافي بأن")
        self.intro_text_edit.setText("تشهد أكاديمية التطوير الاحترافي بأن")
        self.intro_text_edit.textChanged.connect(self.update_preview)
        intro_layout.addRow(intro_label, self.intro_text_edit)
        form_layout.addLayout(intro_layout)

        # Instructor name
        instructor_layout = QFormLayout()
        instructor_label = QLabel("اسم المدرب:")
        instructor_label.setStyleSheet(label_style)
        self.instructor_name_edit = QLineEdit()
        self.instructor_name_edit.setStyleSheet(input_style)
        self.instructor_name_edit.setPlaceholderText("أدخل اسم المدرب")
        self.instructor_name_edit.textChanged.connect(self.update_preview)
        instructor_layout.addRow(instructor_label, self.instructor_name_edit)
        form_layout.addLayout(instructor_layout)

        # Date
        date_layout = QFormLayout()
        date_label = QLabel("تاريخ الإصدار:")
        date_label.setStyleSheet(label_style)
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDisplayFormat("dd/MM/yyyy")
        self.date_edit.setStyleSheet("QDateEdit { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }")
        self.date_edit.dateChanged.connect(self.update_preview)
        date_layout.addRow(date_label, self.date_edit)
        form_layout.addLayout(date_layout)

        # Page orientation (new field)
        orientation_layout = QFormLayout()
        orientation_label = QLabel("اتجاه الطباعة:")
        orientation_label.setStyleSheet(label_style)

        orientation_container = QHBoxLayout()

        self.landscape_radio = QRadioButton("أفقي")
        self.landscape_radio.setChecked(True)
        self.landscape_radio.toggled.connect(self.orientation_changed)

        self.portrait_radio = QRadioButton("رأسي")
        self.portrait_radio.toggled.connect(self.orientation_changed)

        orientation_container.addWidget(self.landscape_radio)
        orientation_container.addWidget(self.portrait_radio)
        orientation_container.addStretch()

        orientation_layout.addRow(orientation_label, orientation_container)
        form_layout.addLayout(orientation_layout)

        # Logo selection
        logo_layout = QHBoxLayout()
        self.logo_label = QLabel("الشعار:")
        self.logo_label.setStyleSheet(label_style)
        self.select_logo_button = QPushButton("اختر الشعار")
        self.select_logo_button.setStyleSheet(
            "QPushButton { background-color: #2196F3; color: white; padding: 8px; border-radius: 4px; }"
            "QPushButton:hover { background-color: #0b7dda; }"
        )
        self.select_logo_button.clicked.connect(self.select_logo)
        self.selected_logo_path_label = QLabel("لم يتم اختيار شعار")
        logo_layout.addWidget(self.logo_label)
        logo_layout.addWidget(self.select_logo_button)
        logo_layout.addWidget(self.selected_logo_path_label)
        form_layout.addLayout(logo_layout)

        # Add some spacing
        form_layout.addSpacing(20)

        # Generate button
        self.generate_button = QPushButton("إنشاء وطباعة الشهادة (PDF)")
        self.generate_button.setStyleSheet(
            "QPushButton { background-color: #4CAF50; color: white; padding: 12px; "
            "font-size: 16px; font-weight: bold; border-radius: 4px; }"
            "QPushButton:hover { background-color: #45a049; }"
        )
        self.generate_button.clicked.connect(self.generate_certificate)
        form_layout.addWidget(self.generate_button)

        # Add stretch to push everything to the top
        form_layout.addStretch()

        form_group.setLayout(form_layout)

        # Right side - Certificate preview
        preview_group = QGroupBox("معاينة الشهادة")
        preview_layout = QVBoxLayout()
        self.preview = CertificatePreview()
        preview_layout.addWidget(self.preview)
        preview_group.setLayout(preview_layout)

        # Add both sides to main layout
        main_layout.addWidget(form_group, 1)  # 1 part of the space
        main_layout.addWidget(preview_group, 2)  # 2 parts of the space

        self.setLayout(main_layout)

        # Initialize preview
        self.update_preview()

    def orientation_changed(self):
        """Handle orientation radio button changes"""
        self.is_landscape = self.landscape_radio.isChecked()
        self.update_preview()

    def update_preview(self):
        """Update the certificate preview based on current form values"""
        student_name = self.student_name_edit.text().strip()
        course_name = self.course_name_edit.text().strip()
        company_name = self.company_name_edit.text().strip()
        instructor_name = self.instructor_name_edit.text().strip()
        intro_text = self.intro_text_edit.text().strip()
        issue_date = self.date_edit.date()

        # Use default intro text if empty
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"

        # Update preview with all values including company name and intro text
        self.preview.update_preview(
            student_name,
            course_name,
            instructor_name,
            issue_date,
            self.logo_path,
            company_name,
            self.is_landscape,
            intro_text
        )

    def select_logo(self):
        """Open file dialog to select a logo image"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر صورة الشعار",
            "",
            "Images (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_name:
            self.logo_path = file_name
            self.selected_logo_path_label.setText(os.path.basename(file_name))
            print(f"Logo selected: {self.logo_path}")
            # Update preview with new logo
            self.update_preview()

    def generate_certificate(self):
        """Generate and save the certificate as PDF"""
        student_name = self.student_name_edit.text().strip()
        course_name = self.course_name_edit.text().strip()
        company_name = self.company_name_edit.text().strip()
        instructor_name = self.instructor_name_edit.text().strip()
        intro_text = self.intro_text_edit.text().strip()
        issue_date = self.date_edit.date()

        # Use default intro text if empty
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"

        if not all([student_name, course_name, instructor_name]):
            QMessageBox.warning(self, "نقص في البيانات", "يرجى ملء جميع الحقول المطلوبة.")
            return

        # Show print preview dialog
        preview_dialog = QPrintPreviewDialog()
        preview_dialog.paintRequested.connect(
            lambda printer: self.print_certificate(
                printer,
                student_name,
                course_name,
                instructor_name,
                issue_date,
                company_name,
                intro_text
            )
        )

        if preview_dialog.exec():
            # User clicked print in the preview dialog
            # --- PDF Generation ---
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)

            # Suggest a filename, allow user to change
            default_pdf_path = os.path.join(os.path.expanduser("~"), f"شهادة_{student_name.replace(' ', '_')}.pdf")
            pdf_path, _ = QFileDialog.getSaveFileName(self, "حفظ الشهادة كـ PDF", default_pdf_path, "PDF Files (*.pdf)")

            if not pdf_path:
                return # User cancelled save dialog

            printer.setOutputFileName(pdf_path)

            # Set A4 orientation based on user selection
            page_size = QPageSize(QPageSize.PageSizeid.A4)
            orientation = QPageLayout.Orientation.Landscape if self.is_landscape else QPageLayout.Orientation.Portrait
            page_layout = QPageLayout(page_size, orientation, QMarginsF(15, 15, 15, 15)) # margins in mm
            printer.setPageLayout(page_layout)

            # Print to PDF
            self.print_certificate(printer, student_name, course_name, instructor_name, issue_date, company_name, intro_text)

            # Show success message and open the PDF
            QMessageBox.information(self, "نجاح", f"تم حفظ الشهادة بنجاح في:\n{pdf_path}")
            QDesktopServices.openUrl(QUrl.fromLocalFile(pdf_path))

    def print_certificate(self, printer, student_name, course_name, instructor_name, issue_date, company_name=None, intro_text=None):
        """Draw the certificate content on the printer/PDF"""
        painter = QPainter()
        if not painter.begin(printer):
            QMessageBox.critical(self, "خطأ", "فشل في بدء عملية الرسم على الطابعة.")
            return

        # --- Drawing Content ---
        # Use pageRect in pixels for drawing coordinates
        page_rect_pixels = printer.pageRect(QPrinter.Unit.DevicePixel)
        width = page_rect_pixels.width()
        height = page_rect_pixels.height()

        # Margins (can be adjusted)
        margin_x = int(width * 0.05) # 5% margin
        margin_y = int(height * 0.05)
        content_width = width - 2 * margin_x
        content_height = height - 2 * margin_y

        # Background with gradient (optional)
        background_gradient = QLinearGradient(0, 0, 0, height)
        background_gradient.setColorAt(0, QColor(255, 255, 255))  # White at top
        background_gradient.setColorAt(1, QColor(240, 240, 255))  # Light blue-ish at bottom
        painter.fillRect(0, 0, width, height, QBrush(background_gradient))

        # Border
        painter.setPen(QPen(QColor("#003366"), 5)) # Dark blue border, 5px thick
        border_rect = QRectF(margin_x / 2, margin_y / 2, width - margin_x, height - margin_y)
        painter.drawRoundedRect(border_rect, 15.0, 15.0) # Rounded corners

        # Decorative elements (optional)
        # Corner decorations
        corner_size = 40
        painter.setPen(QPen(QColor("#005A9C"), 2))

        # Top-left corner
        painter.drawLine(margin_x, margin_y + corner_size, margin_x, margin_y)
        painter.drawLine(margin_x, margin_y, margin_x + corner_size, margin_y)

        # Top-right corner
        painter.drawLine(width - margin_x - corner_size, margin_y, width - margin_x, margin_y)
        painter.drawLine(width - margin_x, margin_y, width - margin_x, margin_y + corner_size)

        # Bottom-left corner
        painter.drawLine(margin_x, height - margin_y - corner_size, margin_x, height - margin_y)
        painter.drawLine(margin_x, height - margin_y, margin_x + corner_size, height - margin_y)

        # Bottom-right corner
        painter.drawLine(width - margin_x - corner_size, height - margin_y, width - margin_x, height - margin_y)
        painter.drawLine(width - margin_x, height - margin_y, width - margin_x, height - margin_y - corner_size)

        # Logo
        if self.logo_path and os.path.exists(self.logo_path):
            logo_pixmap = QPixmap(self.logo_path)
            if not logo_pixmap.isNull():
                logo_max_width = int(content_width * 0.20) # Logo takes up to 20% of content width
                logo_max_height = int(content_height * 0.15) # Logo takes up to 15% of content height

                scaled_logo = logo_pixmap.scaled(logo_max_width, logo_max_height,
                                                 Qt.AspectRatioMode.KeepAspectRatio,
                                                 Qt.TransformationMode.SmoothTransformation)
                # Position logo: top-left corner within content area
                logo_x = margin_x + 10 # Small offset from content margin
                logo_y = margin_y + 10
                painter.drawPixmap(logo_x, logo_y, scaled_logo)
            else:
                print(f"Failed to load logo: {self.logo_path}")

        # --- Text Styling ---
        painter.setRenderHint(QPainter.RenderHint.Antialiasing) # For smoother text

        # Common text color
        text_color = QColor("#333333") # Dark Gray

        # Company name (new)
        if company_name:
            company_font = QFont("Arial", 20)
            company_font.setBold(True)
            painter.setFont(company_font)
            painter.setPen(QColor("#005A9C"))
            company_rect = QRectF(margin_x, margin_y + int(content_height * 0.08), content_width, int(content_height * 0.08))
            painter.drawText(company_rect, Qt.AlignmentFlag.AlignCenter, company_name)

        # Title: "شهادة مشاركة"
        title_font = QFont("Arial", 36)
        title_font.setBold(True)  # Set bold style
        painter.setFont(title_font)
        painter.setPen(QColor("#005A9C")) # A specific blue for title
        title_text = "شهادة مشاركة"

        # Adjust title position if company name is present
        title_y_position = margin_y + int(content_height * (0.15 if not company_name else 0.20))
        title_rect = QRectF(margin_x, title_y_position, content_width, int(content_height * 0.15))
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, title_text)

        # Body Font
        body_font_size = 18
        body_font = QFont("Arial", body_font_size) # Or "Tahoma"

        # Line Spacing (approximate)
        line_height = body_font_size * 2.5 # Adjust as needed

        current_y = margin_y + int(content_height * 0.35) # Start below title

        # Text: Certificate intro text (customizable)
        painter.setFont(body_font)
        painter.setPen(text_color)
        # Use default intro text if none provided
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"
        intro_rect = QRectF(margin_x, current_y, content_width, line_height)
        painter.drawText(intro_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, intro_text)
        current_y += line_height

        # Student Name
        student_name_font = QFont("Arial", 28)
        student_name_font.setBold(True)  # Set bold style
        painter.setFont(student_name_font)
        painter.setPen(QColor("#003366")) # Dark Blue for name
        student_name_rect = QRectF(margin_x, current_y, content_width, line_height * 1.2)
        painter.drawText(student_name_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, student_name)
        current_y += line_height * 1.2

        # Text: "قد أتم بنجاح دورة"
        painter.setFont(body_font)
        painter.setPen(text_color)
        completion_text = "قد أتم بنجاح دورة تدريبية بعنوان:"
        completion_rect = QRectF(margin_x, current_y, content_width, line_height)
        painter.drawText(completion_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, completion_text)
        current_y += line_height

        # Course Name
        course_name_font = QFont("Arial", 24)
        course_name_font.setWeight(QFont.Weight.DemiBold)  # Set demibold weight
        painter.setFont(course_name_font)
        painter.setPen(QColor("#D2691E")) # Chocolate color for course name
        course_name_rect = QRectF(margin_x, current_y, content_width, line_height * 1.1)
        painter.drawText(course_name_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, course_name)
        current_y += line_height * 1.1

        # Text: "مع المدرب"
        painter.setFont(body_font)
        painter.setPen(text_color)
        instructor_intro_text = "تحت إشراف المدرب:"
        instructor_intro_rect = QRectF(margin_x, current_y, content_width, line_height)
        painter.drawText(instructor_intro_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, instructor_intro_text)
        current_y += line_height

        # Instructor Name
        instructor_font = QFont("Arial", 20)
        painter.setFont(instructor_font)
        painter.setPen(text_color) # Standard text color
        instructor_name_rect = QRectF(margin_x, current_y, content_width, line_height)
        painter.drawText(instructor_name_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, instructor_name)
        current_y += line_height * 1.5 # More space before date

        # Date
        painter.setFont(body_font)
        painter.setPen(text_color)
        date_text = f"وذلك بتاريخ: {self.format_arabic_date(issue_date)}"
        date_rect = QRectF(margin_x, current_y, content_width, line_height)
        painter.drawText(date_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, date_text)
        current_y += line_height * 2

        # Signature Lines
        signature_font = QFont("Arial", 14)
        painter.setFont(signature_font)
        painter.setPen(text_color)

        signature_line_y = height - margin_y - int(content_height * 0.15) # Position from bottom
        signature_label_height = 30
        signature_line_length = content_width * 0.3

        # Signature 1 (e.g., Instructor)
        sig1_label_rect = QRectF(margin_x + content_width * 0.1, signature_line_y, signature_line_length, signature_label_height)
        painter.drawText(sig1_label_rect, Qt.AlignmentFlag.AlignCenter, "توقيع المدرب")
        painter.drawLine(int(sig1_label_rect.left()), int(sig1_label_rect.top() - 5),
                         int(sig1_label_rect.right()), int(sig1_label_rect.top() - 5))

        # Signature 2 (e.g., Director)
        sig2_label_rect = QRectF(width - margin_x - content_width * 0.1 - signature_line_length, signature_line_y, signature_line_length, signature_label_height)
        painter.drawText(sig2_label_rect, Qt.AlignmentFlag.AlignCenter, "ختم المؤسسة")
        painter.drawLine(int(sig2_label_rect.left()), int(sig2_label_rect.top() - 5),
                         int(sig2_label_rect.right()), int(sig2_label_rect.top() - 5))

        # Footer (Optional - e.g., Certificate id or a small note)
        footer_font = QFont("Arial", 10)
        footer_font.setItalic(True)  # Set italic style
        painter.setFont(footer_font)
        painter.setPen(QColor("#666666")) # Lighter gray
        footer_text = "هذه الشهادة لا تعتبر مؤهلاً علمياً رسمياً."
        footer_rect = QRectF(margin_x, height - margin_y - 25, content_width, 20)
        painter.drawText(footer_rect, Qt.AlignmentFlag.AlignCenter, footer_text)

        painter.end()

    # Helper for Arabic date
    def format_arabic_date(self, q_date):
        """Format date with Arabic month names"""
        months_ar = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        day = q_date.day()
        month = months_ar[q_date.month() - 1]
        year = q_date.year()
        return f"{day} {month} {year}"


if __name__ == '__main__':
    app = QApplication(sys.argv)
    # Force Right-to-Left layout for the application
    # This affects the GUI elements' layout for proper Arabic display
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # Set application style for a more modern look
    app.setStyle("Fusion")

    # Apply a stylesheet for better visual appearance
    app.setStyleSheet("""
        QGroupBox {
            font-weight: bold;
            border: 1px solid #cccccc;
            border-radius: 6px;
            margin-top: 12px;
            padding-top: 12px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px;
        }

        QLabel {
            font-size: 12px;
        }

        QLineEdit, QDateEdit {
            padding: 8px;
            border: 1px solid #cccccc;
            border-radius: 4px;
        }

        QPushButton {
            padding: 8px 16px;
            border-radius: 4px;
        }
    """)

    # Create and show the application
    ex = CertificateApp()
    ex.show()

    # Start the event loop
    sys.exit(app.exec())