import sys
import os
import math
from PySide6.QtWidgets import (
    QApp<PERSON>, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFileDialog,
    QDateEdit, QMessageBox, QFormLayout, QGroupBox,
    QSplitter, QFrame, QComboBox, QColorDialog, QScrollArea,
    QRadioButton
)
from PySide6.QtGui import (
    QPainter, QPixmap, QFont, QColor, QDesktopServices,
    QPageLayout, QPageSize, QPen, QLinearGradient, QBrush,
    QRadialGradient, QImage, QPainterPath
)
from PySide6.QtCore import Qt, QDate, QUrl, QRectF, QMarginsF, QSize
from PySide6.QtPrintSupport import QPrinter, QPrintPreviewDialog


class CertificatePreview(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(600, 400)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("border: 1px solid #cccccc; background-color: #f9f9f9;")
        self.setText("معاينة الشهادة ستظهر هنا")
        self.setScaledContents(True)

    def update_preview(self, student_name, course_name, instructor_name, date, logo_path=None, company_name=None, is_landscape=True, intro_text=None):
        # Create a blank image with A4 proportions (scaled down)
        preview_width = self.width()

        # Use default intro text if none provided
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"

        if is_landscape:
            # A4 landscape ratio is 1.4142 (width/height)
            preview_height = int(preview_width / 1.4142)
        else:
            # A4 portrait ratio is 0.7071 (width/height)
            preview_height = int(preview_width / 0.7071)
            # Swap dimensions for portrait
            preview_width, preview_height = preview_height, preview_width

        # Create a QPixmap to draw on
        pixmap = QPixmap(preview_width, preview_height)
        pixmap.fill(Qt.GlobalColor.white)

        # Create painter
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Calculate dimensions for the left side pattern
        left_pattern_width = int(preview_width * 0.25)  # 25% of width for the left pattern

        # Create a gradient for the main content area (subtle blue to white)
        main_gradient = QLinearGradient(left_pattern_width, 0, preview_width, preview_height)
        main_gradient.setColorAt(0, QColor(240, 248, 255))  # Very light blue
        main_gradient.setColorAt(1, QColor(255, 255, 255))  # White
        painter.fillRect(0, 0, preview_width, preview_height, main_gradient)

        # Create a gradient for the left side (dark blue to navy)
        left_gradient = QLinearGradient(0, 0, left_pattern_width, preview_height)
        left_gradient.setColorAt(0, QColor(25, 25, 112))  # Midnight Blue
        left_gradient.setColorAt(1, QColor(0, 51, 102))   # Dark Navy
        painter.fillRect(0, 0, left_pattern_width, preview_height, left_gradient)

        # Draw geometric pattern on the left side (more professional pattern)
        painter.setPen(QPen(QColor(255, 255, 255, 40), 1))  # Very transparent white

        # Draw a geometric pattern (more professional)
        pattern_size = 30
        # Convert to integers for range function
        int_left_pattern_width = int(left_pattern_width)
        int_preview_height = int(preview_height)

        # Draw horizontal and vertical lines for a grid pattern
        for x in range(0, int_left_pattern_width, pattern_size):
            painter.drawLine(x, 0, x, int_preview_height)

        for y in range(0, int_preview_height, pattern_size):
            painter.drawLine(0, y, int_left_pattern_width, y)

        # Draw diagonal lines for a more complex pattern
        for i in range(-int_preview_height, int_left_pattern_width + int_preview_height, pattern_size * 2):
            painter.drawLine(0, i, i, 0)
            painter.drawLine(0, int_preview_height - i, i, int_preview_height)

        # Draw gold/orange vertical border
        gold_color = QColor(218, 165, 32)  # Gold color
        painter.fillRect(left_pattern_width, 0, 8, preview_height, gold_color)

        # Add a subtle shadow effect to the border
        shadow_width = 5
        shadow_gradient = QLinearGradient(left_pattern_width + 8, 0, left_pattern_width + 8 + shadow_width, 0)
        shadow_gradient.setColorAt(0, QColor(0, 0, 0, 50))  # Semi-transparent black
        shadow_gradient.setColorAt(1, QColor(0, 0, 0, 0))   # Transparent
        painter.fillRect(left_pattern_width + 8, 0, shadow_width, preview_height, shadow_gradient)

        # Draw more elegant gold corner decorations in top-right and bottom-right
        corner_size = int(preview_width * 0.15)
        pen = QPen(gold_color, 2)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)

        # Top-right corner decoration (more ornate)
        margin = 20
        # Main corner lines
        painter.drawLine(preview_width - corner_size, margin, preview_width - margin, margin)
        painter.drawLine(preview_width - margin, margin, preview_width - margin, corner_size)

        # Additional decorative elements
        # Small diagonal flourish
        painter.drawLine(preview_width - corner_size + 15, margin + 15, preview_width - margin - 15, margin + 15)
        painter.drawLine(preview_width - margin - 15, margin + 15, preview_width - margin - 15, corner_size - 15)

        # Small circle at the corner junction
        painter.setBrush(QBrush(gold_color))
        painter.drawEllipse(preview_width - margin - 5, margin - 5, 10, 10)

        # Bottom-right corner decoration (mirror of top-right)
        # Main corner lines
        painter.drawLine(preview_width - corner_size, preview_height - margin, preview_width - margin, preview_height - margin)
        painter.drawLine(preview_width - margin, preview_height - margin, preview_width - margin, preview_height - corner_size)

        # Additional decorative elements
        # Small diagonal flourish
        painter.drawLine(preview_width - corner_size + 15, preview_height - margin - 15, preview_width - margin - 15, preview_height - margin - 15)
        painter.drawLine(preview_width - margin - 15, preview_height - margin - 15, preview_width - margin - 15, preview_height - corner_size + 15)

        # Small circle at the corner junction
        painter.drawEllipse(preview_width - margin - 5, preview_height - margin - 5, 10, 10)

        # Add subtle decorative elements to the top-left and bottom-left corners where the pattern meets the border
        painter.setPen(QPen(QColor(255, 255, 255, 80), 1))  # Semi-transparent white

        # Top-left corner
        painter.drawLine(left_pattern_width - 15, margin, left_pattern_width - 5, margin)
        painter.drawLine(left_pattern_width - 5, margin, left_pattern_width - 5, margin + 10)

        # Bottom-left corner
        painter.drawLine(left_pattern_width - 15, preview_height - margin, left_pattern_width - 5, preview_height - margin)
        painter.drawLine(left_pattern_width - 5, preview_height - margin, left_pattern_width - 5, preview_height - margin - 10)

        # Draw Arabic calligraphy at the top (Bismillah)
        calligraphy_y = 30
        calligraphy_height = int(preview_height * 0.08)
        calligraphy_width = int(preview_width * 0.6)
        calligraphy_x = left_pattern_width + 10 + (preview_width - left_pattern_width - 20 - calligraphy_width) // 2

        # Draw decorative line under the calligraphy
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        line_y = calligraphy_y + calligraphy_height + 5
        painter.drawLine(calligraphy_x + calligraphy_width//4, line_y,
                        calligraphy_x + calligraphy_width - calligraphy_width//4, line_y)

        # Draw decorative ornament in the middle of the line
        ornament_size = 10
        ornament_x = calligraphy_x + calligraphy_width//2 - ornament_size//2
        painter.setPen(QPen(gold_color, 1))
        painter.drawEllipse(ornament_x, line_y - ornament_size//2, ornament_size, ornament_size)

        # Draw professional medal/seal with laurel leaves on the left side
        medal_x = left_pattern_width // 2
        medal_y = preview_height // 2
        medal_size = int(left_pattern_width * 0.7)

        # Create a radial gradient for the medal background
        medal_gradient = QRadialGradient(medal_x, medal_y, medal_size/2)
        medal_gradient.setColorAt(0, QColor(255, 215, 0))      # Bright gold at center
        medal_gradient.setColorAt(0.7, QColor(218, 165, 32))   # Gold
        medal_gradient.setColorAt(1, QColor(184, 134, 11))     # Darker gold at edge

        # Draw the circular medal background with gradient
        painter.setBrush(QBrush(medal_gradient))
        painter.setPen(QPen(QColor(150, 120, 0), 2))  # Darker gold border
        painter.drawEllipse(medal_x - medal_size//2, medal_y - medal_size//2, medal_size, medal_size)

        # Add a highlight effect (light reflection)
        highlight_path = QPainterPath()
        highlight_path.addEllipse(medal_x - medal_size//4, medal_y - medal_size//4, medal_size//2, medal_size//2)
        painter.setBrush(QBrush(QColor(255, 255, 255, 40)))  # Semi-transparent white
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawPath(highlight_path)

        # Draw inner circle with gradient (darker blue to match the theme)
        inner_gradient = QRadialGradient(medal_x, medal_y, (medal_size-20)/2)
        inner_gradient.setColorAt(0, QColor(30, 50, 100))    # Lighter navy blue
        inner_gradient.setColorAt(1, QColor(10, 30, 70))     # Darker navy blue

        painter.setBrush(QBrush(inner_gradient))
        painter.setPen(QPen(QColor(218, 165, 32), 1))  # Thin gold border
        painter.drawEllipse(medal_x - medal_size//2 + 10, medal_y - medal_size//2 + 10,
                           medal_size - 20, medal_size - 20)

        # Draw a more formal and professional emblem in the center
        painter.setPen(QPen(gold_color, 1))

        # Create a circular emblem with concentric circles
        emblem_size = medal_size // 3

        # Outer circle with gold color
        painter.setBrush(QBrush(gold_color))
        painter.drawEllipse(medal_x - emblem_size/2, medal_y - emblem_size/2, emblem_size, emblem_size)

        # Inner circle with lighter color
        inner_emblem_size = emblem_size * 0.7
        painter.setBrush(QBrush(QColor(240, 240, 240)))  # Light color
        painter.drawEllipse(medal_x - inner_emblem_size/2, medal_y - inner_emblem_size/2,
                           inner_emblem_size, inner_emblem_size)

        # Draw a formal shield or crest shape inside
        painter.setPen(QPen(gold_color, 1.5))
        painter.setBrush(QBrush(QColor(30, 50, 100)))  # Navy blue

        # Create a shield shape
        shield_width = inner_emblem_size * 0.6
        shield_height = inner_emblem_size * 0.7

        shield_path = QPainterPath()
        shield_path.moveTo(medal_x, medal_y - shield_height/2)  # Top center

        # Top right curve
        shield_path.cubicTo(
            medal_x + shield_width/4, medal_y - shield_height/2,  # Control point 1
            medal_x + shield_width/2, medal_y - shield_height/4,  # Control point 2
            medal_x + shield_width/2, medal_y                    # End point (right middle)
        )

        # Bottom right curve
        shield_path.cubicTo(
            medal_x + shield_width/2, medal_y + shield_height/4,  # Control point 1
            medal_x + shield_width/4, medal_y + shield_height/2,  # Control point 2
            medal_x, medal_y + shield_height/2                   # End point (bottom center)
        )

        # Bottom left curve (mirror of right side)
        shield_path.cubicTo(
            medal_x - shield_width/4, medal_y + shield_height/2,  # Control point 1
            medal_x - shield_width/2, medal_y + shield_height/4,  # Control point 2
            medal_x - shield_width/2, medal_y                    # End point (left middle)
        )

        # Top left curve
        shield_path.cubicTo(
            medal_x - shield_width/2, medal_y - shield_height/4,  # Control point 1
            medal_x - shield_width/4, medal_y - shield_height/2,  # Control point 2
            medal_x, medal_y - shield_height/2                   # End point (back to top center)
        )

        painter.drawPath(shield_path)

        # Add decorative horizontal lines inside the shield
        painter.setPen(QPen(gold_color, 0.8))
        line_spacing = shield_height / 5
        for i in range(1, 4):
            y_pos = medal_y - shield_height/2 + i * line_spacing
            # Calculate x position at this y level (shield gets narrower toward top and bottom)
            # This is an approximation of the shield's width at this y level
            width_factor = 1.0 - abs(i - 2.0) / 3.0
            x_width = shield_width/2 * width_factor
            painter.drawLine(medal_x - x_width, y_pos, medal_x + x_width, y_pos)

        # Add a small circle or dot in the center
        center_size = shield_width * 0.15
        painter.setBrush(QBrush(gold_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(medal_x - center_size/2, medal_y - center_size/2, center_size, center_size)

        # Draw elegant laurel leaves around the medal
        painter.setPen(QPen(gold_color, 1.5))
        leaf_length = medal_size // 2.5

        # Draw several leaves around the medal
        for angle in range(0, 360, 20):  # More leaves (every 20 degrees)
            rad_angle = angle * 3.14159 / 180
            start_x = medal_x + int((medal_size//2) * 0.9 * math.cos(rad_angle))
            start_y = medal_y + int((medal_size//2) * 0.9 * math.sin(rad_angle))
            end_x = medal_x + int((medal_size//2 + leaf_length) * math.cos(rad_angle))
            end_y = medal_y + int((medal_size//2 + leaf_length) * math.sin(rad_angle))

            # Draw a curved leaf
            path = QPainterPath()
            path.moveTo(start_x, start_y)

            # Control point for the curve
            ctrl_angle = rad_angle + 0.2
            ctrl_x = medal_x + int((medal_size//2 + leaf_length * 0.7) * math.cos(ctrl_angle))
            ctrl_y = medal_y + int((medal_size//2 + leaf_length * 0.7) * math.sin(ctrl_angle))

            path.quadTo(ctrl_x, ctrl_y, end_x, end_y)
            painter.drawPath(path)

            # Add a second curve for more detailed leaves
            if angle % 40 == 0:  # Only for some leaves
                ctrl_angle2 = rad_angle - 0.2
                ctrl_x2 = medal_x + int((medal_size//2 + leaf_length * 0.6) * math.cos(ctrl_angle2))
                ctrl_y2 = medal_y + int((medal_size//2 + leaf_length * 0.6) * math.sin(ctrl_angle2))

                path2 = QPainterPath()
                path2.moveTo(start_x, start_y)
                path2.quadTo(ctrl_x2, ctrl_y2, end_x, end_y)
                painter.drawPath(path2)

        # Draw logo if available
        if logo_path and os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            if not logo_pixmap.isNull():
                logo_max_width = int(preview_width * 0.15)
                logo_max_height = int(preview_height * 0.15)
                scaled_logo = logo_pixmap.scaled(logo_max_width, logo_max_height,
                                               Qt.AspectRatioMode.KeepAspectRatio,
                                               Qt.TransformationMode.SmoothTransformation)
                # Position logo in the top-right area
                logo_x = preview_width - logo_max_width - 20
                logo_y = 20
                painter.drawPixmap(logo_x, logo_y, scaled_logo)

        # Draw company name if available
        if company_name:
            company_font = QFont("Arial", int(preview_width * 0.025))
            company_font.setBold(True)
            painter.setFont(company_font)
            painter.setPen(QColor("#005A9C"))
            company_rect = QRectF(left_pattern_width + 20, preview_height * 0.15,
                                 preview_width - left_pattern_width - 40, preview_height * 0.08)
            painter.drawText(company_rect, Qt.AlignmentFlag.AlignCenter, company_name)

        # Draw title
        title_font = QFont("Arial", int(preview_width * 0.05))
        title_font.setBold(True)  # Set bold style
        painter.setFont(title_font)
        painter.setPen(gold_color)  # Use gold color for title
        title_rect = QRectF(left_pattern_width + 20, preview_height * 0.22,
                           preview_width - left_pattern_width - 40, preview_height * 0.1)
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, "شهادة مشاركة")

        # Draw intro text
        intro_font = QFont("Arial", int(preview_width * 0.025))
        painter.setFont(intro_font)
        painter.setPen(QColor("#333333"))
        intro_rect = QRectF(left_pattern_width + 20, preview_height * 0.32,
                           preview_width - left_pattern_width - 40, preview_height * 0.08)
        painter.drawText(intro_rect, Qt.AlignmentFlag.AlignCenter, intro_text)

        # Draw student name
        name_font = QFont("Arial", int(preview_width * 0.035))
        name_font.setBold(True)  # Set bold style
        painter.setFont(name_font)
        painter.setPen(QColor("#003366"))
        name_rect = QRectF(left_pattern_width + 20, preview_height * 0.38,
                          preview_width - left_pattern_width - 40, preview_height * 0.1)
        painter.drawText(name_rect, Qt.AlignmentFlag.AlignCenter, student_name if student_name else "اسم الطالب")

        # Draw decorative line under student name
        painter.setPen(QPen(gold_color, 1, Qt.PenStyle.DotLine))
        line_y = name_rect.y() + name_rect.height() * 0.8
        line_width = (preview_width - left_pattern_width - 40) * 0.6
        line_x = left_pattern_width + 20 + (preview_width - left_pattern_width - 40 - line_width) / 2
        painter.drawLine(int(line_x), int(line_y), int(line_x + line_width), int(line_y))

        # Draw course name
        course_font = QFont("Arial", int(preview_width * 0.03))
        painter.setFont(course_font)
        painter.setPen(QColor("#D2691E"))
        course_rect = QRectF(left_pattern_width + 20, preview_height * 0.5,
                            preview_width - left_pattern_width - 40, preview_height * 0.1)
        painter.drawText(course_rect, Qt.AlignmentFlag.AlignCenter, course_name if course_name else "اسم الكورس")

        # Draw instructor name
        painter.setPen(QColor("#333333"))
        instructor_rect = QRectF(left_pattern_width + 20, preview_height * 0.6,
                                preview_width - left_pattern_width - 40, preview_height * 0.1)
        painter.drawText(instructor_rect, Qt.AlignmentFlag.AlignCenter,
                        f"المدرب: {instructor_name}" if instructor_name else "اسم المدرب")

        # Draw date and signature area
        # Draw two signature lines at the bottom
        painter.setPen(QPen(QColor("#333333"), 1))

        # Date on the right side
        date_font = QFont("Arial", int(preview_width * 0.02))
        painter.setFont(date_font)
        date_rect = QRectF(preview_width - 150, preview_height * 0.8, 120, 30)
        date_str = date.toString("dd/MM/yyyy") if date else QDate.currentDate().toString("dd/MM/yyyy")
        painter.drawText(date_rect, Qt.AlignmentFlag.AlignCenter, f"التاريخ: {date_str}")

        # Draw signature line on the left
        sig_x = preview_width - 200
        sig_y = preview_height * 0.85
        sig_width = 150
        painter.drawLine(sig_x, sig_y, sig_x + sig_width, sig_y)

        # Draw "signature" text under the line
        sig_text_rect = QRectF(sig_x, sig_y + 5, sig_width, 20)
        painter.drawText(sig_text_rect, Qt.AlignmentFlag.AlignCenter, "الإمضاء")

        # End painting
        painter.end()

        # Set the pixmap to the label
        self.setPixmap(pixmap)


class CertificateApp(QWidget):
    def __init__(self):
        super().__init__()
        self.logo_path = None
        self.signature_path = None  # New: path to signature image
        self.is_landscape = True  # Default to landscape orientation
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("مولد شهادات المشاركة")
        self.setGeometry(100, 100, 1000, 700)  # Larger window
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)  # RTL layout for Arabic

        # Main layout
        main_layout = QHBoxLayout()

        # Left side - Form inputs
        form_group = QGroupBox("بيانات الشهادة")
        form_layout = QVBoxLayout()

        # Input fields with styling
        input_style = "QLineEdit { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }"
        label_style = "QLabel { font-weight: bold; }"

        # Student name
        student_layout = QFormLayout()
        student_label = QLabel("اسم الطالب:")
        student_label.setStyleSheet(label_style)
        self.student_name_edit = QLineEdit()
        self.student_name_edit.setStyleSheet(input_style)
        self.student_name_edit.setPlaceholderText("أدخل اسم الطالب")
        self.student_name_edit.textChanged.connect(self.update_preview)
        student_layout.addRow(student_label, self.student_name_edit)
        form_layout.addLayout(student_layout)

        # Course name
        course_layout = QFormLayout()
        course_label = QLabel("اسم الكورس:")
        course_label.setStyleSheet(label_style)
        self.course_name_edit = QLineEdit()
        self.course_name_edit.setStyleSheet(input_style)
        self.course_name_edit.setPlaceholderText("أدخل اسم الكورس")
        self.course_name_edit.textChanged.connect(self.update_preview)
        course_layout.addRow(course_label, self.course_name_edit)
        form_layout.addLayout(course_layout)

        # Company name (new field)
        company_layout = QFormLayout()
        company_label = QLabel("اسم الشركة:")
        company_label.setStyleSheet(label_style)
        self.company_name_edit = QLineEdit()
        self.company_name_edit.setStyleSheet(input_style)
        self.company_name_edit.setPlaceholderText("أدخل اسم الشركة أو المؤسسة")
        self.company_name_edit.textChanged.connect(self.update_preview)
        company_layout.addRow(company_label, self.company_name_edit)
        form_layout.addLayout(company_layout)

        # Certificate intro text (new field)
        intro_layout = QFormLayout()
        intro_label = QLabel("نص الشهادة:")
        intro_label.setStyleSheet(label_style)
        self.intro_text_edit = QLineEdit()
        self.intro_text_edit.setStyleSheet(input_style)
        self.intro_text_edit.setPlaceholderText("مثال: تشهد أكاديمية التطوير الاحترافي بأن")
        self.intro_text_edit.setText("تشهد أكاديمية التطوير الاحترافي بأن")
        self.intro_text_edit.textChanged.connect(self.update_preview)
        intro_layout.addRow(intro_label, self.intro_text_edit)
        form_layout.addLayout(intro_layout)

        # Instructor name
        instructor_layout = QFormLayout()
        instructor_label = QLabel("اسم المدرب:")
        instructor_label.setStyleSheet(label_style)
        self.instructor_name_edit = QLineEdit()
        self.instructor_name_edit.setStyleSheet(input_style)
        self.instructor_name_edit.setPlaceholderText("أدخل اسم المدرب")
        self.instructor_name_edit.textChanged.connect(self.update_preview)
        instructor_layout.addRow(instructor_label, self.instructor_name_edit)
        form_layout.addLayout(instructor_layout)

        # Date
        date_layout = QFormLayout()
        date_label = QLabel("تاريخ الإصدار:")
        date_label.setStyleSheet(label_style)
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDisplayFormat("dd/MM/yyyy")
        self.date_edit.setStyleSheet("QDateEdit { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }")
        self.date_edit.dateChanged.connect(self.update_preview)
        date_layout.addRow(date_label, self.date_edit)
        form_layout.addLayout(date_layout)

        # Page orientation (new field)
        orientation_layout = QFormLayout()
        orientation_label = QLabel("اتجاه الطباعة:")
        orientation_label.setStyleSheet(label_style)

        orientation_container = QHBoxLayout()

        self.landscape_radio = QRadioButton("أفقي")
        self.landscape_radio.setChecked(True)
        self.landscape_radio.toggled.connect(self.orientation_changed)

        self.portrait_radio = QRadioButton("رأسي")
        self.portrait_radio.toggled.connect(self.orientation_changed)

        orientation_container.addWidget(self.landscape_radio)
        orientation_container.addWidget(self.portrait_radio)
        orientation_container.addStretch()

        orientation_layout.addRow(orientation_label, orientation_container)
        form_layout.addLayout(orientation_layout)

        # Logo selection
        logo_layout = QHBoxLayout()
        self.logo_label = QLabel("الشعار:")
        self.logo_label.setStyleSheet(label_style)
        self.select_logo_button = QPushButton("اختر الشعار")
        self.select_logo_button.setStyleSheet(
            "QPushButton { background-color: #2196F3; color: white; padding: 8px; border-radius: 4px; }"
            "QPushButton:hover { background-color: #0b7dda; }"
        )
        self.select_logo_button.clicked.connect(self.select_logo)
        self.selected_logo_path_label = QLabel("لم يتم اختيار شعار")
        logo_layout.addWidget(self.logo_label)
        logo_layout.addWidget(self.select_logo_button)
        logo_layout.addWidget(self.selected_logo_path_label)
        form_layout.addLayout(logo_layout)

        # Add some spacing
        form_layout.addSpacing(20)

        # Generate button
        self.generate_button = QPushButton("إنشاء وطباعة الشهادة (PDF)")
        self.generate_button.setStyleSheet(
            "QPushButton { background-color: #4CAF50; color: white; padding: 12px; "
            "font-size: 16px; font-weight: bold; border-radius: 4px; }"
            "QPushButton:hover { background-color: #45a049; }"
        )
        self.generate_button.clicked.connect(self.generate_certificate)
        form_layout.addWidget(self.generate_button)

        # Add stretch to push everything to the top
        form_layout.addStretch()

        form_group.setLayout(form_layout)

        # Right side - Certificate preview
        preview_group = QGroupBox("معاينة الشهادة")
        preview_layout = QVBoxLayout()
        self.preview = CertificatePreview()
        preview_layout.addWidget(self.preview)
        preview_group.setLayout(preview_layout)

        # Add both sides to main layout
        main_layout.addWidget(form_group, 1)  # 1 part of the space
        main_layout.addWidget(preview_group, 2)  # 2 parts of the space

        self.setLayout(main_layout)

        # Initialize preview
        self.update_preview()

    def orientation_changed(self):
        """Handle orientation radio button changes"""
        self.is_landscape = self.landscape_radio.isChecked()
        self.update_preview()

    def update_preview(self):
        """Update the certificate preview based on current form values"""
        student_name = self.student_name_edit.text().strip()
        course_name = self.course_name_edit.text().strip()
        company_name = self.company_name_edit.text().strip()
        instructor_name = self.instructor_name_edit.text().strip()
        intro_text = self.intro_text_edit.text().strip()
        issue_date = self.date_edit.date()

        # Use default intro text if empty
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"

        # Update preview with all values including company name and intro text
        self.preview.update_preview(
            student_name,
            course_name,
            instructor_name,
            issue_date,
            self.logo_path,
            company_name,
            self.is_landscape,
            intro_text
        )

    def select_logo(self):
        """Open file dialog to select a logo image"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر صورة الشعار",
            "",
            "Images (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_name:
            self.logo_path = file_name
            self.selected_logo_path_label.setText(os.path.basename(file_name))
            print(f"Logo selected: {self.logo_path}")
            # Update preview with new logo
            self.update_preview()

    def generate_certificate(self):
        """Generate and save the certificate as PDF"""
        student_name = self.student_name_edit.text().strip()
        course_name = self.course_name_edit.text().strip()
        company_name = self.company_name_edit.text().strip()
        instructor_name = self.instructor_name_edit.text().strip()
        intro_text = self.intro_text_edit.text().strip()
        issue_date = self.date_edit.date()

        # Use default intro text if empty
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"

        if not all([student_name, course_name, instructor_name]):
            QMessageBox.warning(self, "نقص في البيانات", "يرجى ملء جميع الحقول المطلوبة.")
            return

        # Show print preview dialog
        preview_dialog = QPrintPreviewDialog()
        preview_dialog.paintRequested.connect(
            lambda printer: self.print_certificate(
                printer,
                student_name,
                course_name,
                instructor_name,
                issue_date,
                company_name,
                intro_text
            )
        )

        if preview_dialog.exec():
            # User clicked print in the preview dialog
            # --- PDF Generation ---
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)

            # Suggest a filename, allow user to change
            default_pdf_path = os.path.join(os.path.expanduser("~"), f"شهادة_{student_name.replace(' ', '_')}.pdf")
            pdf_path, _ = QFileDialog.getSaveFileName(self, "حفظ الشهادة كـ PDF", default_pdf_path, "PDF Files (*.pdf)")

            if not pdf_path:
                return # User cancelled save dialog

            printer.setOutputFileName(pdf_path)

            # Set A4 orientation based on user selection
            page_size = QPageSize(QPageSize.PageSizeid.A4)
            orientation = QPageLayout.Orientation.Landscape if self.is_landscape else QPageLayout.Orientation.Portrait
            page_layout = QPageLayout(page_size, orientation, QMarginsF(15, 15, 15, 15)) # margins in mm
            printer.setPageLayout(page_layout)

            # Print to PDF
            self.print_certificate(printer, student_name, course_name, instructor_name, issue_date, company_name, intro_text)

            # Show success message and open the PDF
            QMessageBox.information(self, "نجاح", f"تم حفظ الشهادة بنجاح في:\n{pdf_path}")
            QDesktopServices.openUrl(QUrl.fromLocalFile(pdf_path))

    def print_certificate(self, printer, student_name, course_name, instructor_name, issue_date, company_name=None, intro_text=None):
        """Draw the certificate content on the printer/PDF"""
        painter = QPainter()
        if not painter.begin(printer):
            QMessageBox.critical(self, "خطأ", "فشل في بدء عملية الرسم على الطابعة.")
            return

        # --- Drawing Content ---
        # Use pageRect in pixels for drawing coordinates
        page_rect_pixels = printer.pageRect(QPrinter.Unit.DevicePixel)
        width = page_rect_pixels.width()
        height = page_rect_pixels.height()

        # Margins (can be adjusted)
        margin_x = int(width * 0.05) # 5% margin
        margin_y = int(height * 0.05)
        content_width = width - 2 * margin_x
        content_height = height - 2 * margin_y

        # Calculate dimensions for the left side pattern
        left_pattern_width = int(width * 0.25)  # 25% of width for the left pattern

        # Create a gradient for the main content area (subtle blue to white)
        main_gradient = QLinearGradient(left_pattern_width, 0, width, height)
        main_gradient.setColorAt(0, QColor(240, 248, 255))  # Very light blue
        main_gradient.setColorAt(1, QColor(255, 255, 255))  # White
        painter.fillRect(0, 0, width, height, main_gradient)

        # Create a gradient for the left side (dark blue to navy)
        left_gradient = QLinearGradient(0, 0, left_pattern_width, height)
        left_gradient.setColorAt(0, QColor(25, 25, 112))  # Midnight Blue
        left_gradient.setColorAt(1, QColor(0, 51, 102))   # Dark Navy
        painter.fillRect(0, 0, left_pattern_width, height, left_gradient)

        # Draw geometric pattern on the left side (more professional pattern)
        painter.setPen(QPen(QColor(255, 255, 255, 40), 1))  # Very transparent white

        # Draw a geometric pattern (more professional)
        pattern_size = 30
        # Convert to integers for range function
        int_left_pattern_width = int(left_pattern_width)
        int_height = int(height)

        # Draw horizontal and vertical lines for a grid pattern
        for x in range(0, int_left_pattern_width, pattern_size):
            painter.drawLine(x, 0, x, int_height)

        for y in range(0, int_height, pattern_size):
            painter.drawLine(0, y, int_left_pattern_width, y)

        # Draw diagonal lines for a more complex pattern
        for i in range(-int_height, int_left_pattern_width + int_height, pattern_size * 2):
            painter.drawLine(0, i, i, 0)
            painter.drawLine(0, int_height - i, i, int_height)

        # Define gold color
        gold_color = QColor(218, 165, 32)  # Gold color

        # Draw gold/orange vertical border
        painter.fillRect(left_pattern_width, 0, 8, height, gold_color)

        # Add a subtle shadow effect to the border
        shadow_width = 5
        shadow_gradient = QLinearGradient(left_pattern_width + 8, 0, left_pattern_width + 8 + shadow_width, 0)
        shadow_gradient.setColorAt(0, QColor(0, 0, 0, 50))  # Semi-transparent black
        shadow_gradient.setColorAt(1, QColor(0, 0, 0, 0))   # Transparent
        painter.fillRect(left_pattern_width + 8, 0, shadow_width, height, shadow_gradient)

        # Draw more elegant gold corner decorations in top-right and bottom-right
        corner_size = int(width * 0.15)
        pen = QPen(gold_color, 2)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)

        # Top-right corner decoration (more ornate)
        margin = 20
        # Main corner lines
        painter.drawLine(width - corner_size, margin, width - margin, margin)
        painter.drawLine(width - margin, margin, width - margin, corner_size)

        # Additional decorative elements
        # Small diagonal flourish
        painter.drawLine(width - corner_size + 15, margin + 15, width - margin - 15, margin + 15)
        painter.drawLine(width - margin - 15, margin + 15, width - margin - 15, corner_size - 15)

        # Small circle at the corner junction
        painter.setBrush(QBrush(gold_color))
        painter.drawEllipse(width - margin - 5, margin - 5, 10, 10)

        # Bottom-right corner decoration (mirror of top-right)
        # Main corner lines
        painter.drawLine(width - corner_size, height - margin, width - margin, height - margin)
        painter.drawLine(width - margin, height - margin, width - margin, height - corner_size)

        # Additional decorative elements
        # Small diagonal flourish
        painter.drawLine(width - corner_size + 15, height - margin - 15, width - margin - 15, height - margin - 15)
        painter.drawLine(width - margin - 15, height - margin - 15, width - margin - 15, height - corner_size + 15)

        # Small circle at the corner junction
        painter.drawEllipse(width - margin - 5, height - margin - 5, 10, 10)

        # Add subtle decorative elements to the top-left and bottom-left corners where the pattern meets the border
        painter.setPen(QPen(QColor(255, 255, 255, 80), 1))  # Semi-transparent white

        # Top-left corner
        painter.drawLine(left_pattern_width - 15, margin, left_pattern_width - 5, margin)
        painter.drawLine(left_pattern_width - 5, margin, left_pattern_width - 5, margin + 10)

        # Bottom-left corner
        painter.drawLine(left_pattern_width - 15, height - margin, left_pattern_width - 5, height - margin)
        painter.drawLine(left_pattern_width - 5, height - margin, left_pattern_width - 5, height - margin - 10)

        # Draw Arabic calligraphy at the top (Bismillah)
        calligraphy_y = 30
        calligraphy_height = int(height * 0.08)
        calligraphy_width = int(width * 0.6)
        calligraphy_x = left_pattern_width + 10 + (width - left_pattern_width - 20 - calligraphy_width) // 2

        # Draw decorative line under the calligraphy
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        line_y = calligraphy_y + calligraphy_height + 5
        painter.drawLine(calligraphy_x + calligraphy_width//4, line_y,
                        calligraphy_x + calligraphy_width - calligraphy_width//4, line_y)

        # Draw decorative ornament in the middle of the line
        ornament_size = 10
        ornament_x = calligraphy_x + calligraphy_width//2 - ornament_size//2
        painter.setPen(QPen(gold_color, 1))
        painter.drawEllipse(ornament_x, line_y - ornament_size//2, ornament_size, ornament_size)

        # Draw professional medal/seal with laurel leaves on the left side
        medal_x = left_pattern_width // 2
        medal_y = height // 2
        medal_size = int(left_pattern_width * 0.7)

        # Create a radial gradient for the medal background
        medal_gradient = QRadialGradient(medal_x, medal_y, medal_size/2)
        medal_gradient.setColorAt(0, QColor(255, 215, 0))      # Bright gold at center
        medal_gradient.setColorAt(0.7, QColor(218, 165, 32))   # Gold
        medal_gradient.setColorAt(1, QColor(184, 134, 11))     # Darker gold at edge

        # Draw the circular medal background with gradient
        painter.setBrush(QBrush(medal_gradient))
        painter.setPen(QPen(QColor(150, 120, 0), 2))  # Darker gold border
        painter.drawEllipse(medal_x - medal_size//2, medal_y - medal_size//2, medal_size, medal_size)

        # Add a highlight effect (light reflection)
        highlight_path = QPainterPath()
        highlight_path.addEllipse(medal_x - medal_size//4, medal_y - medal_size//4, medal_size//2, medal_size//2)
        painter.setBrush(QBrush(QColor(255, 255, 255, 40)))  # Semi-transparent white
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawPath(highlight_path)

        # Draw inner circle with gradient (darker blue to match the theme)
        inner_gradient = QRadialGradient(medal_x, medal_y, (medal_size-20)/2)
        inner_gradient.setColorAt(0, QColor(30, 50, 100))    # Lighter navy blue
        inner_gradient.setColorAt(1, QColor(10, 30, 70))     # Darker navy blue

        painter.setBrush(QBrush(inner_gradient))
        painter.setPen(QPen(QColor(218, 165, 32), 1))  # Thin gold border
        painter.drawEllipse(medal_x - medal_size//2 + 10, medal_y - medal_size//2 + 10,
                           medal_size - 20, medal_size - 20)

        # Draw a more formal and professional emblem in the center
        painter.setPen(QPen(gold_color, 1))

        # Create a circular emblem with concentric circles
        emblem_size = medal_size // 3

        # Outer circle with gold color
        painter.setBrush(QBrush(gold_color))
        painter.drawEllipse(medal_x - emblem_size/2, medal_y - emblem_size/2, emblem_size, emblem_size)

        # Inner circle with lighter color
        inner_emblem_size = emblem_size * 0.7
        painter.setBrush(QBrush(QColor(240, 240, 240)))  # Light color
        painter.drawEllipse(medal_x - inner_emblem_size/2, medal_y - inner_emblem_size/2,
                           inner_emblem_size, inner_emblem_size)

        # Draw a formal shield or crest shape inside
        painter.setPen(QPen(gold_color, 1.5))
        painter.setBrush(QBrush(QColor(30, 50, 100)))  # Navy blue

        # Create a shield shape
        shield_width = inner_emblem_size * 0.6
        shield_height = inner_emblem_size * 0.7

        shield_path = QPainterPath()
        shield_path.moveTo(medal_x, medal_y - shield_height/2)  # Top center

        # Top right curve
        shield_path.cubicTo(
            medal_x + shield_width/4, medal_y - shield_height/2,  # Control point 1
            medal_x + shield_width/2, medal_y - shield_height/4,  # Control point 2
            medal_x + shield_width/2, medal_y                    # End point (right middle)
        )

        # Bottom right curve
        shield_path.cubicTo(
            medal_x + shield_width/2, medal_y + shield_height/4,  # Control point 1
            medal_x + shield_width/4, medal_y + shield_height/2,  # Control point 2
            medal_x, medal_y + shield_height/2                   # End point (bottom center)
        )

        # Bottom left curve (mirror of right side)
        shield_path.cubicTo(
            medal_x - shield_width/4, medal_y + shield_height/2,  # Control point 1
            medal_x - shield_width/2, medal_y + shield_height/4,  # Control point 2
            medal_x - shield_width/2, medal_y                    # End point (left middle)
        )

        # Top left curve
        shield_path.cubicTo(
            medal_x - shield_width/2, medal_y - shield_height/4,  # Control point 1
            medal_x - shield_width/4, medal_y - shield_height/2,  # Control point 2
            medal_x, medal_y - shield_height/2                   # End point (back to top center)
        )

        painter.drawPath(shield_path)

        # Add decorative horizontal lines inside the shield
        painter.setPen(QPen(gold_color, 0.8))
        line_spacing = shield_height / 5
        for i in range(1, 4):
            y_pos = medal_y - shield_height/2 + i * line_spacing
            # Calculate x position at this y level (shield gets narrower toward top and bottom)
            # This is an approximation of the shield's width at this y level
            width_factor = 1.0 - abs(i - 2.0) / 3.0
            x_width = shield_width/2 * width_factor
            painter.drawLine(medal_x - x_width, y_pos, medal_x + x_width, y_pos)

        # Add a small circle or dot in the center
        center_size = shield_width * 0.15
        painter.setBrush(QBrush(gold_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(medal_x - center_size/2, medal_y - center_size/2, center_size, center_size)

        # Draw elegant laurel leaves around the medal
        painter.setPen(QPen(gold_color, 1.5))
        leaf_length = medal_size // 2.5

        # Draw several leaves around the medal
        for angle in range(0, 360, 20):  # More leaves (every 20 degrees)
            rad_angle = angle * 3.14159 / 180
            start_x = medal_x + int((medal_size//2) * 0.9 * math.cos(rad_angle))
            start_y = medal_y + int((medal_size//2) * 0.9 * math.sin(rad_angle))
            end_x = medal_x + int((medal_size//2 + leaf_length) * math.cos(rad_angle))
            end_y = medal_y + int((medal_size//2 + leaf_length) * math.sin(rad_angle))

            # Draw a curved leaf
            path = QPainterPath()
            path.moveTo(start_x, start_y)

            # Control point for the curve
            ctrl_angle = rad_angle + 0.2
            ctrl_x = medal_x + int((medal_size//2 + leaf_length * 0.7) * math.cos(ctrl_angle))
            ctrl_y = medal_y + int((medal_size//2 + leaf_length * 0.7) * math.sin(ctrl_angle))

            path.quadTo(ctrl_x, ctrl_y, end_x, end_y)
            painter.drawPath(path)

            # Add a second curve for more detailed leaves
            if angle % 40 == 0:  # Only for some leaves
                ctrl_angle2 = rad_angle - 0.2
                ctrl_x2 = medal_x + int((medal_size//2 + leaf_length * 0.6) * math.cos(ctrl_angle2))
                ctrl_y2 = medal_y + int((medal_size//2 + leaf_length * 0.6) * math.sin(ctrl_angle2))

                path2 = QPainterPath()
                path2.moveTo(start_x, start_y)
                path2.quadTo(ctrl_x2, ctrl_y2, end_x, end_y)
                painter.drawPath(path2)

        # Logo
        if self.logo_path and os.path.exists(self.logo_path):
            logo_pixmap = QPixmap(self.logo_path)
            if not logo_pixmap.isNull():
                logo_max_width = int(content_width * 0.20) # Logo takes up to 20% of content width
                logo_max_height = int(content_height * 0.15) # Logo takes up to 15% of content height

                scaled_logo = logo_pixmap.scaled(logo_max_width, logo_max_height,
                                                 Qt.AspectRatioMode.KeepAspectRatio,
                                                 Qt.TransformationMode.SmoothTransformation)
                # Position logo in the top-right area
                logo_x = width - logo_max_width - 20
                logo_y = 20
                painter.drawPixmap(logo_x, logo_y, scaled_logo)
            else:
                print(f"Failed to load logo: {self.logo_path}")

        # --- Text Styling ---
        painter.setRenderHint(QPainter.RenderHint.Antialiasing) # For smoother text

        # Common text color
        text_color = QColor("#333333") # Dark Gray

        # Company name
        if company_name:
            company_font = QFont("Arial", 20)
            company_font.setBold(True)
            painter.setFont(company_font)
            painter.setPen(QColor("#005A9C"))
            company_rect = QRectF(left_pattern_width + 20, margin_y + int(content_height * 0.08),
                                 width - left_pattern_width - 40, int(content_height * 0.08))
            painter.drawText(company_rect, Qt.AlignmentFlag.AlignCenter, company_name)

        # Title: "شهادة مشاركة"
        title_font = QFont("Arial", 36)
        title_font.setBold(True)  # Set bold style
        painter.setFont(title_font)
        painter.setPen(gold_color) # Use gold color for title
        title_text = "شهادة مشاركة"

        # Adjust title position if company name is present
        title_y_position = margin_y + int(content_height * (0.15 if not company_name else 0.20))
        title_rect = QRectF(left_pattern_width + 20, title_y_position,
                           width - left_pattern_width - 40, int(content_height * 0.15))
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, title_text)

        # Body Font
        body_font_size = 18
        body_font = QFont("Arial", body_font_size) # Or "Tahoma"

        # Line Spacing (approximate)
        line_height = body_font_size * 2.5 # Adjust as needed

        current_y = margin_y + int(content_height * 0.35) # Start below title

        # Text: Certificate intro text (customizable)
        painter.setFont(body_font)
        painter.setPen(text_color)
        # Use default intro text if none provided
        if not intro_text:
            intro_text = "تشهد أكاديمية التطوير الاحترافي بأن"
        intro_rect = QRectF(left_pattern_width + 20, current_y, width - left_pattern_width - 40, line_height)
        painter.drawText(intro_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, intro_text)
        current_y += line_height

        # Student Name
        student_name_font = QFont("Arial", 28)
        student_name_font.setBold(True)  # Set bold style
        painter.setFont(student_name_font)
        painter.setPen(QColor("#003366")) # Dark Blue for name
        student_name_rect = QRectF(left_pattern_width + 20, current_y, width - left_pattern_width - 40, line_height * 1.2)
        painter.drawText(student_name_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, student_name)
        current_y += line_height * 1.2

        # Draw decorative line under student name
        painter.setPen(QPen(gold_color, 1, Qt.PenStyle.DotLine))
        line_y = student_name_rect.y() + student_name_rect.height() * 0.8
        line_width = (width - left_pattern_width - 40) * 0.6
        line_x = left_pattern_width + 20 + (width - left_pattern_width - 40 - line_width) / 2
        painter.drawLine(int(line_x), int(line_y), int(line_x + line_width), int(line_y))

        # Text: "قد أتم بنجاح دورة"
        painter.setFont(body_font)
        painter.setPen(text_color)
        completion_text = "قد أتم بنجاح دورة تدريبية بعنوان:"
        completion_rect = QRectF(left_pattern_width + 20, current_y, width - left_pattern_width - 40, line_height)
        painter.drawText(completion_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, completion_text)
        current_y += line_height

        # Course Name
        course_name_font = QFont("Arial", 24)
        course_name_font.setWeight(QFont.Weight.DemiBold)  # Set demibold weight
        painter.setFont(course_name_font)
        painter.setPen(QColor("#D2691E")) # Chocolate color for course name
        course_name_rect = QRectF(left_pattern_width + 20, current_y, width - left_pattern_width - 40, line_height * 1.1)
        painter.drawText(course_name_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, course_name)
        current_y += line_height * 1.1

        # Text: "مع المدرب"
        painter.setFont(body_font)
        painter.setPen(text_color)
        instructor_intro_text = "تحت إشراف المدرب:"
        instructor_intro_rect = QRectF(left_pattern_width + 20, current_y, width - left_pattern_width - 40, line_height)
        painter.drawText(instructor_intro_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, instructor_intro_text)
        current_y += line_height

        # Instructor Name
        instructor_font = QFont("Arial", 20)
        painter.setFont(instructor_font)
        painter.setPen(text_color) # Standard text color
        instructor_name_rect = QRectF(left_pattern_width + 20, current_y, width - left_pattern_width - 40, line_height)
        painter.drawText(instructor_name_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, instructor_name)
        current_y += line_height * 1.5 # More space before date

        # Date and signature area
        # Draw two signature lines at the bottom
        painter.setPen(QPen(QColor("#333333"), 1))

        # Date on the right side
        date_font = QFont("Arial", 16)
        painter.setFont(date_font)
        date_rect = QRectF(width - 200, height - 100, 180, 30)
        date_str = self.format_arabic_date(issue_date)
        painter.drawText(date_rect, Qt.AlignmentFlag.AlignCenter, f"التاريخ: {date_str}")

        # Draw signature line on the left
        sig_x = width - 250
        sig_y = height - 150
        sig_width = 200
        painter.drawLine(sig_x, sig_y, sig_x + sig_width, sig_y)

        # Draw "signature" text under the line
        sig_text_rect = QRectF(sig_x, sig_y + 5, sig_width, 20)
        painter.drawText(sig_text_rect, Qt.AlignmentFlag.AlignCenter, "الإمضاء")

        # Footer (Optional - e.g., Certificate id or a small note)
        footer_font = QFont("Arial", 10)
        footer_font.setItalic(True)  # Set italic style
        painter.setFont(footer_font)
        painter.setPen(QColor("#666666")) # Lighter gray
        footer_text = "تتزاحم الكلمات وتتسابق العبارات, ولم نجد أنسب من هذه الشهادة لنقدمها تقديرا منا على مجهودك الذي بذلته."
        footer_rect = QRectF(left_pattern_width + 20, height - margin_y - 25, width - left_pattern_width - 40, 20)
        painter.drawText(footer_rect, Qt.AlignmentFlag.AlignCenter, footer_text)

        painter.end()

    # Helper for Arabic date
    def format_arabic_date(self, q_date):
        """Format date with Arabic month names"""
        months_ar = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        day = q_date.day()
        month = months_ar[q_date.month() - 1]
        year = q_date.year()
        return f"{day} {month} {year}"


if __name__ == '__main__':
    app = QApplication(sys.argv)
    # Force Right-to-Left layout for the application
    # This affects the GUI elements' layout for proper Arabic display
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # Set application style for a more modern look
    app.setStyle("Fusion")

    # Apply a stylesheet for better visual appearance
    app.setStyleSheet("""
        QGroupBox {
            font-weight: bold;
            border: 1px solid #cccccc;
            border-radius: 6px;
            margin-top: 12px;
            padding-top: 12px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px;
        }

        QLabel {
            font-size: 12px;
        }

        QLineEdit, QDateEdit {
            padding: 8px;
            border: 1px solid #cccccc;
            border-radius: 4px;
        }

        QPushButton {
            padding: 8px 16px;
            border-radius: 4px;
        }
    """)

    # Create and show the application
    ex = CertificateApp()
    ex.show()

    # Start the event loop
    sys.exit(app.exec())