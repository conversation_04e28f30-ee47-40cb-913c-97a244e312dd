#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
التحقق من مشكلة MySQL definer في جدول المشاريع_المدفوعات
"""

import mysql.connector
from متغيرات import host, user, password

def investigate_definer_issue():
    """التحقق من المشكلة في definers"""
    print("🔍 التحقق من مشكلة MySQL Definer...")
    
    try:
        # الاتصال بقاعدة البيانات
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # 1. التحقق من الـ triggers
        print("\n📋 التحقق من الـ Triggers:")
        cursor.execute("""
            SELECT TRIGGER_NAME, DEFINER, EVENT_MANIPULATION, EVENT_OBJECT_TABLE 
            FROM information_schema.TRIGGERS 
            WHERE TRIGGER_SCHEMA = %s
        """, (db_name,))
        
        triggers = cursor.fetchall()
        if triggers:
            for trigger in triggers:
                trigger_name, definer, event, table = trigger
                print(f"  - Trigger: {trigger_name}")
                print(f"    Table: {table}")
                print(f"    Event: {event}")
                print(f"    Definer: {definer}")
                if 'root@%' in definer:
                    print(f"    ⚠️ مشكلة: Definer غير صحيح!")
                print()
        else:
            print("  لا توجد triggers")
        
        # 2. التحقق من الـ views
        print("\n📋 التحقق من الـ Views:")
        cursor.execute("""
            SELECT TABLE_NAME, DEFINER 
            FROM information_schema.VIEWS 
            WHERE TABLE_SCHEMA = %s
        """, (db_name,))
        
        views = cursor.fetchall()
        if views:
            for view in views:
                view_name, definer = view
                print(f"  - View: {view_name}")
                print(f"    Definer: {definer}")
                if 'root@%' in definer:
                    print(f"    ⚠️ مشكلة: Definer غير صحيح!")
                print()
        else:
            print("  لا توجد views")
        
        # 3. التحقق من الـ stored procedures
        print("\n📋 التحقق من الـ Stored Procedures:")
        cursor.execute("""
            SELECT ROUTINE_NAME, DEFINER, ROUTINE_TYPE 
            FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = %s
        """, (db_name,))
        
        routines = cursor.fetchall()
        if routines:
            for routine in routines:
                routine_name, definer, routine_type = routine
                print(f"  - {routine_type}: {routine_name}")
                print(f"    Definer: {definer}")
                if 'root@%' in definer:
                    print(f"    ⚠️ مشكلة: Definer غير صحيح!")
                print()
        else:
            print("  لا توجد stored procedures")
        
        # 4. التحقق من structure جدول المشاريع_المدفوعات
        print("\n📋 هيكل جدول المشاريع_المدفوعات:")
        cursor.execute("SHOW CREATE TABLE المشاريع_المدفوعات")
        create_table = cursor.fetchone()
        if create_table:
            print(create_table[1])
        
        # 5. التحقق من المستخدمين الموجودين
        print("\n👥 المستخدمين الموجودين في MySQL:")
        cursor.execute("SELECT User, Host FROM mysql.user WHERE User = 'root'")
        root_users = cursor.fetchall()
        print("Root users:")
        for user_info in root_users:
            user_name, host_name = user_info
            print(f"  - {user_name}@{host_name}")
        
        # 6. محاولة إنشاء المستخدم المفقود
        print("\n🔧 محاولة حل المشكلة...")
        try:
            # التحقق من وجود root@%
            cursor.execute("SELECT COUNT(*) FROM mysql.user WHERE User = 'root' AND Host = '%'")
            root_wildcard_exists = cursor.fetchone()[0]
            
            if root_wildcard_exists == 0:
                print("❌ المستخدم root@% غير موجود")
                print("💡 الحلول المقترحة:")
                print("   1. إنشاء المستخدم root@% (يتطلب صلاحيات عالية)")
                print("   2. تغيير definer للكائنات المتأثرة")
                print("   3. إعادة إنشاء الجداول والكائنات")
            else:
                print("✅ المستخدم root@% موجود")
                
        except mysql.connector.Error as e:
            print(f"⚠️ لا يمكن التحقق من جدول mysql.user: {e}")
        
        conn.close()
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        return False

def try_fix_definer_issue():
    """محاولة حل مشكلة الـ definer"""
    print("\n🔧 محاولة حل مشكلة الـ Definer...")
    
    try:
        # الاتصال كـ root للحصول على صلاحيات أعلى
        from for_all import host, user_r, password_r
        
        conn = mysql.connector.connect(
            host=host, 
            user=user_r, 
            password=password_r,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال كـ root")
        
        # محاولة إنشاء المستخدم root@% إذا لم يكن موجوداً
        try:
            cursor.execute("CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'kh123456'")
            cursor.execute("GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION")
            cursor.execute("FLUSH PRIVILEGES")
            print("✅ تم إنشاء المستخدم root@% بنجاح")
        except mysql.connector.Error as e:
            print(f"⚠️ لم يتم إنشاء المستخدم: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ فشل في حل المشكلة: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("تحليل مشكلة MySQL Definer Error")
    print("=" * 60)
    
    # تحليل المشكلة
    investigate_definer_issue()
    
    # محاولة الحل
    try_fix_definer_issue()
    
    print("\n" + "=" * 60)
    print("انتهى التحليل")
    print("=" * 60)
