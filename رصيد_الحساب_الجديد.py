from for_all import*

class AccountBalanceDialog(QDialog):
    """نافذة إدارة رصيد الحساب الجديدة"""
    
    def __init__(self, main_window, employee_data=None):
        super().__init__(main_window)
        self.main_window = main_window
        self.employee_data = employee_data
        self.setup_ui()
        self.setup_connections()
        
        # إذا تم تمرير بيانات موظف، قم بتحديد الموظف مسبقاً
        if employee_data:
            self.set_employee_data(employee_data)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم بنفس هيكلية النافذة السابقة"""
        self.setWindowTitle("رصيد الموظف")
        self.resize(900, 650)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)

        # التخطيط الرئيسي
        gridLayout_2 = QGridLayout(self)
        gridLayout_2.setSizeConstraint(QLayout.SetDefaultConstraint)
        gridLayout_2.setContentsMargins(10, 10, 10, 10)
        gridLayout_2.setSpacing(10)

        gridLayout = QGridLayout()
        gridLayout.setSizeConstraint(QLayout.SetMinimumSize)
        gridLayout.setContentsMargins(0, 0, 0, 0)

        # اسم الموظف
        employee_name_label = QLabel("اسم الموظف")
        employee_name_label.setMinimumSize(QSize(50, 40))
        employee_name_label.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(employee_name_label, 0, 0, 1, 1)

        self.employee_name_lineEdit = QLineEdit()
        self.employee_name_lineEdit.setReadOnly(True)
        self.employee_name_lineEdit.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(self.employee_name_lineEdit, 0, 1, 1, 1)

        # نوع الحساب (التصنيف)
        account_type_label = QLabel("نوع الحساب")
        account_type_label.setMinimumSize(QSize(50, 40))
        account_type_label.setLayoutDirection(Qt.RightToLeft)
        account_type_label.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(account_type_label, 1, 0, 1, 1)

        self.account_type_lineEdit = QLineEdit()
        self.account_type_lineEdit.setMinimumSize(QSize(0, 40))
        self.account_type_lineEdit.setLayoutDirection(Qt.RightToLeft)
        self.account_type_lineEdit.setAlignment(Qt.AlignCenter)
        self.account_type_lineEdit.setPlaceholderText("ادخل نوع الحساب")
        gridLayout.addWidget(self.account_type_lineEdit, 1, 1, 1, 1)

        # نوع العملية (جديد)
        operation_label = QLabel("نوع العملية")
        operation_label.setMinimumSize(QSize(50, 40))
        operation_label.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(operation_label, 2, 0, 1, 1)

        self.operation_combo = QComboBox()
        self.operation_combo.setMinimumSize(QSize(0, 40))
        self.operation_combo.addItems(["إيداع", "سحب"])
        gridLayout.addWidget(self.operation_combo, 2, 1, 1, 1)

        # المبلغ
        amount_label = QLabel("المبلغ")
        amount_label.setMinimumSize(QSize(50, 40))
        amount_label.setLayoutDirection(Qt.RightToLeft)
        amount_label.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(amount_label, 3, 0, 1, 1)

        self.amount_input = QLineEdit()
        self.amount_input.setMinimumSize(QSize(0, 40))
        self.amount_input.setLayoutDirection(Qt.RightToLeft)
        self.amount_input.setAlignment(Qt.AlignCenter)
        self.amount_input.setPlaceholderText("يجب إدخال المبلغ")
        gridLayout.addWidget(self.amount_input, 3, 1, 1, 1)

        # الوصف
        description_label = QLabel("الوصف")
        description_label.setMinimumSize(QSize(0, 40))
        description_label.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(description_label, 4, 0, 1, 1)

        self.description_input = QLineEdit()
        self.description_input.setMinimumSize(QSize(0, 40))
        self.description_input.setAlignment(Qt.AlignCenter)
        self.description_input.setPlaceholderText("الوصف (إختياري)")
        gridLayout.addWidget(self.description_input, 4, 1, 1, 1)

        # النسبة
        self.rate_label = QLabel("النسبة")
        self.rate_label.setMinimumSize(QSize(0, 40))
        self.rate_label.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(self.rate_label, 5, 0, 1, 1)

        self.rate_input = QLineEdit()
        self.rate_input.setMinimumSize(QSize(0, 40))
        self.rate_input.setAlignment(Qt.AlignCenter)
        self.rate_input.setPlaceholderText("إدخل النسبة عند اختيار نوع المعاملة (اضافة نسبة)")
        gridLayout.addWidget(self.rate_input, 5, 1, 1, 1)

        # التاريخ
        date_label = QLabel("التاريخ")
        date_label.setMinimumSize(QSize(0, 40))
        date_label.setAlignment(Qt.AlignCenter)
        gridLayout.addWidget(date_label, 6, 0, 1, 1)

        self.date_input = QDateEdit()
        self.date_input.setMinimumSize(QSize(0, 40))
        self.date_input.setAlignment(Qt.AlignCenter)
        self.date_input.setCalendarPopup(True)
        self.date_input.setDisplayFormat("dd/MM/yyyy")
        self.date_input.setDate(QDate.currentDate())
        gridLayout.addWidget(self.date_input, 6, 1, 1, 1)

        # نوع المعاملة
        self.transaction_combo = QComboBox()
        self.transaction_combo.setMinimumSize(QSize(50, 40))
        gridLayout.addWidget(self.transaction_combo, 7, 0, 1, 1)

        # زر إضافة المعاملة
        self.save_btn = QPushButton("إضافة المعاملة")
        self.save_btn.setMinimumSize(QSize(0, 40))
        self.save_btn.setLayoutDirection(Qt.RightToLeft)
        self.save_btn.clicked.connect(self.save_transaction)
        gridLayout.addWidget(self.save_btn, 7, 1, 1, 1)

        # قسم الجدولة التلقائية (جديد)
        self.scheduling_group = QGroupBox("الجدولة التلقائية للمرتب")
        scheduling_layout = QHBoxLayout(self.scheduling_group)

        self.enable_scheduling_check = QCheckBox("تفعيل الجدولة التلقائية")
        scheduling_layout.addWidget(self.enable_scheduling_check)

        scheduling_layout.addWidget(QLabel("نوع الجدولة:"))
        self.scheduling_type_combo = QComboBox()
        self.scheduling_type_combo.addItems([
            "شهري في نفس التاريخ",
            "كل 30 يوم",
            "إدراج يدوي"
        ])
        scheduling_layout.addWidget(self.scheduling_type_combo)
        scheduling_layout.addStretch()

        # إخفاء قسم الجدولة افتراضياً
        self.scheduling_group.setVisible(False)
        gridLayout.addWidget(self.scheduling_group, 8, 0, 1, 2)

        # جدول المعاملات
        self.tableWidget = QTableWidget()
        headers = ["الرقم", "نوع المعاملة", "المبلغ", "التاريخ", "النسبة", "الوصف", "id"]
        self.tableWidget.setColumnCount(len(headers))
        self.tableWidget.setHorizontalHeaderLabels(headers)
        gridLayout.addWidget(self.tableWidget, 9, 0, 1, 2)

        # أزرار الحذف والطباعة
        delete_button = QPushButton("حذف معاملة")
        delete_button.setMinimumSize(QSize(0, 40))
        delete_button.setLayoutDirection(Qt.RightToLeft)
        delete_button.clicked.connect(self.delete_selected_row)
        gridLayout.addWidget(delete_button, 10, 0, 1, 1)

        print_button = QPushButton("طباعة فاتورة")
        print_button.setMinimumSize(QSize(0, 40))
        print_button.setLayoutDirection(Qt.RightToLeft)
        print_button.clicked.connect(self.print_report)
        gridLayout.addWidget(print_button, 10, 1, 1, 1)

        gridLayout_2.addLayout(gridLayout, 0, 1, 1, 1)

        # تحميل البيانات الأولية
        self.load_employees()
        self.update_transaction_types()
    
    def setup_connections(self):
        """إعداد الاتصالات بين العناصر"""
        self.operation_combo.currentTextChanged.connect(self.update_transaction_types)
        self.transaction_combo.currentTextChanged.connect(self.on_transaction_type_changed)
        self.transaction_combo.currentTextChanged.connect(self.load_data_for_transaction_type)
        self.enable_scheduling_check.toggled.connect(self.on_scheduling_toggled)
        self.account_type_lineEdit.textChanged.connect(self.load_reports)

    def load_employees(self):
        """تحميل بيانات الموظف المحدد"""
        if self.employee_data:
            self.account_type_lineEdit.setText(str(self.employee_data.get('التصنيف', '')))
            self.employee_name_lineEdit.setText(self.employee_data.get('اسم_الموظف', ''))
            self.load_reports()
    
    def update_transaction_types(self):
        """تحديث أنواع المعاملات حسب نوع العملية"""
        operation_type = self.operation_combo.currentText()
        self.transaction_combo.clear()
        
        if operation_type == "إيداع":
            self.transaction_combo.addItems([
                "إضافة رصيد",
                "إضافة مرتب", 
                "إضافة نسبة"
            ])
        elif operation_type == "سحب":
            self.transaction_combo.addItems([
                "سداد مبلغ",
                "خصم مبلغ"
            ])
    
    def on_transaction_type_changed(self):
        """عند تغيير نوع المعاملة"""
        transaction_type = self.transaction_combo.currentText()
        
        # إظهار قسم الجدولة فقط عند اختيار إضافة مرتب
        if transaction_type == "إضافة مرتب":
            self.scheduling_group.setVisible(True)
        else:
            self.scheduling_group.setVisible(False)
            self.enable_scheduling_check.setChecked(False)
    
    def on_scheduling_toggled(self, checked):
        """عند تفعيل/إلغاء الجدولة التلقائية"""
        self.scheduling_type_combo.setEnabled(checked)

    def set_employee_data(self, employee_data):
        """تحديد بيانات الموظف مسبقاً"""
        self.employee_data = employee_data

    def safe_start_transaction(self, conn):
        """بدء معاملة قاعدة بيانات بشكل آمن"""
        try:
            conn.start_transaction()
        except Exception:
            # إذا كانت هناك معاملة مفتوحة، قم بإنهائها أولاً
            try:
                conn.rollback()
            except:
                pass
            try:
                conn.start_transaction()
            except Exception as e:
                print(f"خطأ في بدء المعاملة: {e}")
                raise

    def load_reports(self):
        """تحميل تقارير الموظف"""
        account_type = self.account_type_lineEdit.text()
        if not account_type:
            return

        try:
            conn = self.main_window.get_db_connection(str(QDate.currentDate().year()))
            if not conn:
                return

            cursor = conn.cursor()

            # تحميل بيانات الموظف باستخدام التصنيف
            cursor.execute("SELECT * FROM الموظفين WHERE التصنيف=%s", (account_type,))
            employee = cursor.fetchone()
            if employee:
                self.employee_name_lineEdit.setText(str(employee[2]))  # اسم الموظف

            # تحميل التقارير من جدول الموظفين_معاملات_مالية أولاً، ثم من الموظفين_معاملات_مالية
            cursor.execute("""
                (SELECT m.id, m.نوع_المعاملة, m.المبلغ, m.التاريخ,
                        COALESCE(m.النسبة, 0) as النسبة, m.الوصف, 'الموظفين_معاملات_مالية' as المصدر
                 FROM الموظفين_معاملات_مالية m
                 JOIN الموظفين e ON m.معرف_الموظف = e.id
                 WHERE e.التصنيف = %s)
                UNION ALL
                (SELECT r.id, r.نوع_المعاملة, r.المبلغ, r.التاريخ,
                        COALESCE(r.النسبة, 0) as النسبة, r.الوصف, 'الموظفين_معاملات_مالية' as المصدر
                 FROM الموظفين_معاملات_مالية r
                 JOIN الموظفين e ON r.معرف_الموظف = e.id
                 WHERE e.التصنيف = %s)
                ORDER BY التاريخ DESC
            """, (account_type, account_type))
            reports = cursor.fetchall()

            self.tableWidget.setRowCount(0)
            for row_number, row_data in enumerate(reports):
                self.tableWidget.insertRow(row_number)

                # الرقم
                item_number = QTableWidgetItem(str(row_number + 1))
                item_number.setTextAlignment(Qt.AlignCenter)
                self.tableWidget.setItem(row_number, 0, item_number)

                # باقي البيانات
                for i in range(1, len(row_data)):
                    item = QTableWidgetItem(str(row_data[i]))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.tableWidget.setItem(row_number, i, item)

                # id مخفي
                hidden_معرف_item = QTableWidgetItem(str(row_data[0]))
                self.tableWidget.setItem(row_number, 6, hidden_معرف_item)

            self.tableWidget.setColumnHidden(6, True)
            self.colorize_table()

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def colorize_table(self):
        """تلوين الجدول حسب نوع المعاملة"""
        for row_idx in range(self.tableWidget.rowCount()):
            item = self.tableWidget.item(row_idx, 1)  # عمود نوع المعاملة
            if item:
                transaction_type = item.text().strip()
                if transaction_type in ["إضافة مرتب", "إضافة نسبة", "إضافة رصيد"]:
                    item.setForeground(QColor("#cdd7b9"))  # أخضر
                elif transaction_type in ["سداد مبلغ", "خصم مبلغ", "الغاء سحب"]:
                    item.setForeground(QColor("#dc8484"))  # أحمر

    def load_data_for_transaction_type(self):
        """تحميل البيانات حسب نوع المعاملة"""
        transaction_type = self.transaction_combo.currentText()
        account_type = self.account_type_lineEdit.text()

        if not account_type:
            return

        try:
            conn = self.main_window.get_db_connection(str(QDate.currentDate().year()))
            if not conn:
                return

            cursor = conn.cursor()
            cursor.execute("SELECT * FROM الموظفين WHERE التصنيف=%s", (account_type,))
            employee = cursor.fetchone()

            if employee:
                salary = employee[5] if employee[5] else 0
                rate = employee[6] if employee[6] else 0
                balance = employee[8] if employee[8] else 0

                if transaction_type == "إضافة مرتب":
                    self.amount_input.setText(str(salary))
                    self.rate_input.setText("0")
                    self.rate_label.hide()
                    self.rate_input.hide()
                elif transaction_type == "سداد مبلغ":
                    self.amount_input.setText(str(balance))
                    self.rate_input.setText("0")
                    self.rate_label.hide()
                    self.rate_input.hide()
                elif transaction_type == "إضافة نسبة":
                    self.rate_input.setText(str(rate))
                    self.rate_label.show()
                    self.rate_input.show()
                else:
                    self.rate_input.setText("0")
                    self.rate_label.hide()
                    self.rate_input.hide()

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        # التحقق من نوع الحساب
        if not self.account_type_lineEdit.text():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال نوع الحساب")
            return False

        # التحقق من المبلغ
        try:
            amount_text = self.amount_input.text()
            if not amount_text:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ")
                return False

            amount = float(amount_text)
            if amount <= 0:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح أكبر من الصفر")
                return False

        except ValueError:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")
            return False

        # التحقق من النسبة في حالة إضافة نسبة
        transaction_type = self.transaction_combo.currentText()
        if transaction_type == "إضافة نسبة":
            try:
                rate = float(self.rate_input.text())
                if rate <= 0:
                    QMessageBox.warning(self, "خطأ", "يرجى إدخال نسبة صحيحة أكبر من الصفر")
                    return False
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال نسبة صحيحة")
                return False

        return True

    def save_transaction_alternative(self):
        """حفظ المعاملة بطريقة بديلة تتجنب التريجرز"""
        if not self.validate_input():
            return

        try:
            # جمع البيانات
            account_type = self.account_type_lineEdit.text()
            employee_name = self.employee_name_lineEdit.text()
            operation_type = self.operation_combo.currentText()
            transaction_type = self.transaction_combo.currentText()
            amount = float(self.amount_input.text())
            description = self.description_input.text()
            transaction_date = self.date_input.date().toString("yyyy-MM-dd")
            rate = float(self.rate_input.text()) if self.rate_input.text() else 0

            # حساب المبلغ الفعلي في حالة النسبة
            if transaction_type == "إضافة نسبة":
                amount = int((amount / 100) * rate)

            # استخدام جدول الموظفين_معاملات_مالية بدلاً من الموظفين_معاملات_مالية
            conn = self.main_window.get_db_connection(str(QDate.currentDate().year()))
            if not conn:
                QMessageBox.critical(self, "خطأ", "فشل في الاتصال بقاعدة البيانات")
                return

            cursor = conn.cursor()

            # الحصول على بيانات الموظف باستخدام التصنيف
            cursor.execute("SELECT * FROM الموظفين WHERE التصنيف=%s", (account_type,))
            employee = cursor.fetchone()

            if not employee:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على موظف بهذا النوع من الحساب")
                return

            employee_id = employee[0]
            current_balance = int(employee[8]) if employee[8] else 0
            withdrawal = int(employee[9]) if employee[9] else 0

            # رسالة التأكيد
            reply = QMessageBox.question(
                self, "تأكيد المعاملة",
                f"هل تريد حفظ المعاملة التالية؟\n"
                f"نوع الحساب: {account_type}\n"
                f"اسم الموظف: {employee_name}\n"
                f"نوع المعاملة: {transaction_type}\n"
                f"المبلغ: {int(amount)}",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.No:
                return

            # بدء معاملة قاعدة بيانات
            self.safe_start_transaction(conn)

            # إدراج في جدول الموظفين_معاملات_مالية (بدلاً من الموظفين_معاملات_مالية)
            cursor.execute(''' 
                INSERT INTO الموظفين_معاملات_مالية (معرف_الموظف, التصنيف, اسم_الموظف, الوظيفة, نوع_المعاملة, المبلغ, التاريخ, النسبة, الوصف, المستخدم)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', (employee_id, account_type, employee[2], employee[3], transaction_type, amount, transaction_date, rate, description, 'النظام'))

            # حساب الرصيد الجديد وتحديث جدول الموظفين
            if transaction_type == "إضافة رصيد":
                new_balance = current_balance + amount
            elif transaction_type == "سداد مبلغ":
                if current_balance < amount:
                    reply = QMessageBox.warning(
                        self, "تنبيه",
                        "رصيد الموظف أقل من مبلغ السداد، هل تريد الاستمرار؟",
                        QMessageBox.Yes | QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        conn.rollback()
                        return
                new_balance = current_balance - amount
            elif transaction_type == "إضافة مرتب":
                new_balance = current_balance + amount
            elif transaction_type == "إضافة نسبة":
                new_balance = current_balance + amount
            elif transaction_type == "خصم مبلغ":
                new_balance = current_balance - amount
            else:
                new_balance = current_balance

            # تحديث رصيد الموظف
            cursor.execute("UPDATE الموظفين SET الرصيد=%s WHERE id=%s", (new_balance, employee_id))

            # إنشاء جدولة تلقائية للمرتب إذا كان مطلوباً
            if transaction_type == "إضافة مرتب" and self.enable_scheduling_check.isChecked():
                self.create_salary_schedule(cursor, employee_id, employee[2], amount, transaction_date)

            conn.commit()

            QMessageBox.information(
                self, "نجح",
                f"تم حفظ المعاملة بنجاح\n"
                f"المعاملة: {transaction_type}\n"
                f"المبلغ: {int(amount)}\n"
                f"الرصيد الجديد: {int(new_balance)}"
            )

            # تحديث الجدول
            self.load_reports()

            # مسح الحقول (عدا نوع الحساب واسم الموظف)
            self.amount_input.clear()
            self.description_input.clear()
            self.rate_input.setText("0")

        except Exception as e:
            if 'conn' in locals():
                try:
                    conn.rollback()
                except:
                    pass

            error_msg = str(e)
            if "Transaction already in progress" in error_msg:
                error_msg = "خطأ في معاملة قاعدة البيانات. يرجى المحاولة مرة أخرى."
            elif "Connection" in error_msg:
                error_msg = "خطأ في الاتصال بقاعدة البيانات. تحقق من الاتصال."
            elif "trigger" in error_msg.lower():
                error_msg = "تم تجنب مشكلة التريجر. المعاملة محفوظة في جدول منفصل."
            else:
                error_msg = f"فشل في حفظ المعاملة: {error_msg}"

            QMessageBox.critical(self, "خطأ", error_msg)
        finally:
            if 'cursor' in locals():
                try:
                    cursor.close()
                except:
                    pass
            if 'conn' in locals():
                try:
                    conn.close()
                except:
                    pass

    def save_transaction(self):
        """حفظ المعاملة - يستخدم الطريقة البديلة لتجنب مشاكل التريجر"""
        return self.save_transaction_alternative()

    def create_salary_schedule(self, cursor, employee_id, employee_name, salary_amount, start_date):
        """إنشاء جدولة تلقائية للمرتب"""
        try:
            scheduling_type_text = self.scheduling_type_combo.currentText()

            # تحويل نص نوع الجدولة إلى قيمة قاعدة البيانات
            if scheduling_type_text == "شهري في نفس التاريخ":
                scheduling_type = "شهري_نفس_التاريخ"
            elif scheduling_type_text == "كل 30 يوم":
                scheduling_type = "كل_30_يوم"
            else:
                scheduling_type = "يدوي"

            # حساب تاريخ الإيداع التالي
            start_date_obj = QDate.fromString(start_date, Qt.ISODate)
            if scheduling_type == "شهري_نفس_التاريخ":
                next_date = start_date_obj.addMonths(1)
            elif scheduling_type == "كل_30_يوم":
                next_date = start_date_obj.addDays(30)
            else:
                next_date = None

            next_date_str = next_date.toString(Qt.ISODate) if next_date else None

            # إدراج الجدولة
            cursor.execute("""
                INSERT INTO الموظفين_جدولة_المرتبات
                (معرف_الموظف, اسم_الموظف, مبلغ_المرتب, نوع_الجدولة, تاريخ_البداية,
                 تاريخ_آخر_إيداع, تاريخ_الإيداع_التالي, عدد_الإيداعات_المنجزة, المستخدم)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (employee_id, employee_name, salary_amount, scheduling_type, start_date,
                  start_date, next_date_str, 1, 'النظام'))

            return cursor.lastrowid

        except Exception as e:
            print(f"خطأ في إنشاء الجدولة: {str(e)}")
            return None

    def delete_selected_row(self):
        """حذف المعاملة المحددة"""
        current_row = self.tableWidget.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "خطأ", "يرجى تحديد معاملة للحذف")
            return

        # الحصول على id المعاملة
        transaction_معرف_item = self.tableWidget.item(current_row, 6)  # العمود المخفي
        if not transaction_معرف_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن تحديد المعاملة")
            return

        transaction_id = transaction_معرف_item.text()
        transaction_type = self.tableWidget.item(current_row, 1).text()
        amount = self.tableWidget.item(current_row, 2).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل تريد حذف المعاملة التالية؟\n"
            f"نوع المعاملة: {transaction_type}\n"
            f"المبلغ: {amount}",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.No:
            return

        try:
            conn = self.main_window.get_db_connection(str(QDate.currentDate().year()))
            if not conn:
                QMessageBox.critical(self, "خطأ", "فشل في الاتصال بقاعدة البيانات")
                return

            cursor = conn.cursor()

            # بدء معاملة قاعدة بيانات بشكل آمن
            self.safe_start_transaction(conn)

            # حذف من جدول تقارير الموظفين
            cursor.execute("DELETE FROM الموظفين_معاملات_مالية WHERE id = %s", (transaction_id,))

            # حذف من جدول معاملات الحساب إذا وجد
            cursor.execute("DELETE FROM الموظفين_معاملات_مالية WHERE id = %s", (transaction_id,))

            conn.commit()

            QMessageBox.information(self, "نجح", "تم حذف المعاملة بنجاح")

            # تحديث الجدول
            self.load_reports()

            cursor.close()
            conn.close()

        except Exception as e:
            if 'conn' in locals():
                try:
                    conn.rollback()
                except:
                    pass

            error_msg = str(e)
            if "Transaction already in progress" in error_msg:
                error_msg = "خطأ في معاملة قاعدة البيانات. يرجى المحاولة مرة أخرى."
            else:
                error_msg = f"فشل في حذف المعاملة: {error_msg}"

            QMessageBox.critical(self, "خطأ", error_msg)
        finally:
            if 'cursor' in locals():
                try:
                    cursor.close()
                except:
                    pass
            if 'conn' in locals():
                try:
                    conn.close()
                except:
                    pass

    def print_report(self):
        """طباعة التقرير"""
        try:
            # يمكن إضافة دالة الطباعة هنا
            QMessageBox.information(self, "طباعة", "سيتم تطوير ميزة الطباعة قريباً")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في الطباعة: {str(e)}")


def safe_start_transaction(conn):
    """بدء معاملة قاعدة بيانات بشكل آمن - دالة عامة"""
    try:
        conn.start_transaction()
    except Exception:
        try:
            conn.rollback()
        except:
            pass
        try:
            conn.start_transaction()
        except Exception as e:
            print(f"خطأ في بدء المعاملة: {e}")
            raise


def open_new_account_balance_dialog(main_window, employee_data=None):
    """فتح نافذة إدارة رصيد الحساب الجديدة"""
    dialog = AccountBalanceDialog(main_window, employee_data)
    return dialog.exec()


def process_scheduled_salaries(main_window):
    """معالجة المرتبات المجدولة تلقائياً"""
    try:
        conn = main_window.get_db_connection(str(QDate.currentDate().year()))
        if not conn:
            return

        cursor = conn.cursor(dictionary=True)
        today = QDate.currentDate().toString(Qt.ISODate)

        # البحث عن المرتبات المستحقة
        cursor.execute("""
            SELECT * FROM الموظفين_جدولة_المرتبات
            WHERE نشط = TRUE
            AND تاريخ_الإيداع_التالي <= %s
            AND نوع_الجدولة != 'يدوي'
        """, (today,))

        due_salaries = cursor.fetchall()

        if not due_salaries:
            return

        processed_count = 0

        for salary in due_salaries:
            try:
                # بدء معاملة قاعدة بيانات بشكل آمن
                safe_start_transaction(conn)

                # الحصول على الرصيد الحالي للموظف
                cursor.execute("SELECT الرصيد FROM الموظفين WHERE id = %s", (salary['معرف_الموظف'],))
                current_balance_result = cursor.fetchone()
                if not current_balance_result:
                    continue

                current_balance = current_balance_result['الرصيد']
                new_balance = current_balance + salary['مبلغ_المرتب']

                # إدراج المعاملة
                cursor.execute("""
                    INSERT INTO الموظفين_معاملات_مالية
                    (معرف_الموظف, اسم_الموظف, نوع_العملية, نوع_المعاملة, المبلغ, التاريخ,
                     الوصف, الرصيد_قبل_المعاملة, الرصيد_بعد_المعاملة, معرف_الجدولة, معاملة_تلقائية, المستخدم)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (salary['معرف_الموظف'], salary['اسم_الموظف'], 'إيداع', 'إضافة مرتب',
                      salary['مبلغ_المرتب'], today, 'مرتب تلقائي مجدول', current_balance,
                      new_balance, salary['id'], True, 'النظام التلقائي'))

                # تحديث رصيد الموظف
                cursor.execute("UPDATE الموظفين SET الرصيد = %s WHERE id = %s",
                              (new_balance, salary['معرف_الموظف']))

                # إدراج في تقارير الموظفين
                cursor.execute("""
                    INSERT INTO الموظفين_معاملات_مالية
                    (معرف_الموظف, اسم_الموظف, نوع_المعاملة, المبلغ, التاريخ, الوصف, المستخدم)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (salary['معرف_الموظف'], salary['اسم_الموظف'], 'إضافة مرتب',
                      salary['مبلغ_المرتب'], today, 'مرتب تلقائي مجدول', 'النظام التلقائي'))

                # حساب التاريخ التالي
                current_date = QDate.fromString(salary['تاريخ_الإيداع_التالي'], Qt.ISODate)
                if salary['نوع_الجدولة'] == 'شهري_نفس_التاريخ':
                    next_date = current_date.addMonths(1)
                elif salary['نوع_الجدولة'] == 'كل_30_يوم':
                    next_date = current_date.addDays(30)
                else:
                    next_date = current_date

                # تحديث الجدولة
                cursor.execute("""
                    UPDATE الموظفين_جدولة_المرتبات
                    SET تاريخ_آخر_إيداع = %s,
                        تاريخ_الإيداع_التالي = %s,
                        عدد_الإيداعات_المنجزة = عدد_الإيداعات_المنجزة + 1
                    WHERE id = %s
                """, (today, next_date.toString(Qt.ISODate), salary['id']))

                conn.commit()
                processed_count += 1

            except Exception as e:
                conn.rollback()
                print(f"خطأ في معالجة مرتب الموظف {salary['اسم_الموظف']}: {str(e)}")

        if processed_count > 0:
            QMessageBox.information(
                main_window, "معالجة المرتبات التلقائية",
                f"تم معالجة {processed_count} مرتب تلقائي بنجاح"
            )

        cursor.close()
        conn.close()

    except Exception as e:
        QMessageBox.warning(main_window, "خطأ", f"فشل في معالجة المرتبات التلقائية: {str(e)}")
