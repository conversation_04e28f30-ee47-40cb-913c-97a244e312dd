"""
ملف تكوين نظام عرض الإنجاز المتقدم
=====================================

يحتوي على جميع الإعدادات والألوان والثوابت المستخدمة في النظام
"""

# ألوان الحالة
STATUS_COLORS = {
    "مكتمل": {
        "primary": "#27ae60",
        "secondary": "#2ecc71",
        "background": "#d5f4e6",
        "text": "#27ae60"
    },
    "متوقف": {
        "primary": "#e74c3c",
        "secondary": "#c0392b",
        "background": "#fadbd8",
        "text": "#e74c3c"
    },
    "معلق": {
        "primary": "#3498db",
        "secondary": "#2980b9",
        "background": "#d6eaf8",
        "text": "#3498db"
    },
    "قيد الانجاز": {
        "primary": "#f39c12",
        "secondary": "#e67e22",
        "background": "#fff3cd",
        "text": "#856404"
    }
}

# ألوان الأولوية
PRIORITY_COLORS = {
    "عالي": {
        "primary": "#e74c3c",
        "background": "#fadbd8",
        "text": "#e74c3c"
    },
    "متوسط": {
        "primary": "#f39c12",
        "background": "#fdeaa7",
        "text": "#f39c12"
    },
    "عادي": {
        "primary": "#27ae60",
        "background": "#e8f5e8",
        "text": "#27ae60"
    }
}

# ألوان الأيام المتبقية
DAYS_LEFT_COLORS = {
    "overdue": {  # متأخر
        "primary": "#e74c3c",
        "secondary": "#c0392b"
    },
    "urgent": {  # عاجل (0-2 أيام)
        "primary": "#f39c12",
        "secondary": "#e67e22"
    },
    "warning": {  # تحذير (3-5 أيام)
        "primary": "#f1c40f",
        "secondary": "#f39c12"
    },
    "normal": {  # عادي (أكثر من 5 أيام)
        "primary": "#27ae60",
        "secondary": "#2ecc71"
    }
}

# إعدادات شريط التقدم
PROGRESS_BAR_SETTINGS = {
    "height": 50,
    "margin": 4,
    "border_radius": 8,
    "inner_margin": 8,
    "shine_opacity": 80,
    "text_font_size": 10,
    "sub_text_font_size": 8
}

# إعدادات الجدول
TABLE_SETTINGS = {
    "row_height": 60,
    "font_size": 11,
    "header_font_size": 12,
    "border_radius": 8,
    "alternate_row_color": "#f8f9fa",
    "selection_color": "#e3f2fd",
    "grمعرف_color": "#dee2e6"
}

# إعدادات النافذة الرئيسية
WINDOW_SETTINGS = {
    "min_width": 1200,
    "min_height": 800,
    "title": "نظام عرض الإنجاز المتقدم",
    "icon_size": 24,
    "spacing": 20,
    "margins": 20
}

# نصوص الواجهة
UI_TEXTS = {
    "title": "📊 لوحة تحكم الإنجاز المتقدمة",
    "section_title": "🎯 تفاصيل المشاريع والإنجاز",
    "total_projects": "إجمالي المشاريع",
    "completed_projects": "مكتملة",
    "in_progress": "قيد الإنجاز",
    "overdue_projects": "متأخرة",
    "status_ready": "جاهز",
    "status_updating": "جاري التحديث...",
    "status_updated": "تم التحديث بنجاح",
    "status_exporting": "جاري التصدير...",
    "status_exported": "تم التصدير بنجاح",
    "status_settings": "فتح الإعدادات...",
    "btn_refresh": "🔄 تحديث",
    "btn_export": "📤 تصدير",
    "btn_settings": "⚙️ إعدادات"
}

# أعمدة الجدول
TABLE_COLUMNS = {
    "project_name": "اسم المشروع",
    "client": "العميل",
    "status": "الحالة",
    "priority": "الأولوية",
    "progress": "الإنجاز",
    "deadline": "التاريخ المحدد",
    "notes": "الملاحظات"
}

# إعدادات الرسوم المتحركة
ANIMATION_SETTINGS = {
    "duration": 300,  # مدة الرسوم المتحركة بالميلي ثانية
    "easing": "ease-in-out",
    "fade_duration": 200,
    "slide_duration": 250
}

# إعدادات التصدير
EXPORT_SETTINGS = {
    "formats": ["Excel", "PDF", "CSV"],
    "default_format": "Excel",
    "include_charts": True,
    "include_summary": True
}

# إعدادات الإشعارات
NOTIFICATION_SETTINGS = {
    "show_overdue": True,
    "show_due_today": True,
    "show_completed": True,
    "auto_refresh_interval": 300000,  # 5 دقائق بالميلي ثانية
    "notification_duration": 3000  # 3 ثوان
}

# قواعد الألوان الديناميكية
COLOR_RULES = {
    "days_left_thresholds": {
        "overdue": lambda days: days < 0,
        "urgent": lambda days: 0 <= days <= 2,
        "warning": lambda days: 3 <= days <= 5,
        "normal": lambda days: days > 5
    },
    "progress_thresholds": {
        "low": lambda progress: progress < 30,
        "medium": lambda progress: 30 <= progress < 70,
        "high": lambda progress: 70 <= progress < 100,
        "complete": lambda progress: progress >= 100
    }
}

# إعدادات الخطوط
FONT_SETTINGS = {
    "primary_font": "Segoe UI",
    "fallback_fonts": ["Tahoma", "Arial", "sans-serif"],
    "title_size": 24,
    "header_size": 18,
    "normal_size": 12,
    "small_size": 10,
    "bold_weight": "bold",
    "normal_weight": "normal"
}

# مسارات الملفات
FILE_PATHS = {
    "styles": "advanced_progress_styles.qss",
    "config": "progress_config.py",
    "examples": "examples_usage.py",
    "readme": "README_AdvancedProgress.md"
}

# إعدادات قاعدة البيانات (للاستخدام المستقبلي)
DATABASE_SETTINGS = {
    "type": "sqlite",
    "name": "projects.db",
    "tables": {
        "projects": "projects",
        "clients": "clients",
        "users": "users",
        "logs": "activity_logs"
    }
}

def get_color_by_days_left(days_left):
    """الحصول على اللون حسب الأيام المتبقية"""
    if days_left < 0:
        return DAYS_LEFT_COLORS["overdue"]
    elif days_left <= 2:
        return DAYS_LEFT_COLORS["urgent"]
    elif days_left <= 5:
        return DAYS_LEFT_COLORS["warning"]
    else:
        return DAYS_LEFT_COLORS["normal"]

def get_status_color(status):
    """الحصول على لون الحالة"""
    return STATUS_COLORS.get(status, STATUS_COLORS["قيد الانجاز"])

def get_priority_color(priority):
    """الحصول على لون الأولوية"""
    return PRIORITY_COLORS.get(priority, PRIORITY_COLORS["عادي"])

def get_progress_text(progress, days_left):
    """تكوين نص التقدم"""
    if days_left > 0:
        return f"{progress}% - باقي {days_left} يوم"
    elif days_left == 0:
        return f"{progress}% - ينتهي اليوم"
    else:
        return f"{progress}% - متأخر {abs(days_left)} يوم"

def validate_project_data(project):
    """التحقق من صحة بيانات المشروع"""
    required_fields = ["name", "client", "status", "priority", "progress", "days_left", "deadline"]
    
    for field in required_fields:
        if field not in project:
            return False, f"الحقل المطلوب '{field}' غير موجود"
    
    if not (0 <= project["progress"] <= 100):
        return False, "نسبة الإنجاز يجب أن تكون بين 0 و 100"
    
    if project["status"] not in STATUS_COLORS:
        return False, f"حالة غير صحيحة: {project['status']}"
    
    if project["priority"] not in PRIORITY_COLORS:
        return False, f"أولوية غير صحيحة: {project['priority']}"
    
    return True, "البيانات صحيحة"

# إعدادات التطوير والتصحيح
DEBUG_SETTINGS = {
    "enable_debug": False,
    "log_level": "INFO",
    "show_performance_metrics": False,
    "enable_test_data": True
}

# معلومات الإصدار
VERSION_INFO = {
    "version": "1.0.0",
    "build_date": "2024-02-01",
    "author": "Advanced AI System",
    "description": "نظام عرض الإنجاز المتقدم والاحترافي"
}
