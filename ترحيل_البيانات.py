# project_migrator_single_file.py

# ----------------------------------------------------------------------
# 1. Configuration
# ----------------------------------------------------------------------
DB_HOST = "localhost"
# For project_manager1
DB_USER_V1 = "pme"
DB_PASSWORD_V1 = "kh123456"
# For project_manager2
DB_USER_V2 = "pme"
DB_PASSWORD_V2 = "kh123456"
ROOT_USER = "root"
ROOT_PASSWORD = "kh123456" # كلمة مرور المستخدم root - تستخدم فقط لإنشاء DBs

# ----------------------------------------------------------------------
# 2. Imports
# ----------------------------------------------------------------------
import sys
import mysql.connector
from mysql.connector import errorcode
from datetime import datetime

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QTextEdit, QProgressBar, QLabel, QMessageBox
)
from PySide6.QtCore import QThread, Signal, Slot, QDate

# ----------------------------------------------------------------------
# 3. Helper Functions (from migration_logic)
# ----------------------------------------------------------------------
def parse_date_flexible(date_str):
    """Attempts to parse a date string from common formats."""
    if not date_str or not isinstance(date_str, str):
        return None
    formats_to_try = ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y", "%Y/%m/%d", "%m/%d/%Y"]
    for fmt in formats_to_try:
        try:
            return datetime.strptime(date_str.strip(), fmt).strftime("%Y-%m-%d")
        except ValueError:
            continue
    return None

def to_decimal(value, default=0.0):
    """Converts a value to Decimal, handling None or empty strings."""
    if value is None or str(value).strip() == "":
        return default
    try:
        return float(str(value)) # MySQL connector handles float to DECIMAL
    except ValueError:
        return default

def to_int(value, default=0):
    if value is None or str(value).strip() == "":
        return default
    try:
        return int(str(value))
    except ValueError:
        return default

# ----------------------------------------------------------------------
# 4. Database Utilities (formerly db_utils.py)
# ----------------------------------------------------------------------
def get_db_connection(db_name, user, password, host=DB_HOST): # Uses global DB_HOST
    """Establishes a connection to a specific database."""
    try:
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=db_name,
            charset='utf8mb4'
        )
        return conn
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            print(f"Access denied for user '{user}' for database '{db_name}'.")
        elif err.errno == errorcode.ER_BAD_DB_ERROR:
            print(f"Database '{db_name}' does not exist.")
        else:
            print(f"Error connecting to '{db_name}': {err}")
        return None

def get_root_connection(host=DB_HOST, user=ROOT_USER, password=ROOT_PASSWORD): # Uses global DB_HOST, ROOT_USER, ROOT_PASSWORD
    """Establishes a root connection (needed for CREATE DATABASE)."""
    try:
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        return conn
    except mysql.connector.Error as err:
        print(f"Error connecting as root: {err}")
        return None

def create_project_manager2_db_if_not_exists(year, progress_callback=None):
    """
    إنشاء قاعدة البيانات والجداول للسنة المحددة إذا لم تكن موجودة (project_manager2)
    """
    db_name = f"project_manager_V2"
    conn = None
    cursor = None

    def _log(message):
        if progress_callback:
            progress_callback(message)
        else:
            print(message)

    try:
        conn = get_root_connection() # Uses global config via get_root_connection
        if conn is None:
            _log(f"Failed to get root connection to create {db_name}.")
            return False
        cursor = conn.cursor()

        cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        _log(f"Database {db_name} ensured.")
        cursor.execute(f"USE `{db_name}`")

        def create_index_if_not_exists(cursor, index_name, table_name, column_name):
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if not cursor.fetchone():
                _log(f"Table {table_name} does not exist. Skipping index creation for {index_name}.")
                return

            try:
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.STATISTICS
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND INDEX_NAME = %s
                """, (db_name, table_name, index_name))
                if cursor.fetchone()[0] == 0:
                    try:
                        cursor.execute(f"CREATE INDEX `{index_name}` ON `{table_name}`(`{column_name}`)")
                        _log(f"Index {index_name} created on {table_name}({column_name}).")
                    except mysql.connector.Error as e:
                        _log(f"Warning: Could not create index {index_name} on {table_name}({column_name}): {e}")
            except mysql.connector.Error as e:
                _log(f"Warning: Could not check for index {index_name} existence: {e}")

        table_definitions = {
            "العملاء": """
                CREATE TABLE IF NOT EXISTS `العملاء` (
                    `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255) UNIQUE,
                    `اسم_العميل` VARCHAR(255),
                    `العنوان` VARCHAR(255),
                    `رقم_الهاتف` VARCHAR(255),
                    `تاريخ_الإضافة` DATE,
                    `ملاحظات` VARCHAR(255)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """,
            "المشاريع": """
                CREATE TABLE IF NOT EXISTS `المشاريع` (
                    `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العميل` INT,
                    `التصنيف` VARCHAR(255) UNIQUE,
                    `اسم_العميل` VARCHAR(255),
                    `اسم_المشروع` VARCHAR(255),
                    `المبلغ` DECIMAL(10,2),
                    `المدفوع` DECIMAL(10,2) DEFAULT 0,
                    `تاريخ_الإستلام` DATE,
                    `تاريخ_التسليم` DATE,
                    `الباقي` DECIMAL(10,2) GENERATED ALWAYS AS (CAST(`المبلغ` AS DECIMAL(10,2)) - CAST(`المدفوع` AS DECIMAL(10,2))) STORED,
                    `الوقت_المتبقي` VARCHAR(255),
                    `الحالة` VARCHAR(255),
                    `الهاتف` VARCHAR(255),
                    `ملاحظات` TEXT,
                     CONSTRAINT `fk_المشاريع_معرف_العميل`
                     FOREIGN KEY (`معرف_العميل`)
                     REFERENCES `العملاء`(`id`)
                     ON DELETE SET NULL
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """,
            "المشاريع_المدفوعات": """
                CREATE TABLE IF NOT EXISTS `المشاريع_المدفوعات` (
                    `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العميل` INT,
                    `معرف_المشروع` INT,
                    `وصف_المدفوع` VARCHAR(255),
                    `المبلغ_المدفوع` DECIMAL(10,2),
                    `تاريخ_الدفع` DATE,
                    `طريقة_الدفع` VARCHAR(255),
                    `المستلم` VARCHAR(255),
                    CONSTRAINT `fk_المشاريع_المدفوعات_معرف_المشروع`
                    FOREIGN KEY (`معرف_المشروع`)
                    REFERENCES `المشاريع`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """,
            "المصروفات": """
                CREATE TABLE IF NOT EXISTS `المصروفات` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255) UNIQUE,
                    `المصروف` VARCHAR(255),
                    `المستلم` VARCHAR(255),
                    `المبلغ` DECIMAL(10,2),
                    `التاريخ` DATE,
                    `رقم_الهاتف` VARCHAR(255),
                    `رقم_الفاتورة` VARCHAR(255),
                    `ملاحظات` TEXT
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """,
            "الموظفين": """
                CREATE TABLE IF NOT EXISTS `الموظفين` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255) UNIQUE,
                    `اسم_الموظف` VARCHAR(255),
                    `الوظيفة` VARCHAR(255),
                    `تاريخ_التوظيف` DATE,
                    `المرتب` DECIMAL(10,2),
                    `النسبة` INT,
                    `الهاتف` VARCHAR(255),
                    `الرصيد` DECIMAL(10,2) DEFAULT 0,
                    `السحب` DECIMAL(10,2) DEFAULT 0,
                    `ملاحظات` TEXT
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """,
            "تقارير_الموظفين": """
                CREATE TABLE IF NOT EXISTS `تقارير_الموظفين` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_الموظف` INT,
                    `اسم_الموظف` VARCHAR(255),
                    `الوظيفة` VARCHAR(255),
                    `نوع_المعاملة` VARCHAR(255),
                    `المبلغ` DECIMAL(10,2),
                    `التاريخ` DATE,
                    `النسبة` INT,
                    `الوصف` VARCHAR(255),
                    CONSTRAINT `fk_تقارير_الموظفين_معرف_الموظف`
                    FOREIGN KEY (`معرف_الموظف`)
                    REFERENCES `الموظفين`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """
        }

        for table_name, ddl in table_definitions.items():
            cursor.execute(ddl)
            _log(f"Table {table_name} ensured.")

        indexes = [
            ('idx_المشاريع_التصنيف', 'المشاريع', 'التصنيف'),
            ('idx_العملاء_اسم_العميل', 'العملاء', 'اسم_العميل'),
            ('idx_الموظفين_التصنيف', 'الموظفين', 'التصنيف'),
        ]
        for idx_name, tbl_name, col_name in indexes:
            create_index_if_not_exists(cursor, idx_name, tbl_name, col_name)

        triggers = [
            ("update_project_paid_insert", f"""
                CREATE TRIGGER IF NOT EXISTS `update_project_paid_insert`
                AFTER INSERT ON `المشاريع_المدفوعات`
                FOR EACH ROW
                BEGIN
                    UPDATE `المشاريع`
                    SET `المدفوع` = IFNULL(`المدفوع`, 0) + IFNULL(NEW.`المبلغ_المدفوع`, 0)
                    WHERE `id` = NEW.`معرف_المشروع`;
                END;
            """),
             ("update_employee_balance_insert", f"""
                CREATE TRIGGER IF NOT EXISTS `update_employee_balance_insert`
                AFTER INSERT ON `تقارير_الموظفين`
                FOR EACH ROW
                BEGIN
                    UPDATE `الموظفين`
                    SET `الرصيد` = IFNULL(`الرصيد`, 0) + IFNULL(NEW.`المبلغ`, 0)
                    WHERE `id` = NEW.`معرف_الموظف`;
                END;
            """),
        ]

        for trigger_name, trigger_sql in triggers:
            try:
                cursor.execute(f"DROP TRIGGER IF EXISTS `{trigger_name}`;")
                cursor.execute(trigger_sql)
                _log(f"Trigger {trigger_name} ensured.")
            except mysql.connector.Error as err:
                 _log(f"Warning creating trigger {trigger_name}: {err}")

        conn.commit()
        _log(f"Database {db_name} setup complete.")
        return True

    except mysql.connector.Error as err:
        _log(f"Database Creation/Setup Error for {db_name}: {err}")
        return False
    except Exception as e:
        _log(f"Unexpected Error during DB setup for {db_name}: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# ----------------------------------------------------------------------
# 5. Migration Logic (formerly migration_logic.py)
# ----------------------------------------------------------------------
class DataMigrator:
    def __init__(self, year, progress_callback):
        self.year = year
        self.source_db_name = f"project_manager1_{year}"
        self.target_db_name = f"project_manager_V2"
        self.progress_callback = progress_callback

        self.source_conn = None
        self.target_conn = None

        self.employee_معرف_map = {}
        self.client_معرف_map = {}
        self.project_معرف_map = {}

    def _log(self, message):
        if self.progress_callback:
            self.progress_callback(message)
        else:
            print(message)

    def connect_dbs(self):
        self._log(f"Connecting to source DB: {self.source_db_name}")
        self.source_conn = get_db_connection( # Uses global function
            self.source_db_name, DB_USER_V1, DB_PASSWORD_V1 # Uses global config
        )
        if not self.source_conn:
            self._log(f"Failed to connect to source DB: {self.source_db_name}")
            return False

        self._log(f"Ensuring target DB exists: {self.target_db_name}")
        if not create_project_manager2_db_if_not_exists(self.year, self.progress_callback): # Uses global function
            self._log(f"Failed to create or setup target DB: {self.target_db_name}")
            return False
        
        self._log(f"Connecting to target DB: {self.target_db_name}")
        self.target_conn = get_db_connection( # Uses global function
            self.target_db_name, DB_USER_V2, DB_PASSWORD_V2 # Uses global config
        )
        if not self.target_conn:
            self._log(f"Failed to connect to target DB: {self.target_db_name}")
            return False
        
        return True

    def close_dbs(self):
        if self.source_conn:
            self.source_conn.close()
        if self.target_conn:
            self.target_conn.close()
        self._log("Database connections closed.")

    def migrate_employees(self):
        self._log("Starting migration for: الموظفين")
        src_cursor = self.source_conn.cursor(dictionary=True)
        tgt_cursor = self.target_conn.cursor()

        src_cursor.execute("SELECT * FROM الموظفين")
        employees_v1 = src_cursor.fetchall()
        count = 0
        for emp in employees_v1:
            try:
                employee_code = emp.get('التصنيف')
                if not employee_code:
                    self._log(f"Skipping employee with missing التصنيف: id {emp.get('id')}")
                    continue

                insert_query = """
                INSERT INTO الموظفين (التصنيف, اسم_الموظف, الوظيفة, تاريخ_التوظيف, المرتب, النسبة, الهاتف, الرصيد, السحب, ملاحظات)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    اسم_الموظف=VALUES(اسم_الموظف), الوظيفة=VALUES(الوظيفة), تاريخ_التوظيف=VALUES(تاريخ_التوظيف),
                    المرتب=VALUES(المرتب), النسبة=VALUES(النسبة), الهاتف=VALUES(الهاتف),
                    الرصيد=VALUES(الرصيد), السحب=VALUES(السحب), ملاحظات=VALUES(ملاحظات);
                """
                data = (
                    employee_code,
                    emp.get('اسم_الموظف'),
                    emp.get('الوظيفة'),
                    parse_date_flexible(emp.get('تاريخ_التوظيف')), # Global helper
                    to_decimal(emp.get('المرتب')), # Global helper
                    to_int(emp.get('النسبة')), # Global helper
                    emp.get('الهاتف'),
                    to_decimal(emp.get('الرصيد')),
                    to_decimal(emp.get('السحب')),
                    emp.get('ملاحظات')
                )
                tgt_cursor.execute(insert_query, data)
                new_emp_id = tgt_cursor.lastrowid
                if new_emp_id == 0 :
                    fetch_معرف_query = "SELECT id FROM الموظفين WHERE id = %s"
                    tgt_cursor.execute(fetch_معرف_query, (employee_code,))
                    result = tgt_cursor.fetchone()
                    if result:
                        new_emp_id = result[0]
                
                if emp.get('id'):
                     self.employee_معرف_map[emp.get('id')] = new_emp_id
                self.employee_معرف_map[employee_code] = new_emp_id
                count += 1
            except mysql.connector.Error as err:
                self._log(f"Error migrating employee {emp.get('التصنيف')}: {err}")
            except Exception as e:
                self._log(f"Unexpected error migrating employee {emp.get('التصنيف')}: {e}")

        self.target_conn.commit()
        src_cursor.close()
        tgt_cursor.close()
        self._log(f"Migrated {count} employees.")

    def migrate_clients_and_projects(self):
        self._log("Starting migration for: العملاء (from المشاريع v1) and المشاريع")
        src_cursor = self.source_conn.cursor(dictionary=True)
        tgt_client_cursor = self.target_conn.cursor()
        tgt_project_cursor = self.target_conn.cursor()

        src_cursor.execute("SELECT * FROM المشاريع")
        projects_v1 = src_cursor.fetchall()
        
        clients_created_count = 0
        projects_migrated_count = 0
        
        unique_clients = {}
        for proj_v1 in projects_v1:
            client_name = proj_v1.get('اسم_العميل')
            client_phone = proj_v1.get('الهاتف')
            client_key = f"{client_name}_{client_phone}"

            if client_name and client_key not in self.client_معرف_map:
                try:
                    client_code = f"CLT_{''.join(filter(str.isalnum, client_name[:10]))}_{len(self.client_معرف_map)+1}"
                    add_date = datetime.now().strftime("%Y-%m-%d")
                    if proj_v1.get('تاريخ_الإستلام'):
                         parsed_estilam = parse_date_flexible(proj_v1.get('تاريخ_الإستلام'))
                         if parsed_estilam: add_date = parsed_estilam

                    client_insert_query = """
                    INSERT INTO العملاء (التصنيف, اسم_العميل, رقم_الهاتف, تاريخ_الإضافة, ملاحظات)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE اسم_العميل=VALUES(اسم_العميل), رقم_الهاتف=VALUES(رقم_الهاتف);
                    """
                    client_data = (
                        client_code, client_name, client_phone, add_date, "Migrated from v1 projects"
                    )
                    tgt_client_cursor.execute(client_insert_query, client_data)
                    new_client_id = tgt_client_cursor.lastrowid
                    if new_client_id == 0:
                        fetch_معرف_q = "SELECT id FROM العملاء WHERE id = %s"
                        tgt_client_cursor.execute(fetch_معرف_q, (client_code,))
                        res = tgt_client_cursor.fetchone()
                        if res: new_client_id = res[0]

                    if new_client_id:
                        self.client_معرف_map[client_key] = new_client_id
                        clients_created_count +=1
                    else:
                        self._log(f"Could not get new id for client: {client_name}")
                except mysql.connector.Error as err:
                    self._log(f"Error creating client {client_name}: {err}")
                except Exception as e:
                    self._log(f"Unexpected error creating client {client_name}: {e}")
        self.target_conn.commit()
        self._log(f"Created {clients_created_count} unique clients.")

        for proj_v1 in projects_v1:
            try:
                project_code_v1 = proj_v1.get('التصنيف')
                if not project_code_v1:
                    self._log(f"Skipping project with missing التصنيف: old id {proj_v1.get('id')}")
                    continue

                client_name = proj_v1.get('اسم_العميل')
                client_phone = proj_v1.get('الهاتف')
                client_key = f"{client_name}_{client_phone}"
                new_client_معرف_fk = self.client_معرف_map.get(client_key)

                if not new_client_معرف_fk:
                    self._log(f"Client id not found for project {project_code_v1} (Client: {client_name}). Skipping project.")
                    continue
                
                project_insert_query = """
                INSERT INTO المشاريع (معرف_العميل, التصنيف, اسم_العميل, اسم_المشروع, المبلغ, المدفوع,
                                    تاريخ_الإستلام, تاريخ_التسليم, الوقت_المتبقي, الحالة, الهاتف, ملاحظات)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    معرف_العميل=VALUES(معرف_العميل), اسم_العميل=VALUES(اسم_العميل), اسم_المشروع=VALUES(اسم_المشروع),
                    المبلغ=VALUES(المبلغ), المدفوع=VALUES(المدفوع), تاريخ_الإستلام=VALUES(تاريخ_الإستلام),
                    تاريخ_التسليم=VALUES(تاريخ_التسليم), الوقت_المتبقي=VALUES(الوقت_المتبقي),
                    الحالة=VALUES(الحالة), الهاتف=VALUES(الهاتف), ملاحظات=VALUES(ملاحظات);
                """
                project_data = (
                    new_client_معرف_fk, project_code_v1, client_name,
                    proj_v1.get('اسم_المشروع'), to_decimal(proj_v1.get('المبلغ')), 0.0, # المدفوع set to 0
                    parse_date_flexible(proj_v1.get('تاريخ_الإستلام')),
                    parse_date_flexible(proj_v1.get('تاريخ_التسليم')),
                    proj_v1.get('الوقت_المتبقي'), proj_v1.get('الحالة'),
                    proj_v1.get('الهاتف'), proj_v1.get('ملاحظات')
                )
                tgt_project_cursor.execute(project_insert_query, project_data)
                new_project_id = tgt_project_cursor.lastrowid
                if new_project_id == 0:
                    fetch_معرف_q = "SELECT id FROM المشاريع WHERE id = %s"
                    tgt_project_cursor.execute(fetch_معرف_q, (project_code_v1,))
                    res = tgt_project_cursor.fetchone()
                    if res: new_project_id = res[0]

                if new_project_id:
                    self.project_معرف_map[project_code_v1] = new_project_id
                    if proj_v1.get('id'):
                         self.project_معرف_map[proj_v1.get('id')] = new_project_id
                    projects_migrated_count +=1
                else:
                    self._log(f"Could not get new id for project: {project_code_v1}")
            except mysql.connector.Error as err:
                self._log(f"Error migrating project {proj_v1.get('التصنيف')}: {err}")
            except Exception as e:
                 self._log(f"Unexpected error migrating project {proj_v1.get('التصنيف')}: {e}")

        self.target_conn.commit()
        src_cursor.close()
        tgt_client_cursor.close()
        tgt_project_cursor.close()
        self._log(f"Migrated {projects_migrated_count} projects.")

    def migrate_project_payments(self):
        self._log("Starting migration for: المشاريع_المدفوعات")
        src_cursor = self.source_conn.cursor(dictionary=True)
        tgt_cursor = self.target_conn.cursor()

        src_cursor.execute("SELECT * FROM المشاريع_المدفوعات")
        payments_v1 = src_cursor.fetchall()
        count = 0
        for payment in payments_v1:
            try:
                project_code_v1 = payment.get('التصنيف') 
                new_project_معرف_fk = self.project_معرف_map.get(project_code_v1)
                if not new_project_معرف_fk and payment.get('معرف_العميل'):
                    new_project_معرف_fk = self.project_معرف_map.get(to_int(payment.get('معرف_العميل')))

                if not new_project_معرف_fk:
                    self._log(f"Project id for payment (Proj Code: {project_code_v1}) not found. Skipping payment id {payment.get('id')}.")
                    continue

                tgt_cursor.execute("SELECT معرف_العميل FROM المشاريع WHERE id = %s", (new_project_معرف_fk,))
                project_details = tgt_cursor.fetchone()
                new_client_معرف_fk = project_details[0] if project_details else None

                if not new_client_معرف_fk:
                    self._log(f"Client id for payment (Proj Code: {project_code_v1}, New Proj id: {new_project_معرف_fk}) not found. Skipping.")
                    continue

                insert_query = """
                INSERT INTO المشاريع_المدفوعات (معرف_العميل, معرف_المشروع, وصف_المدفوع, المبلغ_المدفوع, تاريخ_الدفع, طريقة_الدفع, المستلم)
                VALUES (%s, %s, %s, %s, %s, %s, %s) 
                """ 
                # طريقة_الدفع and المستلم are new in v2. Defaulting to None or empty.
                data = (
                    new_client_معرف_fk, new_project_معرف_fk,
                    payment.get('وصف_المدفوع'),
                    to_decimal(payment.get('المبلغ_المدفوع')),
                    parse_date_flexible(payment.get('تاريخ_الدفع')),
                    None, # طريقة_الدفع - new field
                    None  # المستلم - new field
                )
                tgt_cursor.execute(insert_query, data)
                count +=1
            except mysql.connector.Error as err:
                self._log(f"Error migrating project payment for project {payment.get('التصنيف')}, id {payment.get('id')}: {err}")
            except Exception as e:
                self._log(f"Unexpected error migrating project payment for project {payment.get('التصنيف')}, id {payment.get('id')}: {e}")
        
        self.target_conn.commit()
        src_cursor.close()
        tgt_cursor.close()
        self._log(f"Migrated {count} project payments.")

    def migrate_expenses(self):
        self._log("Starting migration for: المصروفات")
        src_cursor = self.source_conn.cursor(dictionary=True)
        tgt_cursor = self.target_conn.cursor()

        src_cursor.execute("SELECT * FROM المصروفات")
        expenses_v1 = src_cursor.fetchall()
        count = 0
        for exp in expenses_v1:
            try:
                expense_code = exp.get('التصنيف')
                if not expense_code:
                    self._log(f"Skipping expense with missing التصنيف: id {exp.get('id')}")
                    continue
                
                insert_query = """
                INSERT INTO المصروفات (التصنيف, المصروف, المستلم, المبلغ, التاريخ, رقم_الهاتف, رقم_الفاتورة, ملاحظات)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    المصروف=VALUES(المصروف), المستلم=VALUES(المستلم), المبلغ=VALUES(المبلغ),
                    التاريخ=VALUES(التاريخ), رقم_الهاتف=VALUES(رقم_الهاتف), رقم_الفاتورة=VALUES(رقم_الفاتورة),
                    ملاحظات=VALUES(ملاحظات);
                """
                data = (
                    expense_code, exp.get('المصروف'), exp.get('المستلم'),
                    to_decimal(exp.get('المبلغ')), parse_date_flexible(exp.get('التاريخ')),
                    exp.get('رقم_الهاتف'), exp.get('رقم_الفاتورة'), exp.get('ملاحظات')
                )
                tgt_cursor.execute(insert_query, data)
                count += 1
            except mysql.connector.Error as err:
                self._log(f"Error migrating expense {exp.get('التصنيف')}: {err}")
            except Exception as e:
                self._log(f"Unexpected error migrating expense {exp.get('التصنيف')}: {e}")

        self.target_conn.commit()
        src_cursor.close()
        tgt_cursor.close()
        self._log(f"Migrated {count} expenses.")

    def migrate_employee_reports(self):
        self._log("Starting migration for: تقارير_الموظفين")
        src_cursor = self.source_conn.cursor(dictionary=True)
        tgt_cursor = self.target_conn.cursor()

        src_cursor.execute("SELECT * FROM تقارير_الموظفين")
        reports_v1 = src_cursor.fetchall()
        count = 0
        for report in reports_v1:
            try:
                old_emp_معرف_v1 = report.get('معرف_الموظف')
                employee_code_v1 = report.get('التصنيف')
                new_employee_معرف_fk = None

                if old_emp_معرف_v1:
                    try:
                        new_employee_معرف_fk = self.employee_معرف_map.get(int(old_emp_معرف_v1))
                    except ValueError:
                        new_employee_معرف_fk = self.employee_معرف_map.get(str(old_emp_معرف_v1))
                if not new_employee_معرف_fk and employee_code_v1:
                    new_employee_معرف_fk = self.employee_معرف_map.get(employee_code_v1)

                if not new_employee_معرف_fk:
                    self._log(f"Employee id for report (Emp Code: {employee_code_v1}, Old V1 id: {old_emp_معرف_v1}) not found. Skipping report id {report.get('id')}.")
                    continue
                
                insert_query = """
                INSERT INTO تقارير_الموظفين (معرف_الموظف, اسم_الموظف, الوظيفة, نوع_المعاملة, المبلغ, التاريخ, النسبة, الوصف)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                data = (
                    new_employee_معرف_fk, report.get('اسم_الموظف'), report.get('الوظيفة'),
                    report.get('نوع_المعاملة'), to_decimal(report.get('المبلغ')),
                    parse_date_flexible(report.get('التاريخ')), to_int(report.get('النسبة')),
                    report.get('الوصف')
                )
                tgt_cursor.execute(insert_query, data)
                count +=1
            except mysql.connector.Error as err:
                self._log(f"Error migrating employee report for emp code {report.get('التصنيف')}, id {report.get('id')}: {err}")
            except Exception as e:
                self._log(f"Unexpected error migrating employee report for emp code {report.get('التصنيف')}, id {report.get('id')}: {e}")

        self.target_conn.commit()
        src_cursor.close()
        tgt_cursor.close()
        self._log(f"Migrated {count} employee reports.")

    def run_migration(self):
        if not self.connect_dbs():
            return False
        
        total_steps = 5
        current_step = 0

        def update_overall_progress(step_increment=1):
            nonlocal current_step
            current_step += step_increment
            if self.progress_callback:
                self.progress_callback(f"OVERALL_PROGRESS:{int((current_step / total_steps) * 100)}")

        try:
            self.migrate_employees()
            update_overall_progress()

            self.migrate_clients_and_projects()
            update_overall_progress()

            self.migrate_project_payments()
            update_overall_progress()
            
            self.migrate_expenses()
            update_overall_progress()

            self.migrate_employee_reports()
            update_overall_progress()

            self._log("Data migration process completed successfully.")
            return True
        except Exception as e:
            self._log(f"A critical error occurred during migration: {e}")
            if self.target_conn:
                self.target_conn.rollback()
            return False
        finally:
            self.close_dbs()

# ----------------------------------------------------------------------
# 6. PySide6 UI Application
# ----------------------------------------------------------------------
class MigrationWorker(QThread):
    progress_signal = Signal(str)
    finished_signal = Signal(bool, str)
    overall_progress_signal = Signal(int)

    def __init__(self, year_to_migrate):
        super().__init__()
        self.year_to_migrate = year_to_migrate
        self.migrator = None

    def run(self):
        self.migrator = DataMigrator(self.year_to_migrate, self.emit_progress_wrapper) # Pass wrapped callback
        
        try:
            success = self.migrator.run_migration()
            if success:
                self.finished_signal.emit(True, f"Migration for year {self.year_to_migrate} completed successfully.")
            else:
                self.finished_signal.emit(False, f"Migration for year {self.year_to_migrate} failed. Check logs.")
        except Exception as e:
            self.progress_signal.emit(f"Critical error in worker thread: {e}")
            self.finished_signal.emit(False, f"Migration for year {self.year_to_migrate} failed critically: {e}")

    def emit_progress_wrapper(self, message):
        """Wraps the original progress callback to also handle OVERALL_PROGRESS signal."""
        if message.startswith("OVERALL_PROGRESS:"):
            try:
                progress_val = int(message.split(":")[1])
                self.overall_progress_signal.emit(progress_val)
            except: # pylint: disable=bare-except
                pass 
        self.progress_signal.emit(message) # Emit for logging all messages


class MigrationApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Project Data Migrator (v1 to v2)")
        self.setGeometry(100, 100, 800, 600)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        self.year_label = QLabel("Select Year to Migrate:")
        self.year_combo = QComboBox()
        current_year = QDate.currentDate().year()
        for year in range(current_year - 5, current_year + 2):
            self.year_combo.addItem(str(year))
        self.year_combo.setCurrentText(str(current_year -1))

        self.migrate_button = QPushButton("Start Migration")
        self.migrate_button.clicked.connect(self.start_migration_process)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)

        self.input_layout = QHBoxLayout()
        self.input_layout.addWidget(self.year_label)
        self.input_layout.addWidget(self.year_combo)
        self.input_layout.addWidget(self.migrate_button)

        self.layout.addLayout(self.input_layout)
        self.layout.addWidget(self.progress_bar)
        self.layout.addWidget(QLabel("Migration Log:"))
        self.layout.addWidget(self.log_output)

        self.migration_thread = None

    @Slot()
    def start_migration_process(self):
        selected_year = self.year_combo.currentText()
        if not selected_year:
            QMessageBox.warning(self, "No Year Selected", "Please select a year to migrate.")
            return

        confirm = QMessageBox.question(self, "Confirm Migration",
                                       f"This will attempt to migrate data for {selected_year} "
                                       f"from project_manager1_{selected_year} to project_manager_V2.\n"
                                       f"The target database project_manager_V2 will be created/updated.\n"
                                       "Ensure you have backups.\n\nDo you want to proceed?",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if confirm == QMessageBox.No:
            return

        self.migrate_button.setEnabled(False)
        self.year_combo.setEnabled(False)
        self.log_output.clear()
        self.progress_bar.setValue(0)
        self.log_to_gui(f"Starting migration for year: {selected_year}...")

        self.migration_thread = MigrationWorker(selected_year)
        self.migration_thread.progress_signal.connect(self.log_to_gui)
        self.migration_thread.overall_progress_signal.connect(self.update_progress_bar)
        self.migration_thread.finished_signal.connect(self.migration_finished)
        self.migration_thread.start()

    @Slot(str)
    def log_to_gui(self, message):
        if not message.startswith("OVERALL_PROGRESS:"):
            self.log_output.append(message)

    @Slot(int)
    def update_progress_bar(self, value):
        self.progress_bar.setValue(value)

    @Slot(bool, str)
    def migration_finished(self, success, message):
        self.log_to_gui(message) # Log the final message
        self.progress_bar.setValue(100 if success else self.progress_bar.value())
        self.migrate_button.setEnabled(True)
        self.year_combo.setEnabled(True)
        if success:
            QMessageBox.information(self, "Migration Complete", message)
        else:
            QMessageBox.critical(self, "Migration Failed", message)
        self.migration_thread = None

# ----------------------------------------------------------------------
# 7. Main Execution Block
# ----------------------------------------------------------------------
if __name__ == "__main__":
    app = QApplication(sys.argv)
    main_window = MigrationApp()
    main_window.show()
    sys.exit(app.exec())
