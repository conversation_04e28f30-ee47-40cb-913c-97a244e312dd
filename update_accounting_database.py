#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات المحاسبية
إضافة الأعمدة المفقودة لحل مشاكل SQL
"""

import mysql.connector

def con_db():
    """دالة الاتصال بقاعدة البيانات"""
    try:
        from متغيرات import host, user, password
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        return conn, cursor
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None, None

def update_accounting_database():
    """تحديث قاعدة البيانات المحاسبية بإضافة الأعمدة المفقودة"""
    try:
        conn, cursor = con_db()
        if not conn or not cursor:
            print("خطأ: تعذر الاتصال بقاعدة البيانات")
            return False

        print("بدء تحديث قاعدة البيانات المحاسبية...")

        # التحقق من وجود جدول شجرة الحسابات
        cursor.execute("SHOW TABLES LIKE 'شجرة_الحسابات'")
        if not cursor.fetchone():
            print("جدول شجرة الحسابات غير موجود، سيتم إنشاؤه عند تشغيل النظام")
            return True

        # التحقق من الأعمدة الموجودة
        cursor.execute("SHOW COLUMNS FROM شجرة_الحسابات")
        existing_columns = [row[0] for row in cursor.fetchall()]
        print(f"الأعمدة الموجودة: {existing_columns}")

        # إضافة عمود طبيعة الحساب إذا لم يكن موجوداً
        if 'طبيعة_الحساب' not in existing_columns:
            print("إضافة عمود طبيعة_الحساب...")
            cursor.execute("""
                ALTER TABLE شجرة_الحسابات 
                ADD COLUMN طبيعة_الحساب ENUM('مدين', 'دائن') DEFAULT 'مدين' 
                AFTER نوع_الحساب
            """)
            print("✓ تم إضافة عمود طبيعة_الحساب")
        else:
            print("✓ عمود طبيعة_الحساب موجود بالفعل")

        # إضافة عمود حساب نهائي إذا لم يكن موجوداً
        if 'حساب_نهائي' not in existing_columns:
            print("إضافة عمود حساب_نهائي...")
            cursor.execute("""
                ALTER TABLE شجرة_الحسابات 
                ADD COLUMN حساب_نهائي BOOLEAN DEFAULT FALSE 
                AFTER طبيعة_الحساب
            """)
            print("✓ تم إضافة عمود حساب_نهائي")
        else:
            print("✓ عمود حساب_نهائي موجود بالفعل")

        # تحديث القيم الافتراضية للحسابات الموجودة
        print("تحديث خصائص الحسابات الموجودة...")
        update_account_properties(cursor)

        # إضافة الفهارس إذا لم تكن موجودة
        add_indexes_if_not_exist(cursor)

        conn.commit()
        print("✓ تم تحديث قاعدة البيانات بنجاح")
        return True

    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def update_account_properties(cursor):
    """تحديث خصائص الحسابات الموجودة"""
    try:
        # تحديد طبيعة الحسابات حسب النوع
        account_nature_map = {
            'أصول': 'مدين',
            'خصوم': 'دائن', 
            'إيرادات': 'دائن',
            'مصروفات': 'مدين',
            'حقوق الملكية': 'دائن'
        }
        
        # تحديث طبيعة الحسابات
        for account_type, nature in account_nature_map.items():
            cursor.execute("""
                UPDATE شجرة_الحسابات 
                SET طبيعة_الحساب = %s 
                WHERE نوع_الحساب = %s
            """, (nature, account_type))
            print(f"✓ تم تحديث طبيعة حسابات {account_type} إلى {nature}")
        
        # تحديد الحسابات النهائية (المستوى الثالث أو الحسابات التي ليس لها أطفال)
        cursor.execute("""
            UPDATE شجرة_الحسابات 
            SET حساب_نهائي = TRUE 
            WHERE المستوى >= 3 
            OR كود_الحساب NOT IN (
                SELECT DISTINCT الحساب_الأب 
                FROM (SELECT الحساب_الأب FROM شجرة_الحسابات WHERE الحساب_الأب IS NOT NULL) AS temp
            )
        """)
        print("✓ تم تحديد الحسابات النهائية")
        
    except Exception as e:
        print(f"خطأ في تحديث خصائص الحسابات: {e}")

def add_indexes_if_not_exist(cursor):
    """إضافة الفهارس إذا لم تكن موجودة"""
    try:
        # التحقق من الفهارس الموجودة
        cursor.execute("SHOW INDEX FROM شجرة_الحسابات")
        existing_indexes = [row[2] for row in cursor.fetchall()]
        
        # إضافة فهرس طبيعة الحساب
        if 'idx_طبيعة_الحساب' not in existing_indexes:
            cursor.execute("CREATE INDEX idx_طبيعة_الحساب ON شجرة_الحسابات (طبيعة_الحساب)")
            print("✓ تم إضافة فهرس طبيعة_الحساب")
        
        # إضافة فهرس حساب نهائي
        if 'idx_حساب_نهائي' not in existing_indexes:
            cursor.execute("CREATE INDEX idx_حساب_نهائي ON شجرة_الحسابات (حساب_نهائي)")
            print("✓ تم إضافة فهرس حساب_نهائي")
            
    except Exception as e:
        print(f"خطأ في إضافة الفهارس: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("تحديث قاعدة البيانات المحاسبية")
    print("=" * 50)
    
    success = update_accounting_database()
    
    if success:
        print("\n" + "=" * 50)
        print("تم التحديث بنجاح! يمكنك الآن تشغيل النظام المحاسبي")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("فشل في التحديث! يرجى مراجعة الأخطاء أعلاه")
        print("=" * 50)
