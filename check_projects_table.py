#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص هيكل جدول المشاريع
"""

import mysql.connector
from متغيرات import host, user, password

def check_projects_table():
    """فحص هيكل جدول المشاريع"""
    print("🔍 فحص هيكل جدول المشاريع...")
    
    try:
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        # عرض هيكل الجدول
        cursor.execute("SHOW CREATE TABLE المشاريع")
        create_table = cursor.fetchone()
        if create_table:
            print("📋 هيكل جدول المشاريع:")
            print(create_table[1])
        
        conn.close()
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        return False

if __name__ == "__main__":
    check_projects_table()
