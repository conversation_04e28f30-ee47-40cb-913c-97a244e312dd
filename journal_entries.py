#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام القيود المحاسبية التلقائي
يقوم بإنشاء القيود المحاسبية تلقائياً عند إضافة المعاملات المالية
"""

import sys
import os
from datetime import datetime, date
from decimal import Decimal
import mysql.connector
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QComboBox, QMessageBox, QHeaderView,
    QTabWidget, QWidget, QSplitter, QFrame, QGroupBox
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont

# استيراد الدوال المساعدة
try:
    from functions import *
    from db import *
    from ستايل import *
except ImportError:
    print("تعذر استيراد بعض الوحدات المطلوبة")

class JournalEntryManager:
    """مدير القيود المحاسبية"""
    
    def __init__(self):
        self.conn = None
        self.cursor = None
        
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        try:
            import mysql.connector
            from متغيرات import host, user, password

            db_name = "project_manager_V2"
            self.conn = mysql.connector.connect(
                host=host,
                user=user,
                password=password,
                database=db_name
            )
            self.cursor = self.conn.cursor()
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close_connection(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
    
    def generate_entry_number(self, entry_type="عام"):
        """توليد رقم قيد جديد"""
        try:
            year = datetime.now().year
            
            # الحصول على آخر رقم قيد للسنة الحالية
            self.cursor.execute("""
                SELECT MAX(CAST(SUBSTRING(رقم_القيد, LOCATE('-', رقم_القيد) + 1) AS UNSIGNED)) 
                FROM القيود_المحاسبية 
                WHERE السنة = %s AND رقم_القيد LIKE %s
            """, (year, f"{entry_type}-%"))
            
            result = self.cursor.fetchone()
            last_number = result[0] if result[0] else 0
            
            new_number = last_number + 1
            return f"{entry_type}-{new_number:06d}"
            
        except Exception as e:
            print(f"خطأ في توليد رقم القيد: {e}")
            return f"{entry_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def create_journal_entry(self, entry_data, details_list, auto_approve=False):
        """
        إنشاء قيد محاسبي جديد
        
        entry_data: بيانات القيد الرئيسية
        details_list: قائمة تفاصيل القيد
        auto_approve: اعتماد القيد تلقائياً
        """
        try:
            if not self.get_connection():
                return False, "فشل في الاتصال بقاعدة البيانات"
            
            # التحقق من توازن القيد
            total_debit = sum(detail.get('مدين', 0) for detail in details_list)
            total_credit = sum(detail.get('دائن', 0) for detail in details_list)
            
            if abs(total_debit - total_credit) > 0.01:  # السماح بفرق صغير للتقريب
                return False, f"القيد غير متوازن: مدين {total_debit}, دائن {total_credit}"
            
            # توليد رقم القيد
            entry_number = self.generate_entry_number(entry_data.get('نوع_القيد', 'عام'))
            
            # إدراج القيد الرئيسي
            entry_insert_data = (
                entry_number,
                entry_data.get('تاريخ_القيد', date.today()),
                entry_data.get('وصف_القيد', ''),
                total_debit,
                total_credit,
                'معتمد' if auto_approve else 'مسودة',
                entry_data.get('نوع_القيد', 'عام'),
                entry_data.get('المرجع_الخارجي', ''),
                entry_data.get('مركز_التكلفة', ''),
                entry_data.get('ملاحظات', ''),
                entry_data.get('المستخدم', 'النظام'),
                datetime.now().year
            )
            
            self.cursor.execute("""
                INSERT INTO القيود_المحاسبية
                (رقم_القيد, تاريخ_القيد, وصف_القيد, إجمالي_مدين, إجمالي_دائن, 
                 حالة_القيد, نوع_القيد, المرجع_الخارجي, مركز_التكلفة, ملاحظات, المستخدم, السنة)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, entry_insert_data)
            
            entry_id = self.cursor.lastrowid
            
            # إدراج تفاصيل القيد
            for i, detail in enumerate(details_list, 1):
                # الحصول على اسم الحساب
                account_code = detail.get('كود_الحساب', '')
                self.cursor.execute("SELECT اسم_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
                account_result = self.cursor.fetchone()
                account_name = account_result[0] if account_result else ''
                
                detail_insert_data = (
                    entry_id,
                    entry_number,
                    account_code,
                    account_name,
                    detail.get('وصف_التفصيل', ''),
                    detail.get('مدين', 0),
                    detail.get('دائن', 0),
                    detail.get('مركز_التكلفة', ''),
                    detail.get('ملاحظات', ''),
                    i
                )
                
                self.cursor.execute("""
                    INSERT INTO تفاصيل_القيود_المحاسبية
                    (معرف_القيد, رقم_القيد, كود_الحساب, اسم_الحساب, وصف_التفصيل,
                     مدين, دائن, مركز_التكلفة, ملاحظات, ترتيب_السطر)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, detail_insert_data)
                
                # إضافة الحركة إلى جدول حركات الحسابات
                self.cursor.execute("""
                    INSERT INTO حركات_الحسابات
                    (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن,
                     المرجع, نوع_المستند, رقم_المستند, مركز_التكلفة, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    entry_number,
                    entry_data.get('تاريخ_القيد', date.today()),
                    account_code,
                    detail.get('وصف_التفصيل', ''),
                    detail.get('مدين', 0),
                    detail.get('دائن', 0),
                    entry_data.get('المرجع_الخارجي', ''),
                    entry_data.get('نوع_القيد', 'عام'),
                    entry_number,
                    detail.get('مركز_التكلفة', ''),
                    detail.get('ملاحظات', ''),
                    entry_data.get('المستخدم', 'النظام'),
                    datetime.now().year
                ))
                
                # تحديث رصيد الحساب
                self.update_account_balance(account_code, detail.get('مدين', 0), detail.get('دائن', 0))
            
            self.conn.commit()
            return True, f"تم إنشاء القيد رقم {entry_number} بنجاح"
            
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            return False, f"خطأ في إنشاء القيد: {str(e)}"
        finally:
            self.close_connection()
    
    def update_account_balance(self, account_code, debit_amount, credit_amount):
        """تحديث رصيد الحساب"""
        try:
            # الحصول على نوع الحساب
            self.cursor.execute("SELECT نوع_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
            result = self.cursor.fetchone()
            
            if not result:
                return
                
            account_type = result[0]
            
            # حساب التغيير في الرصيد حسب نوع الحساب
            if account_type == "مدين":  # أصول ومصروفات
                balance_change = debit_amount - credit_amount
            else:  # دائن - خصوم وإيرادات ورأس المال
                balance_change = credit_amount - debit_amount
            
            # تحديث الرصيد
            self.cursor.execute("""
                UPDATE شجرة_الحسابات 
                SET رصيد_مدين = رصيد_مدين + %s,
                    رصيد_دائن = رصيد_دائن + %s,
                    الرصيد_الحالي = الرصيد_الحالي + %s
                WHERE كود_الحساب = %s
            """, (debit_amount, credit_amount, balance_change, account_code))
            
        except Exception as e:
            print(f"خطأ في تحديث رصيد الحساب {account_code}: {e}")

class AutoJournalEntries:
    """نظام القيود التلقائية للمعاملات المالية"""
    
    def __init__(self):
        self.journal_manager = JournalEntryManager()
    
    def create_project_payment_entry(self, payment_data):
        """إنشاء قيد دفعة مشروع"""
        try:
            entry_data = {
                'تاريخ_القيد': payment_data.get('تاريخ_الدفع'),
                'وصف_القيد': f"دفعة مشروع: {payment_data.get('وصف_المدفوع', '')}",
                'نوع_القيد': 'دفعة_مشروع',
                'المرجع_الخارجي': f"PAY-{payment_data.get('id', '')}",
                'مركز_التكلفة': 'CC001',  # مشاريع
                'المستخدم': payment_data.get('المستخدم', 'النظام')
            }
            
            amount = float(payment_data.get('المبلغ_المدفوع', 0))
            
            details = [
                {
                    'كود_الحساب': '1.2.1',  # نقدية بالصندوق
                    'وصف_التفصيل': f"استلام دفعة من {payment_data.get('اسم_العميل', '')}",
                    'مدين': amount,
                    'دائن': 0,
                    'مركز_التكلفة': 'CC001'
                },
                {
                    'كود_الحساب': '3.1.1',  # إيرادات المشاريع
                    'وصف_التفصيل': f"إيراد مشروع: {payment_data.get('اسم_المشروع', '')}",
                    'مدين': 0,
                    'دائن': amount,
                    'مركز_التكلفة': 'CC001'
                }
            ]
            
            return self.journal_manager.create_journal_entry(entry_data, details, auto_approve=True)
            
        except Exception as e:
            return False, f"خطأ في إنشاء قيد دفعة المشروع: {str(e)}"
    
    def create_expense_entry(self, expense_data):
        """إنشاء قيد مصروف"""
        try:
            entry_data = {
                'تاريخ_القيد': expense_data.get('تاريخ_المصروف'),
                'وصف_القيد': f"مصروف: {expense_data.get('المصروف', '')}",
                'نوع_القيد': 'مصروف',
                'المرجع_الخارجي': f"EXP-{expense_data.get('id', '')}",
                'مركز_التكلفة': expense_data.get('مركز_التكلفة', 'CC003'),
                'المستخدم': expense_data.get('المستخدم', 'النظام')
            }
            
            amount = float(expense_data.get('المبلغ', 0))
            expense_account = self.get_expense_account_code(expense_data.get('التصنيف', ''))
            
            details = [
                {
                    'كود_الحساب': expense_account,
                    'وصف_التفصيل': expense_data.get('المصروف', ''),
                    'مدين': amount,
                    'دائن': 0,
                    'مركز_التكلفة': expense_data.get('مركز_التكلفة', 'CC003')
                },
                {
                    'كود_الحساب': '1.2.1',  # نقدية بالصندوق
                    'وصف_التفصيل': f"دفع مصروف لـ {expense_data.get('المستلم', '')}",
                    'مدين': 0,
                    'دائن': amount,
                    'مركز_التكلفة': expense_data.get('مركز_التكلفة', 'CC003')
                }
            ]
            
            return self.journal_manager.create_journal_entry(entry_data, details, auto_approve=True)
            
        except Exception as e:
            return False, f"خطأ في إنشاء قيد المصروف: {str(e)}"
    
    def get_expense_account_code(self, expense_category):
        """الحصول على كود حساب المصروف حسب التصنيف"""
        expense_mapping = {
            'رواتب': '4.1.1',
            'إيجارات': '4.1.2',
            'مستلزمات مكتبية': '4.1.3',
            'صيانة': '4.1.4',
            'مرافق': '4.1.5',
            'وقود ومحروقات': '4.1.6',
            'مصروفات إدارية': '4.2.1',
            'مصروفات تسويق': '4.2.2',
            'مصروفات سفر': '4.2.3',
            'مواد': '4.3.1',
            'عمالة': '4.3.2',
            'مقاولات': '4.3.3',
            'نقل': '4.3.4'
        }
        
        return expense_mapping.get(expense_category, '4.1.1')  # افتراضي: رواتب

# دوال مساعدة للاستخدام في النظام الرئيسي
def create_automatic_journal_entry(transaction_type, transaction_data):
    """إنشاء قيد محاسبي تلقائي للمعاملة"""
    auto_entries = AutoJournalEntries()
    
    if transaction_type == 'payment':
        return auto_entries.create_project_payment_entry(transaction_data)
    elif transaction_type == 'expense':
        return auto_entries.create_expense_entry(transaction_data)
    else:
        return False, "نوع المعاملة غير مدعوم"

if __name__ == "__main__":
    # اختبار النظام
    app = QApplication(sys.argv)
    
    # اختبار إنشاء قيد
    test_payment = {
        'id': 1,
        'تاريخ_الدفع': date.today(),
        'المبلغ_المدفوع': 5000,
        'وصف_المدفوع': 'دفعة أولى',
        'اسم_العميل': 'شركة الاختبار',
        'اسم_المشروع': 'مشروع تجريبي',
        'المستخدم': 'admin'
    }
    
    success, message = create_automatic_journal_entry('payment', test_payment)
    print(f"نتيجة الاختبار: {success}, الرسالة: {message}")
    
    sys.exit(app.exec_())
