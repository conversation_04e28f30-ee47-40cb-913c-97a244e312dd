#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دوال لربط الحسابات في الجداول المختلفة مع شجرة الحسابات
"""

from datetime import datetime

def link_expense_to_account(cursor, expense_id, account_code, username="النظام"):
    """
    ربط مصروف بحساب في شجرة الحسابات
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    expense_id: id المصروف
    account_code: كود الحساب في شجرة الحسابات
    username: اسم المستخدم الذي يقوم بالربط
    """
    year = datetime.now().year
    
    # التحقق من وجود الحساب في شجرة الحسابات
    cursor.execute("SELECT COUNT(*) FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
    if cursor.fetchone()[0] == 0:
        raise ValueError(f"الحساب ذو الكود {account_code} غير موجود في شجرة الحسابات")
    
    # تحديث المصروف بكود الحساب
    cursor.execute("""
        UPDATE الحسابات
        SET كود_الحساب_الشجرة = %s, المستخدم = %s, السنة = %s
        WHERE id = %s
    """, (account_code, username, year, expense_id))
    
    # الحصول على بيانات المصروف
    cursor.execute("SELECT المبلغ, التاريخ FROM الحسابات WHERE id = %s", (expense_id,))
    expense_data = cursor.fetchone()
    if not expense_data:
        return
    
    amount, date_val = expense_data
    
    # إضافة حركة في جدول حركات الحسابات
    cursor.execute("""
        INSERT INTO حركات_الحسابات
        (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن, المرجع, المستخدم, السنة)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        f"EXP-{expense_id}",
        date_val,
        account_code,
        f"مصروف رقم {expense_id}",
        amount,  # مدين
        0,       # دائن
        f"مصروف-{expense_id}",
        username,
        year
    ))
    
    # تحديث رصيد الحساب
    update_account_balance(cursor, account_code, amount, is_debit=True)

def link_project_to_account(cursor, project_id, account_code, username="النظام"):
    """
    ربط مشروع بحساب في شجرة الحسابات
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    project_id: id المشروع
    account_code: كود الحساب في شجرة الحسابات
    username: اسم المستخدم الذي يقوم بالربط
    """
    year = datetime.now().year
    
    # التحقق من وجود الحساب في شجرة الحسابات
    cursor.execute("SELECT COUNT(*) FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
    if cursor.fetchone()[0] == 0:
        raise ValueError(f"الحساب ذو الكود {account_code} غير موجود في شجرة الحسابات")
    
    # تحديث المشروع بكود الحساب
    cursor.execute("""
        UPDATE المشاريع
        SET كود_الحساب_الشجرة = %s, المستخدم = %s, السنة = %s
        WHERE id = %s
    """, (account_code, username, year, project_id))
    
    # الحصول على بيانات المشروع
    cursor.execute("SELECT المبلغ, تاريخ_الإستلام, وصف_المشروع FROM المشاريع WHERE id = %s", (project_id,))
    project_data = cursor.fetchone()
    if not project_data:
        return
    
    amount, date_val, description = project_data
    
    # إضافة حركة في جدول حركات الحسابات
    cursor.execute("""
        INSERT INTO حركات_الحسابات
        (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن, المرجع, المستخدم, السنة)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        f"PRJ-{project_id}",
        date_val,
        account_code,
        f"مشروع: {description}",
        0,       # مدين
        amount,  # دائن
        f"مشروع-{project_id}",
        username,
        year
    ))
    
    # تحديث رصيد الحساب
    update_account_balance(cursor, account_code, amount, is_debit=False)

def link_payment_to_account(cursor, payment_id, account_code, username="النظام"):
    """
    ربط دفعة بحساب في شجرة الحسابات
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    payment_id: id الدفعة
    account_code: كود الحساب في شجرة الحسابات
    username: اسم المستخدم الذي يقوم بالربط
    """
    year = datetime.now().year
    
    # التحقق من وجود الحساب في شجرة الحسابات
    cursor.execute("SELECT COUNT(*) FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
    if cursor.fetchone()[0] == 0:
        raise ValueError(f"الحساب ذو الكود {account_code} غير موجود في شجرة الحسابات")
    
    # تحديث الدفعة بكود الحساب
    cursor.execute("""
        UPDATE المشاريع_المدفوعات
        SET كود_الحساب_الشجرة = %s, المستخدم = %s, السنة = %s
        WHERE id = %s
    """, (account_code, username, year, payment_id))
    
    # الحصول على بيانات الدفعة
    cursor.execute("""
        SELECT المشاريع_المدفوعات.المبلغ_المدفوع, المشاريع_المدفوعات.تاريخ_الدفع, المشاريع_المدفوعات.وصف_المدفوع, المشاريع.وصف_المشروع
        FROM المشاريع_المدفوعات
        JOIN المشاريع ON المشاريع_المدفوعات.معرف_المشروع = المشاريع.id
        WHERE المشاريع_المدفوعات.id = %s
    """, (payment_id,))
    payment_data = cursor.fetchone()
    if not payment_data:
        return
    
    amount, date_val, payment_desc, project_desc = payment_data
    
    # إضافة حركة في جدول حركات الحسابات
    cursor.execute("""
        INSERT INTO حركات_الحسابات
        (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن, المرجع, المستخدم, السنة)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        f"PAY-{payment_id}",
        date_val,
        account_code,
        f"دفعة: {payment_desc} - مشروع: {project_desc}",
        amount,  # مدين
        0,       # دائن
        f"دفعة-{payment_id}",
        username,
        year
    ))
    
    # تحديث رصيد الحساب
    update_account_balance(cursor, account_code, amount, is_debit=True)

def update_account_balance(cursor, account_code, amount, is_debit=True):
    """
    تحديث رصيد الحساب
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    account_code: كود الحساب
    amount: المبلغ
    is_debit: هل هو مدين (True) أم دائن (False)
    """
    # الحصول على نوع الحساب
    cursor.execute("SELECT نوع_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
    result = cursor.fetchone()
    if not result:
        return
    
    account_type = result[0]
    
    # تحديث الرصيد
    if is_debit:
        if account_type == "مدين":
            # زيادة الرصيد المدين والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_مدين = رصيد_مدين + %s, الرصيد_الحالي = الرصيد_الحالي + %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
        else:
            # نقص الرصيد الدائن والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_مدين = رصيد_مدين + %s, الرصيد_الحالي = الرصيد_الحالي - %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
    else:  # دائن
        if account_type == "دائن":
            # زيادة الرصيد الدائن والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_دائن = رصيد_دائن + %s, الرصيد_الحالي = الرصيد_الحالي + %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
        else:
            # نقص الرصيد المدين والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_دائن = رصيد_دائن + %s, الرصيد_الحالي = الرصيد_الحالي - %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
    
    # تحديث الحساب الأب بشكل متكرر
    update_parent_account_balance(cursor, account_code, amount, is_debit)

def update_parent_account_balance(cursor, account_code, amount, is_debit):
    """
    تحديث رصيد الحساب الأب بشكل متكرر
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    account_code: كود الحساب
    amount: المبلغ
    is_debit: هل هو مدين (True) أم دائن (False)
    """
    # الحصول على الحساب الأب
    cursor.execute("SELECT الحساب_الأب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
    result = cursor.fetchone()
    if not result or not result[0]:
        return
    
    parent_code = result[0]
    
    # تحديث رصيد الحساب الأب
    update_account_balance(cursor, parent_code, amount, is_debit)
