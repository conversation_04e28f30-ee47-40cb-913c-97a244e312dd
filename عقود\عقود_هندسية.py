import sys
from PySide6.QtWidgets import (QA<PERSON>lication, QMainWindow, QW<PERSON>t, QVBoxLayout, QHBoxLayout, 
                             QLabel, QLineEdit, QTextEdit, QComboBox, QPushButton, 
                             QTabWidget, QFormLayout, QGroupBox, QScrollArea, QFileDialog,
                             QMessageBox, QDateEdit, QSpinBox, QDoubleSpinBox, QCheckBox)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QTextDocument
from PySide6.QtPrintSupport import QPrinter
from PySide6.QtGui import QPageSize, QPageLayout
import os
import markdown
import datetime

class ContractGeneratorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("منشئ العقود الهندسية")
        self.setGeometry(100, 100, 1000, 800)
        
        # تعيين الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # إنشاء الواجهة الرئيسية
        self.init_ui()
        
    def init_ui(self):
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # عنوان التطبيق
        title_label = QLabel("نظام إنشاء وإدارة العقود الهندسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont("Arial", 16, QFont.Bold)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # تبويبات التطبيق
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)
        
        # إضافة تبويبات
        self.create_contract_selection_tab()
        self.create_design_contract_tab()
        self.create_implementation_contract_tab()
        self.create_preview_tab()
        
        # أزرار التحكم الرئيسية
        control_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ العقد")
        self.save_button.clicked.connect(self.save_contract)
        
        self.print_button = QPushButton("طباعة العقد")
        self.print_button.clicked.connect(self.print_contract)
        
        self.clear_button = QPushButton("مسح البيانات")
        self.clear_button.clicked.connect(self.clear_form)
        
        control_layout.addWidget(self.save_button)
        control_layout.addWidget(self.print_button)
        control_layout.addWidget(self.clear_button)
        
        main_layout.addLayout(control_layout)
        
    def create_contract_selection_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة اختيار نوع العقد
        contract_group = QGroupBox("اختيار نوع العقد")
        contract_layout = QVBoxLayout()
        
        self.contract_type = QComboBox()
        self.contract_type.addItem("عقد تصميم هندسي")
        self.contract_type.addItem("عقد تنفيذ أعمال هندسية")
        self.contract_type.currentIndexChanged.connect(self.contract_type_changed)
        
        contract_layout.addWidget(QLabel("نوع العقد:"))
        contract_layout.addWidget(self.contract_type)
        
        contract_group.setLayout(contract_layout)
        layout.addWidget(contract_group)
        
        # معلومات المشروع
        project_group = QGroupBox("معلومات المشروع")
        project_layout = QFormLayout()
        
        self.project_name = QLineEdit()
        self.project_location = QLineEdit()
        self.project_area = QDoubleSpinBox()
        self.project_area.setRange(0, 1000000)
        self.project_area.setSuffix(" متر مربع")
        
        project_layout.addRow("اسم المشروع:", self.project_name)
        project_layout.addRow("موقع المشروع:", self.project_location)
        project_layout.addRow("المساحة الإجمالية:", self.project_area)
        
        project_group.setLayout(project_layout)
        layout.addWidget(project_group)
        
        # معلومات الطرف الأول (المالك)
        owner_group = QGroupBox("معلومات المالك (الطرف الأول)")
        owner_layout = QFormLayout()
        
        self.owner_name = QLineEdit()
        self.owner_address = QLineEdit()
        self.owner_id = QLineEdit()
        self.owner_phone = QLineEdit()
        self.owner_email = QLineEdit()
        
        owner_layout.addRow("الاسم:", self.owner_name)
        owner_layout.addRow("العنوان:", self.owner_address)
        owner_layout.addRow("رقم الهوية/السجل التجاري:", self.owner_id)
        owner_layout.addRow("رقم الهاتف:", self.owner_phone)
        owner_layout.addRow("البريد الإلكتروني:", self.owner_email)
        
        owner_group.setLayout(owner_layout)
        layout.addWidget(owner_group)
        
        # معلومات الطرف الثاني (المكتب الهندسي/المقاول)
        contractor_group = QGroupBox("معلومات المكتب الهندسي/المقاول (الطرف الثاني)")
        contractor_layout = QFormLayout()
        
        self.contractor_name = QLineEdit()
        self.contractor_address = QLineEdit()
        self.contractor_license = QLineEdit()
        self.contractor_id = QLineEdit()
        self.contractor_phone = QLineEdit()
        self.contractor_email = QLineEdit()
        
        contractor_layout.addRow("الاسم:", self.contractor_name)
        contractor_layout.addRow("العنوان:", self.contractor_address)
        contractor_layout.addRow("رقم الترخيص:", self.contractor_license)
        contractor_layout.addRow("رقم السجل التجاري:", self.contractor_id)
        contractor_layout.addRow("رقم الهاتف:", self.contractor_phone)
        contractor_layout.addRow("البريد الإلكتروني:", self.contractor_email)
        
        contractor_group.setLayout(contractor_layout)
        layout.addWidget(contractor_group)
        
        # تاريخ العقد
        date_group = QGroupBox("تاريخ العقد")
        date_layout = QFormLayout()
        
        self.contract_date = QDateEdit()
        self.contract_date.setCalendarPopup(True)
        self.contract_date.setDate(QDate.currentDate())
        
        date_layout.addRow("تاريخ العقد:", self.contract_date)
        
        date_group.setLayout(date_layout)
        layout.addWidget(date_group)
        
        # إضافة زر للانتقال إلى التبويب المناسب
        next_button = QPushButton("التالي")
        next_button.clicked.connect(self.go_to_contract_tab)
        layout.addWidget(next_button)
        
        # إضافة مساحة فارغة في النهاية
        layout.addStretch()
        
        # إنشاء منطقة تمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_content.setLayout(layout)
        scroll_area.setWidget(scroll_content)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll_area)
        
        self.tabs.addTab(tab, "بيانات العقد الأساسية")
        
    def create_design_contract_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة نطاق الخدمات الهندسية
        services_group = QGroupBox("نطاق الخدمات الهندسية")
        services_layout = QVBoxLayout()
        
        self.design_services = []
        services = [
            "إعداد الدراسات الأولية والتصورات المبدئية",
            "إعداد التصميمات المعمارية",
            "إعداد التصميمات الإنشائية",
            "إعداد التصميمات الكهربائية",
            "إعداد التصميمات الميكانيكية",
            "إعداد التصميمات الصحية",
            "إعداد الرسومات التنفيذية التفصيلية",
            "إعداد المواصفات الفنية",
            "إعداد جداول الكميات وتقدير التكاليف",
            "الحصول على الموافقات والتراخيص اللازمة"
        ]
        
        for service in services:
            checkbox = QCheckBox(service)
            checkbox.setChecked(True)
            self.design_services.append(checkbox)
            services_layout.addWidget(checkbox)
        
        self.other_services = QTextEdit()
        services_layout.addWidget(QLabel("خدمات أخرى:"))
        services_layout.addWidget(self.other_services)
        
        services_group.setLayout(services_layout)
        layout.addWidget(services_group)
        
        # مجموعة الأتعاب وطريقة السداد
        fees_group = QGroupBox("الأتعاب وطريقة السداد")
        fees_layout = QFormLayout()
        
        self.total_fees = QDoubleSpinBox()
        self.total_fees.setRange(0, 10000000)
        self.total_fees.setSuffix(" دينار ليبي")
        
        self.first_payment_percent = QSpinBox()
        self.first_payment_percent.setRange(0, 100)
        self.first_payment_percent.setSuffix(" %")
        self.first_payment_percent.setValue(30)
        
        self.second_payment_percent = QSpinBox()
        self.second_payment_percent.setRange(0, 100)
        self.second_payment_percent.setSuffix(" %")
        self.second_payment_percent.setValue(30)
        
        self.third_payment_percent = QSpinBox()
        self.third_payment_percent.setRange(0, 100)
        self.third_payment_percent.setSuffix(" %")
        self.third_payment_percent.setValue(30)
        
        self.final_payment_percent = QSpinBox()
        self.final_payment_percent.setRange(0, 100)
        self.final_payment_percent.setSuffix(" %")
        self.final_payment_percent.setValue(10)
        
        fees_layout.addRow("قيمة الأتعاب الإجمالية:", self.total_fees)
        fees_layout.addRow("نسبة الدفعة الأولى (عند توقيع العقد):", self.first_payment_percent)
        fees_layout.addRow("نسبة الدفعة الثانية (عند تقديم التصميمات المبدئية):", self.second_payment_percent)
        fees_layout.addRow("نسبة الدفعة الثالثة (عند تقديم التصميمات النهائية):", self.third_payment_percent)
        fees_layout.addRow("نسبة الدفعة النهائية (عند الحصول على التراخيص):", self.final_payment_percent)
        
        fees_group.setLayout(fees_layout)
        layout.addWidget(fees_group)
        
        # مجموعة الجدول الزمني
        timeline_group = QGroupBox("الجدول الزمني")
        timeline_layout = QFormLayout()
        
        self.design_duration = QSpinBox()
        self.design_duration.setRange(1, 1000)
        self.design_duration.setSuffix(" يوم")
        self.design_duration.setValue(90)
        
        timeline_layout.addRow("المدة الإجمالية لإنجاز التصميمات:", self.design_duration)
        
        timeline_group.setLayout(timeline_layout)
        layout.addWidget(timeline_group)
        
        # مجموعة الضمان
        warranty_group = QGroupBox("الضمان")
        warranty_layout = QFormLayout()
        
        self.design_warranty = QSpinBox()
        self.design_warranty.setRange(1, 20)
        self.design_warranty.setSuffix(" سنوات")
        self.design_warranty.setValue(10)
        
        warranty_layout.addRow("مدة ضمان سلامة التصميم:", self.design_warranty)
        
        warranty_group.setLayout(warranty_layout)
        layout.addWidget(warranty_group)
        
        # إضافة زر للمعاينة
        preview_button = QPushButton("معاينة العقد")
        preview_button.clicked.connect(self.preview_design_contract)
        layout.addWidget(preview_button)
        
        # إضافة مساحة فارغة في النهاية
        layout.addStretch()
        
        # إنشاء منطقة تمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_content.setLayout(layout)
        scroll_area.setWidget(scroll_content)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll_area)
        
        self.tabs.addTab(tab, "عقد التصميم")
        
    def create_implementation_contract_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة نطاق الأعمال
        scope_group = QGroupBox("نطاق الأعمال")
        scope_layout = QVBoxLayout()
        
        self.implementation_scope = []
        scopes = [
            "تنفيذ الأعمال المدنية والإنشائية",
            "تنفيذ الأعمال الكهربائية",
            "تنفيذ الأعمال الميكانيكية",
            "تنفيذ الأعمال الصحية",
            "تنفيذ أعمال التشطيبات الداخلية",
            "تنفيذ أعمال التشطيبات الخارجية",
            "توريد وتركيب المواد والمعدات",
            "إجراء الاختبارات اللازمة"
        ]
        
        for scope in scopes:
            checkbox = QCheckBox(scope)
            checkbox.setChecked(True)
            self.implementation_scope.append(checkbox)
            scope_layout.addWidget(checkbox)
        
        self.other_scope = QTextEdit()
        scope_layout.addWidget(QLabel("أعمال أخرى:"))
        scope_layout.addWidget(self.other_scope)
        
        scope_group.setLayout(scope_layout)
        layout.addWidget(scope_group)
        
        # مجموعة قيمة العقد وطريقة السداد
        value_group = QGroupBox("قيمة العقد وطريقة السداد")
        value_layout = QFormLayout()
        
        self.contract_value = QDoubleSpinBox()
        self.contract_value.setRange(0, 100000000)
        self.contract_value.setSuffix(" دينار ليبي")
        
        self.advance_payment_percent = QSpinBox()
        self.advance_payment_percent.setRange(0, 50)
        self.advance_payment_percent.setSuffix(" %")
        self.advance_payment_percent.setValue(20)
        
        self.retention_percent = QSpinBox()
        self.retention_percent.setRange(0, 20)
        self.retention_percent.setSuffix(" %")
        self.retention_percent.setValue(5)
        
        self.final_payment_impl_percent = QSpinBox()
        self.final_payment_impl_percent.setRange(0, 30)
        self.final_payment_impl_percent.setSuffix(" %")
        self.final_payment_impl_percent.setValue(10)
        
        value_layout.addRow("القيمة الإجمالية للعقد:", self.contract_value)
        value_layout.addRow("نسبة الدفعة المقدمة:", self.advance_payment_percent)
        value_layout.addRow("نسبة استقطاع ضمان الأعمال:", self.retention_percent)
        value_layout.addRow("نسبة الدفعة النهائية:", self.final_payment_impl_percent)
        
        value_group.setLayout(value_layout)
        layout.addWidget(value_group)
        
        # مجموعة الجدول الزمني
        timeline_group = QGroupBox("الجدول الزمني")
        timeline_layout = QFormLayout()
        
        self.implementation_duration = QSpinBox()
        self.implementation_duration.setRange(1, 1000)
        self.implementation_duration.setSuffix(" يوم")
        self.implementation_duration.setValue(180)
        
        self.implementation_start_date = QDateEdit()
        self.implementation_start_date.setCalendarPopup(True)
        self.implementation_start_date.setDate(QDate.currentDate().addDays(30))
        
        timeline_layout.addRow("مدة تنفيذ المشروع:", self.implementation_duration)
        timeline_layout.addRow("تاريخ بدء التنفيذ:", self.implementation_start_date)
        
        timeline_group.setLayout(timeline_layout)
        layout.addWidget(timeline_group)
        
        # مجموعة الضمانات والتأمينات
        guarantees_group = QGroupBox("الضمانات والتأمينات")
        guarantees_layout = QFormLayout()
        
        self.performance_guarantee = QSpinBox()
        self.performance_guarantee.setRange(0, 20)
        self.performance_guarantee.setSuffix(" %")
        self.performance_guarantee.setValue(10)
        
        self.warranty_period = QSpinBox()
        self.warranty_period.setRange(0, 60)
        self.warranty_period.setSuffix(" شهر")
        self.warranty_period.setValue(12)
        
        self.delay_penalty = QDoubleSpinBox()
        self.delay_penalty.setRange(0, 100000)
        self.delay_penalty.setSuffix(" دينار ليبي/يوم")
        self.delay_penalty.setValue(1000)
        
        self.max_penalty = QSpinBox()
        self.max_penalty.setRange(0, 30)
        self.max_penalty.setSuffix(" %")
        self.max_penalty.setValue(10)
        
        guarantees_layout.addRow("نسبة ضمان حسن التنفيذ:", self.performance_guarantee)
        guarantees_layout.addRow("فترة الضمان بعد الاستلام المؤقت:", self.warranty_period)
        guarantees_layout.addRow("غرامة التأخير اليومية:", self.delay_penalty)
        guarantees_layout.addRow("الحد الأقصى لغرامة التأخير:", self.max_penalty)
        
        guarantees_group.setLayout(guarantees_layout)
        layout.addWidget(guarantees_group)
        
        # إضافة زر للمعاينة
        preview_button = QPushButton("معاينة العقد")
        preview_button.clicked.connect(self.preview_implementation_contract)
        layout.addWidget(preview_button)
        
        # إضافة مساحة فارغة في النهاية
        layout.addStretch()
        
        # إنشاء منطقة تمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_content.setLayout(layout)
        scroll_area.setWidget(scroll_content)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll_area)
        
        self.tabs.addTab(tab, "عقد التنفيذ")
        
    def create_preview_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        
        layout.addWidget(self.preview_text)
        
        self.tabs.addTab(tab, "معاينة العقد")
        
    def contract_type_changed(self, index):
        # تغيير التبويب النشط بناءً على نوع العقد المختار
        if index == 0:  # عقد تصميم
            self.tabs.setTabEnabled(1, True)
            self.tabs.setTabEnabled(2, False)
        else:  # عقد تنفيذ
            self.tabs.setTabEnabled(1, False)
            self.tabs.setTabEnabled(2, True)
    
    def go_to_contract_tab(self):
        # الانتقال إلى التبويب المناسب بناءً على نوع العقد المختار
        if self.contract_type.currentIndex() == 0:  # عقد تصميم
            self.tabs.setCurrentIndex(1)
        else:  # عقد تنفيذ
            self.tabs.setCurrentIndex(2)
    
    def preview_design_contract(self):
        # توليد نص عقد التصميم
        contract_text = self.generate_design_contract()
        
        # عرض النص في تبويب المعاينة
        self.preview_text.setMarkdown(contract_text)
        
        # الانتقال إلى تبويب المعاينة
        self.tabs.setCurrentIndex(3)
    
    def preview_implementation_contract(self):
        # توليد نص عقد التنفيذ
        contract_text = self.generate_implementation_contract()
        
        # عرض النص في تبويب المعاينة
        self.preview_text.setMarkdown(contract_text)
        
        # الانتقال إلى تبويب المعاينة
        self.tabs.setCurrentIndex(3)
    
    def generate_design_contract(self):
        # استخراج البيانات من الحقول
        contract_date = self.contract_date.date().toString("yyyy/MM/dd")
        
        # تنسيق التاريخ بالعربية
        day = self.contract_date.date().day()
        month = self.get_arabic_month(self.contract_date.date().month())
        year = self.contract_date.date().year()
        arabic_date = f"{day} من {month} {year}"
        
        # بيانات المشروع
        project_name = self.project_name.text()
        project_location = self.project_location.text()
        project_area = self.project_area.value()
        
        # بيانات المالك
        owner_name = self.owner_name.text()
        owner_address = self.owner_address.text()
        owner_id = self.owner_id.text()
        owner_phone = self.owner_phone.text()
        owner_email = self.owner_email.text()
        
        # بيانات المكتب الهندسي
        contractor_name = self.contractor_name.text()
        contractor_address = self.contractor_address.text()
        contractor_license = self.contractor_license.text()
        contractor_id = self.contractor_id.text()
        contractor_phone = self.contractor_phone.text()
        contractor_email = self.contractor_email.text()
        
        # نطاق الخدمات
        services = []
        for checkbox in self.design_services:
            if checkbox.isChecked():
                services.append(checkbox.text())
        
        other_services = self.other_services.toPlainText()
        if other_services:
            services.append(other_services)
        
        services_text = "\n".join([f"{i+1}. {service}" for i, service in enumerate(services)])
        
        # الأتعاب
        total_fees = self.total_fees.value()
        first_payment_percent = self.first_payment_percent.value()
        second_payment_percent = self.second_payment_percent.value()
        third_payment_percent = self.third_payment_percent.value()
        final_payment_percent = self.final_payment_percent.value()
        
        first_payment = total_fees * first_payment_percent / 100
        second_payment = total_fees * second_payment_percent / 100
        third_payment = total_fees * third_payment_percent / 100
        final_payment = total_fees * final_payment_percent / 100
        
        # الجدول الزمني
        design_duration = self.design_duration.value()
        
        # الضمان
        design_warranty = self.design_warranty.value()
        
        # توليد نص العقد
        contract = f"""## عقد تصميم هندسي

إنه في يوم {arabic_date} الموافق {contract_date}

تم الاتفاق بين كل من:

### أولاً: الطرف الأول (المالك/العميل)

السيد/ السادة: {owner_name}

العنوان: {owner_address}

رقم الهوية/السجل التجاري: {owner_id}

الهاتف: {owner_phone} البريد الإلكتروني: {owner_email}

ويشار إليه فيما بعد بـ "الطرف الأول" أو "المالك"

### ثانياً: الطرف الثاني (المكتب الهندسي)

السيد/ السادة: {contractor_name}

العنوان: {contractor_address}

رقم الترخيص: {contractor_license}

رقم السجل التجاري: {contractor_id}

الهاتف: {contractor_phone} البريد الإلكتروني: {contractor_email}

ويشار إليه فيما بعد بـ "الطرف الثاني" أو "المكتب الهندسي"

وبعد أن أقر الطرفان بأهليتهما القانونية للتعاقد، فقد اتفقا على ما يلي:

### البند الأول: موضوع العقد

يلتزم الطرف الثاني بموجب هذا العقد بإعداد التصميمات والرسومات الهندسية الكاملة لمشروع:
{project_name}
الكائن بمنطقة: {project_location}
مساحة إجمالية: {project_area} متر مربع
وذلك وفقاً للمواصفات والشروط المبينة في هذا العقد وملاحقه.

### البند الثاني: نطاق الخدمات الهندسية

يشمل نطاق الخدمات الهندسية التي يلتزم الطرف الثاني بتقديمها ما يلي:

{services_text}

وتفاصيل هذه الخدمات موضحة في الملحق رقم (1) المرفق بهذا العقد.

### البند الثالث: التزامات الطرف الثاني (المكتب الهندسي)

يلتزم الطرف الثاني بما يلي:

1. إعداد التصميمات والرسومات وفقاً للمعايير والمواصفات الهندسية المعتمدة في ليبيا، وبما يتوافق مع القوانين واللوائح المنظمة للبناء والتشييد.
2. تقديم التصميمات في المواعيد المحددة في الجدول الزمني المرفق بهذا العقد.
3. تعديل التصميمات وفقاً لملاحظات الطرف الأول والجهات المختصة دون مقابل إضافي، شريطة أن تكون هذه التعديلات ضمن نطاق العمل الأصلي وليست تغييرات جوهرية في فكرة المشروع.
4. الحصول على موافقة الطرف الأول على التصميمات قبل تقديمها للجهات المختصة.
5. ضمان سلامة التصميم من الأخطاء الهندسية لمدة {design_warranty} سنوات من تاريخ الانتهاء من تنفيذ المشروع.
6. الالتزام بالسرية التامة وعدم إفشاء أية معلومات تتعلق بالمشروع.
7. عدم التنازل عن العقد أو إسناد العمل لطرف ثالث دون موافقة كتابية من الطرف الأول.

### البند الرابع: التزامات الطرف الأول (المالك)

يلتزم الطرف الأول بما يلي:

1. تزويد الطرف الثاني بجميع البيانات والمعلومات اللازمة لإعداد التصميمات.
2. تقديم المتطلبات والاحتياجات الخاصة بالمشروع بشكل واضح ومكتوب.
3. مراجعة واعتماد التصميمات المقدمة من الطرف الثاني خلال مدة أقصاها 14 يوم من تاريخ تقديمها.
4. سداد الأتعاب المستحقة للطرف الثاني في مواعيدها المحددة.
5. الحصول على الموافقات اللازمة من الجهات المعنية (إذا كان ذلك من مسؤولياته وفقاً للعقد).

### البند الخامس: الأتعاب وطريقة السداد

1. قيمة الأتعاب الإجمالية: {total_fees} دينار ليبي (فقط {self.number_to_arabic_words(total_fees)} دينار ليبي لا غير).
2. تشمل هذه الأتعاب جميع الخدمات المذكورة في البند الثاني من هذا العقد.
3. تسدد الأتعاب على النحو التالي:
   - دفعة أولى: {first_payment} دينار ليبي ({first_payment_percent}%) عند توقيع العقد.
   - دفعة ثانية: {second_payment} دينار ليبي ({second_payment_percent}%) عند تقديم التصميمات المبدئية واعتمادها من الطرف الأول.
   - دفعة ثالثة: {third_payment} دينار ليبي ({third_payment_percent}%) عند تقديم التصميمات النهائية والرسومات التنفيذية.
   - دفعة رابعة: {final_payment} دينار ليبي ({final_payment_percent}%) عند الحصول على الموافقات والتراخيص اللازمة من الجهات المختصة.
4. يتحمل الطرف الثاني كافة الضرائب والرسوم المستحقة على هذا العقد وفقاً للقوانين الليبية.

### البند السادس: الجدول الزمني

1. المدة الإجمالية لإنجاز جميع الخدمات المطلوبة: {design_duration} يوم من تاريخ توقيع العقد.
2. تقسم هذه المدة على مراحل العمل المختلفة وفقاً للجدول الزمني المرفق بالملحق رقم (2).
3. لا تشمل هذه المدة الفترات اللازمة لمراجعة واعتماد التصميمات من قبل الطرف الأول أو الجهات المختصة.
4. في حالة تأخر الطرف الأول في تقديم البيانات المطلوبة أو اعتماد التصميمات، تمدد مدة العقد بما يعادل فترة التأخير.

### البند السابع: التعديلات والتغييرات

1. يحق للطرف الأول طلب إجراء تعديلات على التصميمات خلال مرحلة التصميم المبدئي دون مقابل إضافي.
2. التعديلات المطلوبة بعد اعتماد التصميمات النهائية تعتبر أعمالاً إضافية وتستحق أتعاباً إضافية يتفق عليها الطرفان.
3. في حالة طلب تعديلات جوهرية تؤدي إلى تغيير فكرة المشروع بالكامل، يتم الاتفاق على أتعاب إضافية وتمديد للمدة الزمنية.
4. يجب أن تكون جميع طلبات التعديل مكتوبة وموقعة من الطرف الأول.

### البند الثامن: المسؤولية والضمان

1. يضمن الطرف الثاني سلامة التصميمات من الناحية الفنية والهندسية لمدة {design_warranty} سنوات من تاريخ الانتهاء من تنفيذ المشروع.
2. يكون الطرف الثاني مسؤولاً عن أية أخطاء أو عيوب في التصميم تظهر خلال فترة الضمان، ويلتزم بإصلاحها على نفقته الخاصة.
3. لا يشمل الضمان العيوب الناتجة عن سوء التنفيذ أو استخدام مواد مخالفة للمواصفات أو إجراء تعديلات على التصميم دون موافقة الطرف الثاني.
4. يلتزم الطرف الثاني بالتأمين على مسؤوليته المهنية لدى إحدى شركات التأمين المعتمدة في ليبيا.

### البند التاسع: حقوق الملكية الفكرية

1. تؤول ملكية التصميمات والرسومات إلى الطرف الأول بعد سداد كامل الأتعاب المستحقة.
2. يحتفظ الطرف الثاني بحقه في استخدام التصميمات كمرجع في أعماله المستقبلية دون الإخلال بسرية المعلومات.
3. لا يحق للطرف الأول إعادة استخدام التصميمات في مشروع آخر دون موافقة كتابية من الطرف الثاني ودفع أتعاب إضافية.

### البند العاشر: فسخ العقد وإنهاؤه

1. يحق للطرف الأول فسخ العقد في الحالات التالية:
   - إذا تأخر الطرف الثاني في تنفيذ التزاماته لمدة تزيد عن 30 يوم دون عذر مقبول.
   - إذا ثبت عدم قدرة الطرف الثاني على تنفيذ العمل بالجودة المطلوبة.
   - إذا أخل الطرف الثاني بأي من التزاماته الجوهرية المنصوص عليها في العقد.

2. يحق للطرف الثاني فسخ العقد في الحالات التالية:
   - إذا تأخر الطرف الأول في سداد الأتعاب المستحقة لمدة تزيد عن 30 يوم من تاريخ استحقاقها.
   - إذا لم يقم الطرف الأول بتوفير البيانات والمعلومات اللازمة للعمل.
   - إذا طلب الطرف الأول تعديلات جوهرية متكررة تتعارض مع أصول المهنة أو القوانين واللوائح.

3. في حالة فسخ العقد، يستحق الطرف الثاني الأتعاب عن الأعمال التي تم إنجازها فعلياً حتى تاريخ الفسخ.

4. في حالة وفاة الطرف الثاني أو فقدانه الأهلية، ينتهي العقد تلقائياً ما لم يتفق الطرف الأول مع ورثته أو من يمثلهم قانوناً على استمرار العمل.

### البند الحادي عشر: القوة القاهرة

1. لا يعتبر أي من الطرفين مخلاً بالتزاماته إذا كان عدم الوفاء بها ناتجاً عن قوة قاهرة خارجة عن إرادته.
2. يجب على الطرف المتأثر بالقوة القاهرة إخطار الطرف الآخر كتابياً خلال 7 أيام من وقوعها.
3. تمدد مدة العقد بما يعادل فترة استمرار القوة القاهرة.
4. إذا استمرت القوة القاهرة لمدة تزيد عن 90 يوم، يحق لأي من الطرفين إنهاء العقد دون تحمل أية مسؤولية.

### البند الثاني عشر: تسوية النزاعات

1. يسعى الطرفان لحل أي خلاف ينشأ عن تنفيذ هذا العقد بالطرق الودية.
2. في حالة تعذر الحل الودي، يتم اللجوء إلى التحكيم وفقاً لقواعد التحكيم المعمول بها في ليبيا.
3. في حالة اللجوء إلى القضاء، تكون محاكم ليبيا هي المختصة بنظر النزاع.

### البند الثالث عشر: أحكام عامة

1. يخضع هذا العقد للقوانين واللوائح المعمول بها في دولة ليبيا.
2. لا يجوز تعديل أي بند من بنود هذا العقد إلا بموافقة كتابية من الطرفين.
3. تعتبر المراسلات المتبادلة بين الطرفين جزءاً لا يتجزأ من هذا العقد.
4. العناوين المذكورة في صدر هذا العقد هي محل الإخطارات القانونية بين الطرفين.
5. حرر هذا العقد من نسختين أصليتين، بيد كل طرف نسخة للعمل بموجبها.

### البند الرابع عشر: الملاحق

تعتبر الملاحق التالية جزءاً لا يتجزأ من هذا العقد:
1. ملحق رقم (1): نطاق الخدمات الهندسية التفصيلي.
2. ملحق رقم (2): الجدول الزمني للتنفيذ.
3. ملحق رقم (3): الأتعاب وطريقة السداد.
4. ملحق رقم (4): المواصفات الفنية المطلوبة.

وإشهاداً على ما تقدم، تم التوقيع:

الطرف الأول (المالك)                                 الطرف الثاني (المكتب الهندسي)

الاسم: .......................                        الاسم: .......................

التوقيع: .....................                        التوقيع: .....................

التاريخ: .....................                        التاريخ: .....................

الشهود:

1- الاسم: ....................... التوقيع: .....................

2- الاسم: ....................... التوقيع: .....................
"""
        
        return contract
    
    def generate_implementation_contract(self):
        # استخراج البيانات من الحقول
        contract_date = self.contract_date.date().toString("yyyy/MM/dd")
        
        # تنسيق التاريخ بالعربية
        day = self.contract_date.date().day()
        month = self.get_arabic_month(self.contract_date.date().month())
        year = self.contract_date.date().year()
        arabic_date = f"{day} من {month} {year}"
        
        # بيانات المشروع
        project_name = self.project_name.text()
        project_location = self.project_location.text()
        project_area = self.project_area.value()
        
        # بيانات المالك
        owner_name = self.owner_name.text()
        owner_address = self.owner_address.text()
        owner_id = self.owner_id.text()
        owner_phone = self.owner_phone.text()
        owner_email = self.owner_email.text()
        
        # بيانات المقاول
        contractor_name = self.contractor_name.text()
        contractor_address = self.contractor_address.text()
        contractor_license = self.contractor_license.text()
        contractor_id = self.contractor_id.text()
        contractor_phone = self.contractor_phone.text()
        contractor_email = self.contractor_email.text()
        
        # نطاق الأعمال
        scopes = []
        for checkbox in self.implementation_scope:
            if checkbox.isChecked():
                scopes.append(checkbox.text())
        
        other_scope = self.other_scope.toPlainText()
        if other_scope:
            scopes.append(other_scope)
        
        scopes_text = "\n".join([f"{i+1}. {scope}" for i, scope in enumerate(scopes)])
        
        # قيمة العقد
        contract_value = self.contract_value.value()
        advance_payment_percent = self.advance_payment_percent.value()
        retention_percent = self.retention_percent.value()
        final_payment_impl_percent = self.final_payment_impl_percent.value()
        
        advance_payment = contract_value * advance_payment_percent / 100
        
        # الجدول الزمني
        implementation_duration = self.implementation_duration.value()
        implementation_start_date = self.implementation_start_date.date().toString("yyyy/MM/dd")
        
        # تنسيق تاريخ البدء بالعربية
        start_day = self.implementation_start_date.date().day()
        start_month = self.get_arabic_month(self.implementation_start_date.date().month())
        start_year = self.implementation_start_date.date().year()
        arabic_start_date = f"{start_day} من {start_month} {start_year}"
        
        # تاريخ الانتهاء المتوقع
        end_date = self.implementation_start_date.date().addDays(implementation_duration)
        end_date_str = end_date.toString("yyyy/MM/dd")
        
        # تنسيق تاريخ الانتهاء بالعربية
        end_day = end_date.day()
        end_month = self.get_arabic_month(end_date.month())
        end_year = end_date.year()
        arabic_end_date = f"{end_day} من {end_month} {end_year}"
        
        # الضمانات
        performance_guarantee = self.performance_guarantee.value()
        warranty_period = self.warranty_period.value()
        delay_penalty = self.delay_penalty.value()
        max_penalty = self.max_penalty.value()
        
        # توليد نص العقد
        contract = f"""## عقد تنفيذ أعمال هندسية

إنه في يوم {arabic_date} الموافق {contract_date}

تم الاتفاق بين كل من:

### أولاً: الطرف الأول (المالك/العميل)

السيد/ السادة: {owner_name}

العنوان: {owner_address}

رقم الهوية/السجل التجاري: {owner_id}

الهاتف: {owner_phone} البريد الإلكتروني: {owner_email}

ويشار إليه فيما بعد بـ "الطرف الأول" أو "المالك"

### ثانياً: الطرف الثاني (المقاول/المنفذ)

السيد/ السادة: {contractor_name}

العنوان: {contractor_address}

رقم الترخيص: {contractor_license}

رقم السجل التجاري: {contractor_id}

الهاتف: {contractor_phone} البريد الإلكتروني: {contractor_email}

ويشار إليه فيما بعد بـ "الطرف الثاني" أو "المقاول"

وبعد أن أقر الطرفان بأهليتهما القانونية للتعاقد، فقد اتفقا على ما يلي:

### البند الأول: موضوع العقد

يلتزم الطرف الثاني بموجب هذا العقد بتنفيذ أعمال مشروع:
{project_name}
الكائن بمنطقة: {project_location}
مساحة إجمالية: {project_area} متر مربع
وذلك وفقاً للمخططات والتصاميم والمواصفات الفنية المعتمدة والمرفقة بهذا العقد.

### البند الثاني: نطاق الأعمال

يشمل نطاق الأعمال التي يلتزم الطرف الثاني بتنفيذها ما يلي:

{scopes_text}

وتفاصيل هذه الأعمال موضحة في جداول الكميات والمواصفات الفنية المرفقة بالملحق رقم (1).

### البند الثالث: المستندات التعاقدية

تعتبر المستندات التالية جزءاً لا يتجزأ من هذا العقد وتقرأ وتفسر معه كوحدة واحدة:

1. وثيقة العقد الأساسية (هذا المستند).
2. المخططات والتصاميم المعتمدة.
3. المواصفات الفنية.
4. جداول الكميات والأسعار.
5. الجدول الزمني للتنفيذ.
6. الشروط الخاصة (إن وجدت).
7. أية ملاحق أو تعديلات يتم الاتفاق عليها لاحقاً وتوقيعها من الطرفين.

في حالة وجود تعارض بين هذه المستندات، تكون الأولوية وفقاً للترتيب المذكور أعلاه.

### البند الرابع: التزامات الطرف الثاني (المقاول)

يلتزم الطرف الثاني بما يلي:

1. تنفيذ الأعمال وفقاً للمخططات والمواصفات المعتمدة وطبقاً لأصول الصناعة وقواعد الفن الهندسي.
2. توفير العمالة المؤهلة والمعدات والأدوات اللازمة لتنفيذ الأعمال.
3. استخدام مواد ذات جودة عالية مطابقة للمواصفات المعتمدة.
4. الالتزام بالجدول الزمني المتفق عليه.
5. الالتزام بتعليمات المهندس المشرف وتنفيذ الملاحظات الصادرة عنه.
6. المحافظة على سلامة العاملين وتطبيق إجراءات الأمن والسلامة في موقع العمل.
7. تقديم تقارير دورية عن سير العمل وفقاً للنموذج المعتمد من الطرف الأول.
8. إصلاح أية عيوب أو أخطاء في التنفيذ على نفقته الخاصة.
9. تنظيف الموقع وإزالة المخلفات بشكل دوري وعند الانتهاء من الأعمال.
10. الحصول على التراخيص والموافقات اللازمة من الجهات المختصة (إذا كانت من مسؤولياته).
11. تسليم المشروع خالياً من العيوب وصالحاً للاستخدام.

### البند الخامس: التزامات الطرف الأول (المالك)

يلتزم الطرف الأول بما يلي:

1. تسليم الموقع للطرف الثاني خالياً من العوائق والموانع القانونية والمادية.
2. توفير المخططات والتصاميم المعتمدة والمواصفات الفنية.
3. تعيين مهندس مشرف أو استشاري للإشراف على تنفيذ الأعمال.
4. سداد المستحقات المالية في مواعيدها المحددة.
5. مراجعة واعتماد المواد قبل استخدامها في المشروع.
6. معاينة واستلام الأعمال المنفذة في المواعيد المحددة.
7. الحصول على التراخيص والموافقات اللازمة من الجهات المختصة (إذا كانت من مسؤولياته).

### البند السادس: قيمة العقد وطريقة السداد

1. القيمة الإجمالية للعقد: {contract_value} دينار ليبي (فقط {self.number_to_arabic_words(contract_value)} دينار ليبي لا غير).
2. تشمل هذه القيمة جميع الأعمال المذكورة في جداول الكميات المرفقة بالملحق رقم (1).
3. تسدد قيمة العقد على النحو التالي:
   - دفعة مقدمة: {advance_payment} دينار ليبي ({advance_payment_percent}%) عند توقيع العقد وتسليم الموقع.
   - دفعات مرحلية: تصرف شهرياً وفقاً للأعمال المنفذة فعلياً بعد اعتمادها من المهندس المشرف.
   - دفعة ختامية: {contract_value * final_payment_impl_percent / 100} دينار ليبي ({final_payment_impl_percent}%) بعد الاستلام النهائي للمشروع وانتهاء فترة الضمان.
4. يتم صرف الدفعات المرحلية بناءً على مستخلصات الأعمال المنفذة والمعتمدة من المهندس المشرف.
5. يتم خصم نسبة {retention_percent}% من قيمة كل مستخلص كضمان أعمال، وترد بعد الاستلام النهائي وانتهاء فترة الضمان.
6. يتحمل الطرف الثاني كافة الضرائب والرسوم المستحقة على هذا العقد وفقاً للقوانين الليبية.

### البند السابع: الجدول الزمني

1. مدة تنفيذ المشروع: {implementation_duration} يوم من تاريخ تسليم الموقع.
2. تاريخ بدء التنفيذ: {arabic_start_date} الموافق {implementation_start_date}
3. تاريخ الانتهاء المتوقع: {arabic_end_date} الموافق {end_date_str}
4. يتم تقسيم المشروع إلى مراحل وفقاً للجدول الزمني المرفق بالملحق رقم (2).
5. لا تشمل هذه المدة فترات التوقف المعتمدة من الطرف الأول أو المهندس المشرف.
6. في حالة وجود ظروف خارجة عن إرادة الطرف الثاني تؤدي إلى تأخير التنفيذ، يتم تمديد مدة العقد بموافقة كتابية من الطرف الأول.

### البند الثامن: الضمانات والتأمينات

1. يقدم الطرف الثاني خطاب ضمان بنكي غير مشروط بقيمة {contract_value * performance_guarantee / 100} دينار ليبي ({performance_guarantee}% من قيمة العقد) كضمان حسن تنفيذ.
2. يقدم الطرف الثاني خطاب ضمان بنكي غير مشروط بقيمة الدفعة المقدمة، ويتم تخفيضه تدريجياً مع تقدم العمل.
3. يلتزم الطرف الثاني بتقديم وثائق التأمين التالية:
   - تأمين شامل على المشروع ضد جميع الأخطار.
   - تأمين المسؤولية المدنية تجاه الغير.
   - تأمين على العمال والمعدات.
4. فترة الضمان: {warranty_period} شهر من تاريخ الاستلام المؤقت للمشروع.
5. يلتزم الطرف الثاني خلال فترة الضمان بإصلاح أية عيوب تظهر في المشروع على نفقته الخاصة.
6. يتم رد ضمان حسن التنفيذ بعد انتهاء فترة الضمان والاستلام النهائي للمشروع.

### البند التاسع: الإشراف على التنفيذ

1. يعين الطرف الأول مهندساً مشرفاً أو استشارياً للإشراف على تنفيذ المشروع.
2. يكون للمهندس المشرف الصلاحيات التالية:
   - مراقبة جودة التنفيذ والمواد المستخدمة.
   - اعتماد المواد قبل استخدامها في المشروع.
   - إصدار التعليمات والملاحظات اللازمة لضمان جودة التنفيذ.
   - اعتماد مستخلصات الأعمال المنفذة.
   - إيقاف العمل في حالة وجود مخالفات جسيمة للمواصفات.
3. يلتزم الطرف الثاني بتنفيذ تعليمات وملاحظات المهندس المشرف.
4. في حالة وجود خلاف فني بين الطرف الثاني والمهندس المشرف، يتم الرجوع إلى الطرف الأول للفصل فيه.

### البند العاشر: التغييرات والأعمال الإضافية

1. يحق للطرف الأول طلب إجراء تغييرات أو أعمال إضافية بنسبة لا تتجاوز 15% من قيمة العقد.
2. يتم تقدير قيمة التغييرات والأعمال الإضافية وفقاً للأسعار الواردة في جداول الكميات.
3. في حالة عدم وجود بنود مماثلة في جداول الكميات، يتم الاتفاق على أسعار جديدة.
4. يجب أن تكون جميع طلبات التغيير أو الأعمال الإضافية مكتوبة وموقعة من الطرف الأول أو المهندس المشرف.
5. يتم تعديل مدة التنفيذ بما يتناسب مع حجم التغييرات والأعمال الإضافية.

### البند الحادي عشر: الغرامات والجزاءات

1. في حالة تأخر الطرف الثاني عن إنجاز المشروع في الموعد المحدد، يتم تطبيق غرامة تأخير قدرها {delay_penalty} دينار ليبي عن كل يوم تأخير.
2. الحد الأقصى لغرامة التأخير هو {max_penalty}% من القيمة الإجمالية للعقد.
3. في حالة مخالفة المواصفات الفنية، يحق للطرف الأول فرض غرامة قدرها 5% من قيمة البند المخالف.
4. لا تطبق غرامات التأخير في الحالات التالية:
   - التأخير الناتج عن تعديلات أو أعمال إضافية طلبها الطرف الأول.
   - التأخير الناتج عن ظروف قاهرة خارجة عن إرادة الطرف الثاني.
   - التأخير الناتج عن عدم التزام الطرف الأول بتوفير المخططات أو المواد أو سداد المستحقات في مواعيدها.

### البند الثاني عشر: الاستلام المؤقت والنهائي

1. يتم الاستلام المؤقت للمشروع بعد إنجاز جميع الأعمال وفقاً للمواصفات المعتمدة.
2. يقوم الطرف الأول والمهندس المشرف بمعاينة المشروع وإعداد محضر استلام مؤقت.
3. في حالة وجود ملاحظات، يلتزم الطرف الثاني بإصلاحها خلال مدة 14 يوم من تاريخ المعاينة.
4. تبدأ فترة الضمان من تاريخ الاستلام المؤقت.
5. يتم الاستلام النهائي للمشروع بعد انتهاء فترة الضمان والتأكد من سلامة المشروع وخلوه من العيوب.
6. يتم إعداد محضر استلام نهائي موقع من الطرفين.
7. يلتزم الطرف الثاني بتسليم جميع المستندات والشهادات والضمانات المتعلقة بالمشروع عند الاستلام النهائي.

### البند الثالث عشر: فسخ العقد وإنهاؤه

1. يحق للطرف الأول فسخ العقد في الحالات التالية:
   - إذا تأخر الطرف الثاني في بدء التنفيذ لمدة تزيد عن 30 يوم من تاريخ تسليم الموقع.
   - إذا توقف الطرف الثاني عن العمل دون سبب مقبول لمدة تزيد عن 15 يوم متصلة.
   - إذا ثبت تقصير الطرف الثاني أو إهماله في تنفيذ الأعمال وفقاً للمواصفات المعتمدة.
   - إذا تنازل الطرف الثاني عن العقد أو أسند تنفيذه لطرف ثالث دون موافقة كتابية من الطرف الأول.
   - إذا أفلس الطرف الثاني أو أشهر إعساره.

2. يحق للطرف الثاني فسخ العقد في الحالات التالية:
   - إذا تأخر الطرف الأول في تسليم الموقع لمدة تزيد عن 30 يوم من تاريخ توقيع العقد.
   - إذا تأخر الطرف الأول في سداد المستحقات المالية لمدة تزيد عن 60 يوم من تاريخ استحقاقها.
   - إذا أوقف الطرف الأول تنفيذ المشروع لمدة تزيد عن 60 يوم لأسباب لا علاقة للطرف الثاني بها.

3. في حالة فسخ العقد، يتم تسوية المستحقات على النحو التالي:
   - يستحق الطرف الثاني قيمة الأعمال المنفذة فعلياً والمعتمدة من المهندس المشرف.
   - يستحق الطرف الثاني قيمة المواد الموردة للموقع والمعتمدة من المهندس المشرف.
   - يتحمل الطرف المتسبب في الفسخ التعويضات المترتبة على ذلك وفقاً للقوانين المعمول بها.

### البند الرابع عشر: القوة القاهرة

1. لا يعتبر أي من الطرفين مخلاً بالتزاماته إذا كان عدم الوفاء بها ناتجاً عن قوة قاهرة خارجة عن إرادته.
2. يجب على الطرف المتأثر بالقوة القاهرة إخطار الطرف الآخر كتابياً خلال 7 أيام من وقوعها.
3. تمدد مدة العقد بما يعادل فترة استمرار القوة القاهرة.
4. إذا استمرت القوة القاهرة لمدة تزيد عن 90 يوم، يحق لأي من الطرفين إنهاء العقد دون تحمل أية مسؤولية.

### البند الخامس عشر: تسوية النزاعات

1. يسعى الطرفان لحل أي خلاف ينشأ عن تنفيذ هذا العقد بالطرق الودية.
2. في حالة تعذر الحل الودي، يتم اللجوء إلى التحكيم وفقاً لقواعد التحكيم المعمول بها في ليبيا.
3. في حالة اللجوء إلى القضاء، تكون محاكم ليبيا هي المختصة بنظر النزاع.

### البند السادس عشر: أحكام عامة

1. يخضع هذا العقد للقوانين واللوائح المعمول بها في دولة ليبيا.
2. لا يجوز تعديل أي بند من بنود هذا العقد إلا بموافقة كتابية من الطرفين.
3. تعتبر المراسلات المتبادلة بين الطرفين جزءاً لا يتجزأ من هذا العقد.
4. العناوين المذكورة في صدر هذا العقد هي محل الإخطارات القانونية بين الطرفين.
5. حرر هذا العقد من نسختين أصليتين، بيد كل طرف نسخة للعمل بموجبها.

### البند السابع عشر: الملاحق

تعتبر الملاحق التالية جزءاً لا يتجزأ من هذا العقد:
1. ملحق رقم (1): جداول الكميات والمواصفات الفنية.
2. ملحق رقم (2): الجدول الزمني للتنفيذ.
3. ملحق رقم (3): المخططات والتصاميم المعتمدة.
4. ملحق رقم (4): نماذج الضمانات والتأمينات.
5. ملحق رقم (5): الشروط الخاصة (إن وجدت).

وإشهاداً على ما تقدم، تم التوقيع:

الطرف الأول (المالك)                                 الطرف الثاني (المقاول)
الاسم: .......................                        الاسم: .......................
التوقيع: .....................                        التوقيع: .....................
التاريخ: .....................                        التاريخ: .....................

الشهود:
1- الاسم: ....................... التوقيع: .....................
2- الاسم: ....................... التوقيع: .....................
"""
        
        return contract
    
    def save_contract(self):
        # تحديد نوع العقد
        contract_type = self.contract_type.currentIndex()
        
        # اختيار مسار الحفظ
        file_path, _ = QFileDialog.getSaveFileName(self, "حفظ العقد", "", "ملفات ماركداون (*.md);;ملفات نصية (*.txt)")
        
        if not file_path:
            return
        
        # توليد نص العقد
        if contract_type == 0:  # عقد تصميم
            contract_text = self.generate_design_contract()
        else:  # عقد تنفيذ
            contract_text = self.generate_implementation_contract()
        
        # حفظ العقد
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(contract_text)
        
        QMessageBox.information(self, "تم الحفظ", f"تم حفظ العقد بنجاح في:\n{file_path}")
    
    def print_contract(self):
        # تحديد نوع العقد
        contract_type = self.contract_type.currentIndex()
        
        # توليد نص العقد
        if contract_type == 0:  # عقد تصميم
            contract_text = self.generate_design_contract()
        else:  # عقد تنفيذ
            contract_text = self.generate_implementation_contract()
        
        # اختيار مسار حفظ ملف PDF
        file_path, _ = QFileDialog.getSaveFileName(self, "حفظ العقد كملف PDF", "", "ملفات PDF (*.pdf)")
        
        if not file_path:
            return
        
        # تحويل نص الماركداون إلى HTML
        html_content = markdown.markdown(contract_text)
        
        # إنشاء مستند نصي
        document = QTextDocument()
        document.setHtml(html_content)
        
        # إنشاء طابعة PDF
        printer = QPrinter(QPrinter.HighResolution)
        printer.setOutputFormat(QPrinter.PdfFormat)
        printer.setOutputFileName(file_path)
        printer.setPageSize(QPageSize(QPageSize.A4))
        
        # طباعة المستند إلى ملف PDF
        document.print_(printer)
        
        QMessageBox.information(self, "تم التصدير", f"تم تصدير العقد كملف PDF بنجاح في:\n{file_path}")
    
    def clear_form(self):
        # تأكيد المسح
        reply = QMessageBox.question(self, "تأكيد المسح", "هل أنت متأكد من رغبتك في مسح جميع البيانات؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.No:
            return
        
        # مسح بيانات المشروع
        self.project_name.clear()
        self.project_location.clear()
        self.project_area.setValue(0)
        
        # مسح بيانات المالك
        self.owner_name.clear()
        self.owner_address.clear()
        self.owner_id.clear()
        self.owner_phone.clear()
        self.owner_email.clear()
        
        # مسح بيانات المكتب الهندسي/المقاول
        self.contractor_name.clear()
        self.contractor_address.clear()
        self.contractor_license.clear()
        self.contractor_id.clear()
        self.contractor_phone.clear()
        self.contractor_email.clear()
        
        # إعادة تعيين التاريخ
        self.contract_date.setDate(QDate.currentDate())
        
        # مسح نص المعاينة
        self.preview_text.clear()
        
        # الانتقال إلى التبويب الأول
        self.tabs.setCurrentIndex(0)
    
    def get_arabic_month(self, month):
        arabic_months = {
            1: "يناير",
            2: "فبراير",
            3: "مارس",
            4: "أبريل",
            5: "مايو",
            6: "يونيو",
            7: "يوليو",
            8: "أغسطس",
            9: "سبتمبر",
            10: "أكتوبر",
            11: "نوفمبر",
            12: "ديسمبر"
        }
        return arabic_months.get(month, "")
    
    def number_to_arabic_words(self, number):
        # تبسيط: تحويل الرقم إلى كلمات عربية
        # هذه دالة مبسطة، يمكن استبدالها بمكتبة متخصصة لتحويل الأرقام إلى كلمات عربية
        return str(number)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ContractGeneratorApp()
    window.show()
    sys.exit(app.exec())
