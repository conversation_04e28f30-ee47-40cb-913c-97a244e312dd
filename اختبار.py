from PySide6.QtWidgets import (
    <PERSON>A<PERSON><PERSON>, QMainWindow, QTableWidget, QTableWidgetItem,
    QStyledItemDelegate, QStyleOptionProgressBar, QStyle, QVBoxLayout,
    QHBoxLayout, QWidget, QLabel, QPushButton, QFrame, QHeaderView,
    QScrollArea, QGridLayout, QProgressBar, QGroupBox, QSplitter
)
from PySide6.QtGui import (
    QColor, QPainter, QPalette, QFont, QLinearGradient,
    QBrush, QPen, QPixmap, QIcon, QFontMetrics
)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect, QSize
import sys
import math
from progress_config import (
    STATUS_COLORS, PRIORITY_COLORS, DAYS_LEFT_COLORS,
    PROGRESS_BAR_SETTINGS, TABLE_SETTINGS, WINDOW_SETTINGS,
    UI_TEXTS, TABLE_COLUMNS, get_color_by_days_left,
    get_status_color, get_priority_color, get_progress_text
)

class AdvancedProgressBarDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.animation_offset = 0

    def paint(self, painter, option, index):
        data = index.data(Qt.UserRole)
        if not data:
            return super().paint(painter, option, index)

        painter.save()
        painter.setRenderHint(QPainter.Antialiasing)

        progress = data.get("progress", 0)
        days_left = data.get("days_left", 0)
        status = data.get("status", "قيد الانجاز")
        priority = data.get("priority", "عادي")

        rect = option.rect
        margin = 4
        progress_rect = QRect(rect.x() + margin, rect.y() + margin,
                            rect.width() - 2*margin, rect.height() - 2*margin)

        # تحديد الألوان حسب الحالة والأولوية
        colors = self._get_status_colors(status, days_left, priority)

        # رسم الخلفية مع تدرج
        self._draw_background(painter, progress_rect, colors)

        # رسم شريط التقدم مع تأثيرات بصرية
        self._draw_progress_bar(painter, progress_rect, progress, colors)

        # رسم النص مع تنسيق متقدم
        self._draw_text(painter, progress_rect, progress, days_left, status, colors)

        # رسم مؤشرات إضافية
        self._draw_indicators(painter, progress_rect, data, colors)

        painter.restore()

    def _get_status_colors(self, status, days_left, priority):
        """تحديد الألوان حسب الحالة والأولوية"""
        colors = {}

        # ألوان حسب الحالة
        if status == "مكتمل":
            colors['primary'] = QColor("#27ae60")
            colors['secondary'] = QColor("#2ecc71")
        elif status == "متوقف":
            colors['primary'] = QColor("#e74c3c")
            colors['secondary'] = QColor("#c0392b")
        elif status == "معلق":
            colors['primary'] = QColor("#3498db")
            colors['secondary'] = QColor("#2980b9")
        else:  # قيد الانجاز
            if days_left <= 0:
                colors['primary'] = QColor("#e74c3c")
                colors['secondary'] = QColor("#c0392b")
            elif days_left <= 2:
                colors['primary'] = QColor("#f39c12")
                colors['secondary'] = QColor("#e67e22")
            elif days_left <= 5:
                colors['primary'] = QColor("#f1c40f")
                colors['secondary'] = QColor("#f39c12")
            else:
                colors['primary'] = QColor("#27ae60")
                colors['secondary'] = QColor("#2ecc71")

        # تعديل حسب الأولوية
        if priority == "عالي":
            colors['accent'] = QColor("#e74c3c")
        elif priority == "متوسط":
            colors['accent'] = QColor("#f39c12")
        else:
            colors['accent'] = QColor("#95a5a6")

        colors['background'] = QColor("#ecf0f1")
        colors['text'] = QColor("#2c3e50")
        colors['text_light'] = QColor("#7f8c8d")

        return colors

    def _draw_background(self, painter, rect, colors):
        """رسم خلفية متدرجة احترافية"""
        # خلفية رئيسية
        painter.setBrush(QBrush(colors['background']))
        painter.setPen(QPen(colors['text_light'], 1))
        painter.drawRoundedRect(rect, 8, 8)

        # تدرج داخلي
        gradient = QLinearGradient(rect.topLeft(), rect.bottomLeft())
        gradient.setColorAt(0, QColor(255, 255, 255, 50))
        gradient.setColorAt(1, QColor(0, 0, 0, 20))
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(rect.adjusted(1, 1, -1, -1), 7, 7)

    def _draw_progress_bar(self, painter, rect, progress, colors):
        """رسم شريط التقدم مع تأثيرات بصرية"""
        progress_width = int((rect.width() - 16) * progress / 100)
        progress_rect = QRect(rect.x() + 8, rect.y() + 8,
                            progress_width, rect.height() - 16)

        if progress_width > 0:
            # تدرج شريط التقدم
            gradient = QLinearGradient(progress_rect.topLeft(), progress_rect.bottomLeft())
            gradient.setColorAt(0, colors['secondary'])
            gradient.setColorAt(0.5, colors['primary'])
            gradient.setColorAt(1, colors['secondary'])

            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(progress_rect, 6, 6)

            # تأثير لمعان
            shine_rect = QRect(progress_rect.x(), progress_rect.y(),
                             progress_rect.width(), progress_rect.height() // 2)
            shine_gradient = QLinearGradient(shine_rect.topLeft(), shine_rect.bottomLeft())
            shine_gradient.setColorAt(0, QColor(255, 255, 255, 80))
            shine_gradient.setColorAt(1, QColor(255, 255, 255, 0))

            painter.setBrush(QBrush(shine_gradient))
            painter.drawRoundedRect(shine_rect, 6, 6)

    def _draw_text(self, painter, rect, progress, days_left, status, colors):
        """رسم النص مع تنسيق متقدم"""
        painter.setPen(colors['text'])

        # خط رئيسي
        main_font = QFont("Arial", 10, QFont.Bold)
        painter.setFont(main_font)

        # النص الرئيسي
        main_text = f"{progress}%"
        main_rect = QRect(rect.x(), rect.y(), rect.width() // 2, rect.height())
        painter.drawText(main_rect, Qt.AlignCenter, main_text)

        # خط فرعي
        sub_font = QFont("Arial", 8)
        painter.setFont(sub_font)
        painter.setPen(colors['text_light'])

        # النص الفرعي
        if days_left > 0:
            sub_text = f"باقي {days_left} يوم"
        elif days_left == 0:
            sub_text = "ينتهي اليوم"
        else:
            sub_text = f"متأخر {abs(days_left)} يوم"

        sub_rect = QRect(rect.x() + rect.width() // 2, rect.y(),
                        rect.width() // 2, rect.height())
        painter.drawText(sub_rect, Qt.AlignCenter, sub_text)

    def _draw_indicators(self, painter, rect, data, colors):
        """رسم مؤشرات إضافية"""
        priority = data.get("priority", "عادي")

        # مؤشر الأولوية
        if priority != "عادي":
            indicator_rect = QRect(rect.right() - 12, rect.y() + 4, 8, 8)
            painter.setBrush(QBrush(colors['accent']))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(indicator_rect)

    def sizeHint(self, option, index):
        return QSize(option.rect.width(), 50)

class AdvancedProgressWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام عرض الإنجاز المتقدم")
        self.setMinimumSize(1200, 800)

        # إعداد الواجهة الرئيسية
        self.setup_ui()
        self.setup_styles()
        self.load_sample_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # شريط العنوان والإحصائيات
        self.create_header_section(main_layout)

        # منطقة الجدول الرئيسية
        self.create_table_section(main_layout)

        # شريط الحالة السفلي
        self.create_status_section(main_layout)

    def create_header_section(self, parent_layout):
        """إنشاء قسم العنوان والإحصائيات"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QHBoxLayout(header_frame)

        # العنوان الرئيسي
        title_label = QLabel("📊 لوحة تحكم الإنجاز المتقدمة")
        title_label.setObjectName("titleLabel")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # إحصائيات سريعة
        stats_layout = QHBoxLayout()

        self.total_projects_label = QLabel("إجمالي المشاريع: 0")
        self.completed_projects_label = QLabel("مكتملة: 0")
        self.in_progress_label = QLabel("قيد الإنجاز: 0")
        self.overdue_label = QLabel("متأخرة: 0")

        for label in [self.total_projects_label, self.completed_projects_label,
                     self.in_progress_label, self.overdue_label]:
            label.setObjectName("statsLabel")
            stats_layout.addWidget(label)

        header_layout.addLayout(stats_layout)
        parent_layout.addWidget(header_frame)

    def create_table_section(self, parent_layout):
        """إنشاء قسم الجدول الرئيسي"""
        table_frame = QFrame()
        table_frame.setObjectName("tableFrame")
        table_layout = QVBoxLayout(table_frame)

        # عنوان القسم
        section_title = QLabel("🎯 تفاصيل المشاريع والإنجاز")
        section_title.setObjectName("sectionTitle")
        table_layout.addWidget(section_title)

        # الجدول
        self.table = QTableWidget()
        self.table.setObjectName("progressTable")
        self.setup_table()
        table_layout.addWidget(self.table)

        parent_layout.addWidget(table_frame)

    def setup_table(self):
        """إعداد الجدول"""
        # تحديد الأعمدة
        columns = ["اسم المشروع", "العميل", "الحالة", "الأولوية", "الإنجاز", "التاريخ المحدد", "الملاحظات"]
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setSortingEnabled(True)

        # إعداد حجم الأعمدة
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # اسم المشروع
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # العميل
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الأولوية
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # الإنجاز
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # التاريخ

        # إعداد ارتفاع الصفوف
        self.table.verticalHeader().setDefaultSectionSize(60)
        self.table.verticalHeader().setVisible(False)

        # تطبيق المفوض المخصص على عمود الإنجاز
        self.progress_delegate = AdvancedProgressBarDelegate()
        self.table.setItemDelegateForColumn(4, self.progress_delegate)

    def create_status_section(self, parent_layout):
        """إنشاء شريط الحالة السفلي"""
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_layout = QHBoxLayout(status_frame)

        # معلومات الحالة
        self.status_label = QLabel("جاهز")
        self.status_label.setObjectName("statusLabel")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        # أزرار التحكم
        refresh_btn = QPushButton("🔄 تحديث")
        export_btn = QPushButton("📤 تصدير")
        settings_btn = QPushButton("⚙️ إعدادات")

        # ربط الأزرار بالوظائف
        refresh_btn.clicked.connect(self.refresh_data)
        export_btn.clicked.connect(self.export_data)
        settings_btn.clicked.connect(self.show_settings)

        for btn in [refresh_btn, export_btn, settings_btn]:
            btn.setObjectName("controlButton")
            status_layout.addWidget(btn)

        parent_layout.addWidget(status_frame)

    def load_sample_data(self):
        """تحميل بيانات تجريبية"""
        sample_projects = [
            {
                "name": "تطوير موقع الشركة",
                "client": "شركة التقنية المتقدمة",
                "status": "قيد الانجاز",
                "priority": "عالي",
                "progress": 75,
                "days_left": 5,
                "deadline": "2024-02-15",
                "notes": "في المرحلة النهائية"
            },
            {
                "name": "تصميم هوية بصرية",
                "client": "مؤسسة الإبداع",
                "status": "قيد الانجاز",
                "priority": "متوسط",
                "progress": 45,
                "days_left": 12,
                "deadline": "2024-02-28",
                "notes": "تحتاج مراجعة العميل"
            },
            {
                "name": "تطبيق الجوال",
                "client": "شركة الحلول الذكية",
                "status": "قيد الانجاز",
                "priority": "عالي",
                "progress": 30,
                "days_left": -2,
                "deadline": "2024-01-30",
                "notes": "متأخر - يحتاج متابعة عاجلة"
            },
            {
                "name": "نظام إدارة المخزون",
                "client": "مجموعة التجارة الحديثة",
                "status": "مكتمل",
                "priority": "عادي",
                "progress": 100,
                "days_left": 0,
                "deadline": "2024-01-25",
                "notes": "تم التسليم بنجاح"
            },
            {
                "name": "منصة التعليم الإلكتروني",
                "client": "معهد التعليم المتطور",
                "status": "معلق",
                "priority": "متوسط",
                "progress": 60,
                "days_left": 20,
                "deadline": "2024-03-15",
                "notes": "في انتظار موافقة العميل"
            }
        ]

        self.table.setRowCount(len(sample_projects))

        for row, project in enumerate(sample_projects):
            # اسم المشروع
            self.table.setItem(row, 0, QTableWidgetItem(project["name"]))

            # العميل
            self.table.setItem(row, 1, QTableWidgetItem(project["client"]))

            # الحالة
            status_item = QTableWidgetItem(project["status"])
            self.apply_status_color(status_item, project["status"])
            self.table.setItem(row, 2, status_item)

            # الأولوية
            priority_item = QTableWidgetItem(project["priority"])
            self.apply_priority_color(priority_item, project["priority"])
            self.table.setItem(row, 3, priority_item)

            # الإنجاز (مع البيانات المخصصة)
            progress_item = QTableWidgetItem()
            progress_item.setData(Qt.UserRole, {
                "progress": project["progress"],
                "days_left": project["days_left"],
                "status": project["status"],
                "priority": project["priority"]
            })
            self.table.setItem(row, 4, progress_item)

            # التاريخ المحدد
            self.table.setItem(row, 5, QTableWidgetItem(project["deadline"]))

            # الملاحظات
            self.table.setItem(row, 6, QTableWidgetItem(project["notes"]))

        self.update_statistics()

    def apply_status_color(self, item, status):
        """تطبيق ألوان الحالة"""
        if status == "مكتمل":
            item.setForeground(QColor("#d5f4e6"))
            item.setForeground(QColor("#27ae60"))
        elif status == "متوقف":
            item.setForeground(QColor("#fadbd8"))
            item.setForeground(QColor("#e74c3c"))
        elif status == "معلق":
            item.setForeground(QColor("#d6eaf8"))
            item.setForeground(QColor("#3498db"))
        else:  # قيد الانجاز
            item.setForeground(QColor("#fff3cd"))
            item.setForeground(QColor("#856404"))

    def apply_priority_color(self, item, priority):
        """تطبيق ألوان الأولوية"""
        if priority == "عالي":
            item.setForeground(QColor("#fadbd8"))
            item.setForeground(QColor("#e74c3c"))
        elif priority == "متوسط":
            item.setForeground(QColor("#fdeaa7"))
            item.setForeground(QColor("#f39c12"))
        else:  # عادي
            item.setForeground(QColor("#e8f5e8"))
            item.setForeground(QColor("#27ae60"))

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total = self.table.rowCount()
        completed = 0
        in_progress = 0
        overdue = 0

        for row in range(total):
            status_item = self.table.item(row, 2)
            progress_item = self.table.item(row, 4)

            if status_item:
                status = status_item.text()
                if status == "مكتمل":
                    completed += 1
                elif status == "قيد الانجاز":
                    in_progress += 1

            if progress_item:
                data = progress_item.data(Qt.UserRole)
                if data and data.get("days_left", 0) < 0:
                    overdue += 1

        self.total_projects_label.setText(f"إجمالي المشاريع: {total}")
        self.completed_projects_label.setText(f"مكتملة: {completed}")
        self.in_progress_label.setText(f"قيد الإنجاز: {in_progress}")
        self.overdue_label.setText(f"متأخرة: {overdue}")

    def setup_styles(self):
        """إعداد الأنماط المتقدمة من ملف خارجي"""
        try:
            with open('advanced_progress_styles.qss', 'r', encoding='utf-8') as file:
                style_sheet = file.read()
                self.setStyleSheet(style_sheet)
        except FileNotFoundError:
            # في حالة عدم وجود الملف، استخدم أنماط أساسية
            basic_style = """
            QMainWindow {
                background: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                gridline-color: #dee2e6;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            """
            self.setStyleSheet(basic_style)

    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText("جاري التحديث...")
        self.status_label.setStyleSheet("color: #f39c12;")

        # محاكاة تحديث البيانات
        QTimer.singleShot(1000, self._finish_refresh)

    def _finish_refresh(self):
        """إنهاء عملية التحديث"""
        self.load_sample_data()
        self.status_label.setText("تم التحديث بنجاح")
        self.status_label.setStyleSheet("color: #27ae60;")

        # العودة للحالة العادية بعد 3 ثوان
        QTimer.singleShot(3000, lambda: self.status_label.setText("جاهز"))

    def export_data(self):
        """تصدير البيانات"""
        self.status_label.setText("جاري التصدير...")
        self.status_label.setStyleSheet("color: #3498db;")

        # محاكاة عملية التصدير
        QTimer.singleShot(2000, self._finish_export)

    def _finish_export(self):
        """إنهاء عملية التصدير"""
        self.status_label.setText("تم التصدير بنجاح")
        self.status_label.setStyleSheet("color: #27ae60;")

        # العودة للحالة العادية بعد 3 ثوان
        QTimer.singleShot(3000, lambda: self.status_label.setText("جاهز"))

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        self.status_label.setText("فتح الإعدادات...")
        self.status_label.setStyleSheet("color: #9b59b6;")

        # العودة للحالة العادية بعد ثانية واحدة
        QTimer.singleShot(1000, lambda: self.status_label.setText("جاهز"))

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # تطبيق خط عربي مناسب
    app.setStyle('Fusion')

    window = AdvancedProgressWindow()
    window.show()
    sys.exit(app.exec())
