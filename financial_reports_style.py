"""
ستايل التقارير المالية - منظومة المهندس
"""

FINANCIAL_REPORTS_STYLESHEET = """
/* النافذة الرئيسية */
QDialog {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
}

/* شريط الأدوات العلوي */
QFrame#ToolbarFrame {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                               stop: 0 #667eea, stop: 1 #764ba2);
    border-radius: 8px;
    margin: 5px;
}

QLabel#TitleLabel {
    color: white;
    font-size: 24px;
    font-weight: bold;
    padding: 10px;
}

QPushButton#ToolbarButton {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 12px;
}

QPushButton#ToolbarButton:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
}

QPushButton#ToolbarButton:pressed {
    background-color: rgba(255, 255, 255, 0.1);
}

/* لوحة الفلاتر */
QFrame#FiltersFrame {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    margin: 5px;
}

QGroupBox {
    font-weight: bold;
    font-size: 14px;
    color: #2c3e50;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    background-color: white;
}

QComboBox {
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px;
    font-size: 12px;
    background-color: white;
    min-height: 20px;
}

QComboBox:hover {
    border: 2px solid #3498db;
}

QComboBox:focus {
    border: 2px solid #2980b9;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(icons/down-arrow.png);
    width: 12px;
    height: 12px;
}

QDateEdit {
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px;
    font-size: 12px;
    background-color: white;
    min-height: 20px;
}

QDateEdit:hover {
    border: 2px solid #3498db;
}

QDateEdit:focus {
    border: 2px solid #2980b9;
}

QPushButton#ApplyButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #27ae60, stop: 1 #2ecc71);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 12px;
    font-weight: bold;
    font-size: 14px;
}

QPushButton#ApplyButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #2ecc71, stop: 1 #27ae60);
}

QPushButton#ApplyButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #229954, stop: 1 #27ae60);
}

/* اللوحة الرئيسية */
QFrame#MainFrame {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    margin: 5px;
}

/* التبويبات */
QTabWidget#ReportsTabWidget {
    border: none;
}

QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: white;
}

QTabBar::tab {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #ecf0f1, stop: 1 #bdc3c7);
    border: 1px solid #bdc3c7;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: bold;
    font-size: 12px;
    color: #2c3e50;
}

QTabBar::tab:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #3498db, stop: 1 #2980b9);
    color: white;
    border-bottom: none;
}

QTabBar::tab:hover:!selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #d5dbdb, stop: 1 #bdc3c7);
}

/* الجداول */
QTableWidget#SummaryTable, QTableWidget#DetailedTable {
    gridline-color: #e0e0e0;
    background-color: white;
    alternate-background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 12px;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #e0e0e0;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QHeaderView::section {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #34495e, stop: 1 #2c3e50);
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
    font-size: 12px;
}

QHeaderView::section:horizontal {
    border-right: 1px solid #2c3e50;
}

QHeaderView::section:vertical {
    border-bottom: 1px solid #2c3e50;
}

/* شريط الحالة */
QFrame#StatusFrame {
    background-color: #34495e;
    border-radius: 5px;
    margin: 5px;
}

QLabel#StatusLabel, QLabel#UpdateLabel {
    color: white;
    font-size: 11px;
    padding: 5px;
}

/* بطاقات الإحصائيات */
QFrame#StatCard {
    background-color: white;
    border-radius: 12px;
    margin: 8px;
    padding: 10px;
}

QFrame#StatCard:hover {
    background-color: #f8f9fa;
    border: 2px solid #3498db;
}

/* أزرار عامة */
QPushButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #3498db, stop: 1 #2980b9);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 12px;
}

QPushButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #2980b9, stop: 1 #3498db);
}

QPushButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #21618c, stop: 1 #2980b9);
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* تحسينات إضافية */
QScrollBar:vertical {
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #ecf0f1;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}
"""

def apply_financial_reports_style(widget):
    """تطبيق ستايل التقارير المالية على الويدجت"""
    widget.setStyleSheet(FINANCIAL_REPORTS_STYLESHEET)
