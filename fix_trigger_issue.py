#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشكلة الـ trigger في جدول المشاريع_المدفوعات
"""

import mysql.connector
from for_all import host, user_r, password_r

def fix_trigger_issue():
    """إصلاح مشكلة الـ trigger"""
    print("🔧 إصلاح مشكلة الـ trigger...")
    
    try:
        # الاتصال كـ root للحصول على صلاحيات تعديل الـ triggers
        conn = mysql.connector.connect(
            host=host, 
            user=user_r, 
            password=password_r,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال كـ root")
        
        # عرض الـ trigger الحالي
        print("\n📋 عرض الـ trigger الحالي:")
        cursor.execute("SHOW CREATE TRIGGER update_project_paid_insert")
        trigger_def = cursor.fetchone()
        if trigger_def:
            print("Trigger Definition:")
            print(trigger_def[2])  # SQL Statement
        
        # حذف الـ triggers المشكلة وإعادة إنشاؤها بشكل صحيح
        print("\n🗑️ حذف الـ triggers المشكلة...")
        
        triggers_to_fix = [
            'update_project_paid_insert',
            'update_project_paid_update', 
            'update_project_paid_delete'
        ]
        
        for trigger_name in triggers_to_fix:
            try:
                cursor.execute(f"DROP TRIGGER IF EXISTS {trigger_name}")
                print(f"✅ تم حذف {trigger_name}")
            except mysql.connector.Error as e:
                print(f"⚠️ خطأ في حذف {trigger_name}: {e}")
        
        # إنشاء triggers جديدة صحيحة
        print("\n🔨 إنشاء triggers جديدة...")
        
        # Trigger للـ INSERT
        insert_trigger = """
        CREATE TRIGGER update_project_paid_insert
        AFTER INSERT ON المشاريع_المدفوعات
        FOR EACH ROW
        BEGIN
            UPDATE المشاريع
            SET المدفوع = (
                SELECT COALESCE(SUM(المبلغ_المدفوع), 0)
                FROM المشاريع_المدفوعات
                WHERE معرف_المشروع = NEW.معرف_المشروع
            )
            WHERE id = NEW.معرف_المشروع;
        END
        """
        
        # Trigger للـ UPDATE
        update_trigger = """
        CREATE TRIGGER update_project_paid_update
        AFTER UPDATE ON المشاريع_المدفوعات
        FOR EACH ROW
        BEGIN
            UPDATE المشاريع
            SET المدفوع = (
                SELECT COALESCE(SUM(المبلغ_المدفوع), 0)
                FROM المشاريع_المدفوعات
                WHERE معرف_المشروع = NEW.معرف_المشروع
            )
            WHERE id = NEW.معرف_المشروع;

            -- إذا تم تغيير معرف_المشروع، تحديث المشروع القديم أيضاً
            IF OLD.معرف_المشروع != NEW.معرف_المشروع THEN
                UPDATE المشاريع
                SET المدفوع = (
                    SELECT COALESCE(SUM(المبلغ_المدفوع), 0)
                    FROM المشاريع_المدفوعات
                    WHERE معرف_المشروع = OLD.معرف_المشروع
                )
                WHERE id = OLD.معرف_المشروع;
            END IF;
        END
        """
        
        # Trigger للـ DELETE
        delete_trigger = """
        CREATE TRIGGER update_project_paid_delete
        AFTER DELETE ON المشاريع_المدفوعات
        FOR EACH ROW
        BEGIN
            UPDATE المشاريع
            SET المدفوع = (
                SELECT COALESCE(SUM(المبلغ_المدفوع), 0)
                FROM المشاريع_المدفوعات
                WHERE معرف_المشروع = OLD.معرف_المشروع
            )
            WHERE id = OLD.معرف_المشروع;
        END
        """
        
        # تنفيذ الـ triggers
        try:
            cursor.execute(insert_trigger)
            print("✅ تم إنشاء update_project_paid_insert")
        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء insert trigger: {e}")
        
        try:
            cursor.execute(update_trigger)
            print("✅ تم إنشاء update_project_paid_update")
        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء update trigger: {e}")
        
        try:
            cursor.execute(delete_trigger)
            print("✅ تم إنشاء update_project_paid_delete")
        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء delete trigger: {e}")
        
        conn.commit()
        conn.close()
        print("✅ تم حفظ التغييرات وإغلاق الاتصال")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("إصلاح مشكلة الـ Triggers في جدول المشاريع_المدفوعات")
    print("=" * 60)
    
    success = fix_trigger_issue()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ تم إصلاح الـ triggers بنجاح")
    else:
        print("❌ فشل في إصلاح الـ triggers")
    print("=" * 60)
