from for_all import*

class AccountTransactionsReportDialog(QDialog):
    """نافذة تقارير معاملات الحساب"""
    
    def __init__(self, main_window, employee_data=None):
        super().__init__(main_window)
        self.main_window = main_window
        self.employee_data = employee_data
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تقارير معاملات الحساب")
        self.setModal(True)
        self.resize(1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("تقارير معاملات الحساب")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # قسم الفلاتر
        filters_group = QGroupBox("فلاتر البحث")
        filters_layout = QGridLayout(filters_group)
        
        # فلتر الموظف
        filters_layout.addWidget(QLabel("الموظف:"), 0, 0)
        self.employee_filter = QComboBox()
        self.employee_filter.setMinimumWidth(200)
        filters_layout.addWidget(self.employee_filter, 0, 1)
        
        # فلتر نوع العملية
        filters_layout.addWidget(QLabel("نوع العملية:"), 0, 2)
        self.operation_filter = QComboBox()
        self.operation_filter.addItems(["الكل", "إيداع", "سحب"])
        filters_layout.addWidget(self.operation_filter, 0, 3)
        
        # فلتر نوع المعاملة
        filters_layout.addWidget(QLabel("نوع المعاملة:"), 1, 0)
        self.transaction_filter = QComboBox()
        self.transaction_filter.addItem("الكل")
        filters_layout.addWidget(self.transaction_filter, 1, 1)
        
        # فلتر التاريخ من
        filters_layout.addWidget(QLabel("من تاريخ:"), 1, 2)
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_from.setCalendarPopup(True)
        filters_layout.addWidget(self.date_from, 1, 3)
        
        # فلتر التاريخ إلى
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 2, 0)
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        filters_layout.addWidget(self.date_to, 2, 1)
        
        # زر البحث
        search_btn = QPushButton("بحث")
        search_btn.clicked.connect(self.load_data)
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        filters_layout.addWidget(search_btn, 2, 2)
        
        # زر إعادة تعيين
        reset_btn = QPushButton("إعادة تعيين")
        reset_btn.clicked.connect(self.reset_filters)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        filters_layout.addWidget(reset_btn, 2, 3)
        
        main_layout.addWidget(filters_group)
        
        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setSortingEnabled(True)
        
        # تعيين أعمدة الجدول
        columns = [
            "الid", "اسم الموظف", "نوع العملية", "نوع المعاملة", 
            "المبلغ", "التاريخ", "الرصيد قبل", "الرصيد بعد", 
            "تلقائية", "الوصف"
        ]
        self.transactions_table.setColumnCount(len(columns))
        self.transactions_table.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        main_layout.addWidget(self.transactions_table)
        
        # قسم الإحصائيات
        stats_group = QGroupBox("إحصائيات")
        stats_layout = QHBoxLayout(stats_group)
        
        self.total_transactions_label = QLabel("إجمالي المعاملات: 0")
        self.total_deposits_label = QLabel("إجمالي الإيداعات: 0.00")
        self.total_withdrawals_label = QLabel("إجمالي السحوبات: 0.00")
        self.net_amount_label = QLabel("صافي المبلغ: 0.00")
        
        stats_layout.addWidget(self.total_transactions_label)
        stats_layout.addWidget(self.total_deposits_label)
        stats_layout.addWidget(self.total_withdrawals_label)
        stats_layout.addWidget(self.net_amount_label)
        stats_layout.addStretch()
        
        main_layout.addWidget(stats_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        # زر طباعة
        print_btn = QPushButton("طباعة التقرير")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        
        # زر تصدير
        export_btn = QPushButton("تصدير إلى Excel")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        export_btn.clicked.connect(self.export_to_excel)
        
        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_btn)
        buttons_layout.addWidget(close_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # تحميل البيانات الأولية
        self.load_employees()
        self.load_transaction_types()
    
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            conn = self.main_window.get_db_connection(str(QDate.currentDate().year()))
            if not conn:
                return
            
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT id, اسم_الموظف FROM الموظفين ORDER BY اسم_الموظف")
            employees = cursor.fetchall()
            
            self.employee_filter.clear()
            self.employee_filter.addItem("جميع الموظفين", None)
            
            for emp in employees:
                self.employee_filter.addItem(emp['اسم_الموظف'], emp['id'])
            
            # إذا تم تمرير بيانات موظف محدد، قم بتحديده
            if self.employee_data:
                for i in range(self.employee_filter.count()):
                    if self.employee_filter.itemData(i) == self.employee_data.get('id'):
                        self.employee_filter.setCurrentIndex(i)
                        break
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل قائمة الموظفين: {str(e)}")
    
    def load_transaction_types(self):
        """تحميل أنواع المعاملات"""
        try:
            conn = self.main_window.get_db_connection(str(QDate.currentDate().year()))
            if not conn:
                return
            
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT نوع_المعاملة FROM معاملات_الحساب ORDER BY نوع_المعاملة")
            types = cursor.fetchall()
            
            self.transaction_filter.clear()
            self.transaction_filter.addItem("الكل")
            
            for type_row in types:
                self.transaction_filter.addItem(type_row[0])
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل أنواع المعاملات: {str(e)}")
    
    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.employee_filter.setCurrentIndex(0)
        self.operation_filter.setCurrentIndex(0)
        self.transaction_filter.setCurrentIndex(0)
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_to.setDate(QDate.currentDate())
        self.load_data()

    def load_data(self):
        """تحميل بيانات المعاملات"""
        try:
            conn = self.main_window.get_db_connection(str(QDate.currentDate().year()))
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # بناء الاستعلام مع الفلاتر
            query = """
                SELECT id, اسم_الموظف, نوع_العملية, نوع_المعاملة, المبلغ, التاريخ,
                       الرصيد_قبل_المعاملة, الرصيد_بعد_المعاملة, معاملة_تلقائية, الوصف
                FROM معاملات_الحساب
                WHERE 1=1
            """
            params = []

            # فلتر الموظف
            employee_id = self.employee_filter.currentData()
            if employee_id:
                query += " AND معرف_الموظف = %s"
                params.append(employee_id)

            # فلتر نوع العملية
            operation_type = self.operation_filter.currentText()
            if operation_type != "الكل":
                query += " AND نوع_العملية = %s"
                params.append(operation_type)

            # فلتر نوع المعاملة
            transaction_type = self.transaction_filter.currentText()
            if transaction_type != "الكل":
                query += " AND نوع_المعاملة = %s"
                params.append(transaction_type)

            # فلتر التاريخ
            date_from = self.date_from.date().toString(Qt.ISODate)
            date_to = self.date_to.date().toString(Qt.ISODate)
            query += " AND التاريخ BETWEEN %s AND %s"
            params.extend([date_from, date_to])

            # ترتيب النتائج
            query += " ORDER BY التاريخ DESC, id DESC"

            cursor.execute(query, params)
            transactions = cursor.fetchall()

            # تحديث الجدول
            self.update_table(transactions)

            # تحديث الإحصائيات
            self.update_statistics(transactions)

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def update_table(self, transactions):
        """تحديث جدول المعاملات"""
        self.transactions_table.setRowCount(len(transactions))

        for row, transaction in enumerate(transactions):
            # الid
            self.transactions_table.setItem(row, 0, QTableWidgetItem(str(transaction['id'])))

            # اسم الموظف
            self.transactions_table.setItem(row, 1, QTableWidgetItem(transaction['اسم_الموظف'] or ''))

            # نوع العملية
            operation_item = QTableWidgetItem(transaction['نوع_العملية'] or '')
            if transaction['نوع_العملية'] == 'إيداع':
                operation_item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
            else:
                operation_item.setForeground(QColor("#ffeaa7"))  # أصفر فاتح
            self.transactions_table.setItem(row, 2, operation_item)

            # نوع المعاملة
            self.transactions_table.setItem(row, 3, QTableWidgetItem(transaction['نوع_المعاملة'] or ''))

            # المبلغ
            amount_item = QTableWidgetItem(f"{transaction['المبلغ']:,.2f}")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row, 4, amount_item)

            # التاريخ
            date_item = QTableWidgetItem(str(transaction['التاريخ']))
            date_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row, 5, date_item)

            # الرصيد قبل المعاملة
            before_item = QTableWidgetItem(f"{transaction['الرصيد_قبل_المعاملة']:,.2f}")
            before_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row, 6, before_item)

            # الرصيد بعد المعاملة
            after_item = QTableWidgetItem(f"{transaction['الرصيد_بعد_المعاملة']:,.2f}")
            after_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row, 7, after_item)

            # معاملة تلقائية
            auto_text = "نعم" if transaction['معاملة_تلقائية'] else "لا"
            auto_item = QTableWidgetItem(auto_text)
            auto_item.setTextAlignment(Qt.AlignCenter)
            if transaction['معاملة_تلقائية']:
                auto_item.setForeground(QColor("#74b9ff"))  # أزرق فاتح
            self.transactions_table.setItem(row, 8, auto_item)

            # الوصف
            self.transactions_table.setItem(row, 9, QTableWidgetItem(transaction['الوصف'] or ''))

    def update_statistics(self, transactions):
        """تحديث الإحصائيات"""
        total_count = len(transactions)
        total_deposits = sum(t['المبلغ'] for t in transactions if t['نوع_العملية'] == 'إيداع')
        total_withdrawals = sum(t['المبلغ'] for t in transactions if t['نوع_العملية'] == 'سحب')
        net_amount = total_deposits - total_withdrawals

        self.total_transactions_label.setText(f"إجمالي المعاملات: {total_count}")
        self.total_deposits_label.setText(f"إجمالي الإيداعات: {total_deposits:,.2f}")
        self.total_withdrawals_label.setText(f"إجمالي السحوبات: {total_withdrawals:,.2f}")
        self.net_amount_label.setText(f"صافي المبلغ: {net_amount:,.2f}")

        # تلوين صافي المبلغ
        if net_amount > 0:
            self.net_amount_label.setStyleSheet("color: green; font-weight: bold;")
        elif net_amount < 0:
            self.net_amount_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.net_amount_label.setStyleSheet("color: black; font-weight: bold;")

    def print_report(self):
        """طباعة التقرير"""
        try:
            from الطباعة import print_table_data

            # إعداد بيانات الطباعة
            employee_name = self.employee_filter.currentText()
            date_range = f"من {self.date_from.date().toString('yyyy-MM-dd')} إلى {self.date_to.date().toString('yyyy-MM-dd')}"

            title = f"تقرير معاملات الحساب - {employee_name}\n{date_range}"

            print_table_data(
                self,
                table=self.transactions_table,
                title=title,
                file_prefix="تقرير_معاملات_الحساب"
            )

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في طباعة التقرير: {str(e)}")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd
            from datetime import datetime

            # جمع البيانات من الجدول
            data = []
            for row in range(self.transactions_table.rowCount()):
                row_data = []
                for col in range(self.transactions_table.columnCount()):
                    item = self.transactions_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # إنشاء DataFrame
            columns = [
                "الid", "اسم الموظف", "نوع العملية", "نوع المعاملة",
                "المبلغ", "التاريخ", "الرصيد قبل", "الرصيد بعد",
                "تلقائية", "الوصف"
            ]
            df = pd.DataFrame(data, columns=columns)

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير",
                f"تقرير_معاملات_الحساب_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                # حفظ الملف
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='معاملات الحساب', index=False)

                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except ImportError:
            QMessageBox.warning(self, "خطأ", "مكتبة pandas غير مثبتة. يرجى تثبيتها لاستخدام ميزة التصدير.")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")


def open_account_transactions_report(main_window, employee_data=None):
    """فتح نافذة تقارير معاملات الحساب"""
    dialog = AccountTransactionsReportDialog(main_window, employee_data)
    return dialog.exec()
