import sys
import sqlite3
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QLabel, QLineEdit,
    QComboBox, QDateEdit, QTextEdit, QMessageBox, QHeaderView,
    QFrame, QGridLayout, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QPalette, QColor

class ProjectExpensesWindow(QMainWindow):
    def __init__(self, parent=None, project_id=None, project_name=None, client_name=None):
        super().__init__(parent)
        self.project_id = project_id
        self.project_name = project_name
        self.client_name = client_name
        self.init_ui()
        self.load_expense_data()
        self.update_statistics()
        
    def init_ui(self):
        title = "مصروفات المشروع"
        if self.project_name:
            title += f" - {self.project_name}"
        if self.client_name:
            title += f" - {self.client_name}"
        self.setWindowTitle(title)
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # قسم الإحصائيات
        self.create_statistics_section(main_layout)
        
        # قسم إدخال المصروفات
        self.create_input_section(main_layout)
        
        # قسم عرض المصروفات
        self.create_table_section(main_layout)
        
        # قسم الأزرار
        self.create_buttons_section(main_layout)
        
    def create_statistics_section(self, parent_layout):
        """إنشاء قسم الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Shape.Box)
        stats_frame.setStyleSheet("QFrame { border: 2px solid #cccccc; border-radius: 5px; padding: 10px; }")
        
        stats_layout = QGridLayout(stats_frame)
        
        # تسميات الإحصائيات
        self.total_expenses_label = QLabel("إجمالي المصروفات: 0.00")
        self.independent_expenses_label = QLabel("المصروفات المباشرة: 0.00")
        self.custody_expenses_label = QLabel("مصروفات العهد: 0.00")
        self.expenses_count_label = QLabel("عدد المصروفات: 0")
        
        # تنسيق التسميات
        for label in [self.total_expenses_label, self.independent_expenses_label, 
                     self.custody_expenses_label, self.expenses_count_label]:
            label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            label.setStyleSheet("QLabel { color: #2c3e50; padding: 5px; }")
        
        stats_layout.addWidget(self.total_expenses_label, 0, 0)
        stats_layout.addWidget(self.independent_expenses_label, 0, 1)
        stats_layout.addWidget(self.custody_expenses_label, 1, 0)
        stats_layout.addWidget(self.expenses_count_label, 1, 1)
        
        parent_layout.addWidget(stats_frame)
        
    def create_input_section(self, parent_layout):
        """إنشاء قسم إدخال المصروفات"""
        input_frame = QFrame()
        input_frame.setFrameStyle(QFrame.Shape.Box)
        input_frame.setStyleSheet("QFrame { border: 2px solid #cccccc; border-radius: 5px; padding: 10px; }")
        
        input_layout = QGridLayout(input_frame)
        
        # حقول الإدخال
        input_layout.addWidget(QLabel("نوع المصروف:"), 0, 0)
        self.expense_type_combo = QComboBox()
        self.expense_type_combo.addItems(["مصروف مباشر", "مصروف عهدة"])
        input_layout.addWidget(self.expense_type_combo, 0, 1)
        
        input_layout.addWidget(QLabel("المبلغ:"), 0, 2)
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("أدخل المبلغ")
        input_layout.addWidget(self.amount_input, 0, 3)
        
        input_layout.addWidget(QLabel("التاريخ:"), 1, 0)
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        input_layout.addWidget(self.date_input, 1, 1)
        
        input_layout.addWidget(QLabel("الوصف:"), 1, 2)
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("وصف المصروف")
        input_layout.addWidget(self.description_input, 1, 3)
        
        # زر الإضافة
        self.add_expense_btn = QPushButton("إضافة مصروف")
        self.add_expense_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        self.add_expense_btn.clicked.connect(self.add_expense)
        input_layout.addWidget(self.add_expense_btn, 2, 0, 1, 4)
        
        parent_layout.addWidget(input_frame)
        
    def create_table_section(self, parent_layout):
        """إنشاء قسم جدول المصروفات"""
        # تسمية الجدول
        table_label = QLabel("قائمة المصروفات")
        table_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_label.setStyleSheet("QLabel { color: #2c3e50; margin: 10px 0; }")
        parent_layout.addWidget(table_label)
        
        # الجدول
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(6)
        self.expenses_table.setHorizontalHeaderLabels([
            "الid", "النوع", "المبلغ", "التاريخ", "الوصف", "الإجراءات"
        ])
        
        # تنسيق الجدول
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #cccccc;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        parent_layout.addWidget(self.expenses_table)
        
    def create_buttons_section(self, parent_layout):
        """إنشاء قسم الأزرار"""
        buttons_layout = QHBoxLayout()
        
        # أزرار العمليات
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.report_btn = QPushButton("تقرير")
        self.export_btn = QPushButton("تصدير Excel")
        
        # تنسيق الأزرار
        buttons = [self.edit_btn, self.delete_btn, self.report_btn, self.export_btn]
        colors = ["#3498db", "#e74c3c", "#f39c12", "#9b59b6"]
        
        for btn, color in zip(buttons, colors):
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 5px;
                }}
                QPushButton:hover {{
                    opacity: 0.8;
                }}
            """)
            buttons_layout.addWidget(btn)
        
        # ربط الأزرار بالوظائف
        self.edit_btn.clicked.connect(self.edit_expense)
        self.delete_btn.clicked.connect(self.delete_expense)
        self.report_btn.clicked.connect(self.generate_report)
        self.export_btn.clicked.connect(self.export_to_excel)
        
        # إضافة مساحة فارغة
        buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        
        parent_layout.addLayout(buttons_layout)
        
    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            expense_type = self.expense_type_combo.currentText()
            amount = float(self.amount_input.text())
            date = self.date_input.date().toString("yyyy-MM-dd")
            description = self.description_input.text()
            
            if amount <= 0:
                QMessageBox.warning(self, "خطأ", "يجب أن يكون المبلغ أكبر من صفر")
                return
                
            if not description.strip():
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف للمصروف")
                return
            
            conn = sqlite3.connect('project_manager.db')
            cursor = conn.cursor()
            
            if expense_type == "مصروف مباشر":
                cursor.execute("""
                    INSERT INTO مصروفات_مباشرة_المشروع (project_id, amount, expense_date, description)
                    VALUES (?, ?, ?, ?)
                """, (self.project_id, amount, date, description))
            else:
                cursor.execute("""
                    INSERT INTO مصروفات_عهدة_المشروع (project_id, amount, expense_date, description)
                    VALUES (?, ?, ?, ?)
                """, (self.project_id, amount, date, description))
            
            conn.commit()
            conn.close()
            
            # مسح الحقول
            self.amount_input.clear()
            self.description_input.clear()
            self.date_input.setDate(QDate.currentDate())
            
            # تحديث البيانات
            self.load_expense_data()
            self.update_statistics()
            
            QMessageBox.information(self, "نجح", "تم إضافة المصروف بنجاح")
            
        except ValueError:
            QMessageBox.warning(self, "خطأ", "يجب إدخال مبلغ صحيح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
            
    def load_expense_data(self):
        """تحميل بيانات المصروفات"""
        QMessageBox.information(self, "تحميل البيانات", "سيتم تطوير وظيفة تحميل البيانات قريباً")
        
    def edit_expense(self):
        """تعديل مصروف"""
        QMessageBox.information(self, "تعديل", "سيتم تطوير وظيفة التعديل قريباً")
        
    def delete_expense(self):
        """حذف مصروف"""
        QMessageBox.information(self, "حذف", "سيتم تطوير وظيفة الحذف قريباً")
        
    def generate_report(self):
        """إنشاء تقرير"""
        QMessageBox.information(self, "تقرير", "سيتم تطوير وظيفة التقارير قريباً")
        
    def export_to_excel(self):
        """تصدير إلى Excel"""
        QMessageBox.information(self, "تصدير", "سيتم تطوير وظيفة التصدير قريباً")
        
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            conn = sqlite3.connect('project_manager.db')
            cursor = conn.cursor()
            
            # إجمالي المصروفات المباشرة
            cursor.execute("""
                SELECT COALESCE(SUM(amount), 0) FROM مصروفات_مباشرة_المشروع 
                WHERE project_id = ?
            """, (self.project_id,))
            independent_total = cursor.fetchone()[0]
            
            # إجمالي مصروفات العهدة
            cursor.execute("""
                SELECT COALESCE(SUM(amount), 0) FROM مصروفات_عهدة_المشروع 
                WHERE project_id = ?
            """, (self.project_id,))
            custody_total = cursor.fetchone()[0]
            
            # عدد المصروفات
            cursor.execute("""
                SELECT COUNT(*) FROM (
                    SELECT id FROM مصروفات_مباشرة_المشروع WHERE project_id = ?
                    UNION ALL
                    SELECT id FROM مصروفات_عهدة_المشروع WHERE project_id = ?
                )
            """, (self.project_id, self.project_id))
            expenses_count = cursor.fetchone()[0]
            
            total_expenses = independent_total + custody_total
            
            # تحديث التسميات
            self.total_expenses_label.setText(f"إجمالي المصروفات: {total_expenses:.2f}")
            self.independent_expenses_label.setText(f"المصروفات المباشرة: {independent_total:.2f}")
            self.custody_expenses_label.setText(f"مصروفات العهد: {custody_total:.2f}")
            self.expenses_count_label.setText(f"عدد المصروفات: {expenses_count}")
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحديث الإحصائيات: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Test with a sample project id
    window = ProjectExpensesWindow(project_id=1)
    window.show()
    
    sys.exit(app.exec())
