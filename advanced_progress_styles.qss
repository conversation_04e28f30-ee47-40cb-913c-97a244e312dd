/* ========================================
   نظام عرض الإنجاز المتقدم - ملف الأنماط
   ======================================== */

/* الأنماط العامة */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
}

/* شريط العنوان */
#headerFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f1f3f4);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 15px;
    margin: 5px;

}

#titleLabel {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px;

}

#statsLabel {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3498db, stop:1 #2980b9);
    color: white;
    font-weight: bold;
    font-size: 12px;
    padding: 8px 15px;
    border-radius: 20px;
    margin: 2px;
    border: 2px solid #2980b9;
    min-width: 120px;
}

#statsLabel:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5dade2, stop:1 #3498db);
}

/* إطار الجدول */
#tableFrame {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    margin: 5px;

}

#sectionTitle {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px 0px;
    border-bottom: 3px solid #3498db;
    margin-bottom: 15px;

}

/* الجدول */
#progressTable {
    background-color: white;
    alternate-background-color: #f8f9fa;
    selection-background-color: #e3f2fd;
    gridline-color: #dee2e6;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 11px;
    outline: none;
}

#progressTable::item {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
    border-right: 1px solid #e9ecef;
}

#progressTable::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #1976d2;
    border: 2px solid #2196f3;
}

#progressTable::item:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f5f5f5, stop:1 #eeeeee);
}

/* رأس الجدول */
#progressTable QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3498db, stop:1 #2980b9);
    color: white;
    font-weight: bold;
    font-size: 12px;
    padding: 12px 8px;
    border: none;
    border-right: 1px solid #2980b9;
    text-align: center;
}

#progressTable QHeaderView::section:first {
    border-top-left-radius: 8px;
}

#progressTable QHeaderView::section:last {
    border-top-right-radius: 8px;
    border-right: none;
}

#progressTable QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5dade2, stop:1 #3498db);
}

/* شريط الحالة */
#statusFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f1f3f4);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 10px;
    margin: 5px;

}

#statusLabel {
    font-size: 14px;
    color: #27ae60;
    font-weight: bold;
    padding: 5px 10px;
    background: rgba(39, 174, 96, 0.1);
    border-radius: 15px;
}

/* أزرار التحكم */
#controlButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3498db, stop:1 #2980b9);
    color: white;
    font-weight: bold;
    font-size: 12px;
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    margin: 2px 5px;
    min-width: 100px;
}

#controlButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5dade2, stop:1 #3498db);
    border: 2px solid #2980b9;
}

#controlButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2980b9, stop:1 #1f618d);
    border: 2px solid #1f618d;
}

/* شريط التمرير */
QScrollBar:vertical {
    background: #f1f3f4;
    width: 12px;
    border-radius: 6px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #bdc3c7, stop:1 #95a5a6);
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #95a5a6, stop:1 #7f8c8d);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background: #f1f3f4;
    height: 12px;
    border-radius: 6px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #bdc3c7, stop:1 #95a5a6);
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #95a5a6, stop:1 #7f8c8d);
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* تأثيرات إضافية */
QFrame {
    border-radius: 8px;
}

QLabel {
    color: #2c3e50;
}

/* تحسينات للنص العربي */
* {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* تحسينات إضافية */
#controlButton:focus, #statsLabel:focus {
    outline: 2px solid #3498db;
}
