"""
أمثلة على استخدام نظام عرض الإنجاز المتقدم
================================================

هذا الملف يحتوي على أمثلة مختلفة لكيفية استخدام وتخصيص نظام عرض الإنجاز
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
import sys

# استيراد النظام الرئيسي
from اختبار import AdvancedProgressWindow

class CustomProgressWindow(AdvancedProgressWindow):
    """نسخة مخصصة من نافذة الإنجاز مع بيانات مختلفة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة المشاريع - نسخة مخصصة")
        
    def load_sample_data(self):
        """تحميل بيانات مخصصة"""
        custom_projects = [
            {
                "name": "تطوير تطبيق إدارة المشاريع",
                "client": "شركة البرمجيات المتقدمة",
                "status": "قيد الانجاز",
                "priority": "عالي",
                "progress": 85,
                "days_left": 3,
                "deadline": "2024-02-10",
                "notes": "في المرحلة الأخيرة من التطوير"
            },
            {
                "name": "تصميم واجهة المستخدم",
                "client": "استوديو التصميم الإبداعي",
                "status": "قيد الانجاز",
                "priority": "متوسط",
                "progress": 60,
                "days_left": 7,
                "deadline": "2024-02-20",
                "notes": "تحتاج مراجعة من العميل"
            },
            {
                "name": "نظام إدارة المحتوى",
                "client": "شركة النشر الرقمي",
                "status": "مكتمل",
                "priority": "عادي",
                "progress": 100,
                "days_left": 0,
                "deadline": "2024-01-28",
                "notes": "تم التسليم والاستلام"
            },
            {
                "name": "تطبيق التجارة الإلكترونية",
                "client": "متجر الإلكترونيات الحديث",
                "status": "قيد الانجاز",
                "priority": "عالي",
                "progress": 40,
                "days_left": -1,
                "deadline": "2024-01-31",
                "notes": "متأخر يوم واحد - يحتاج متابعة"
            },
            {
                "name": "نظام إدارة المخزون المتقدم",
                "client": "شركة اللوجستيات الذكية",
                "status": "معلق",
                "priority": "متوسط",
                "progress": 25,
                "days_left": 15,
                "deadline": "2024-02-25",
                "notes": "في انتظار موافقة على التصميم"
            },
            {
                "name": "منصة التعلم الذكي",
                "client": "معهد التكنولوجيا التعليمية",
                "status": "قيد الانجاز",
                "priority": "عالي",
                "progress": 70,
                "days_left": 10,
                "deadline": "2024-02-18",
                "notes": "تطوير الوحدات التفاعلية"
            }
        ]
        
        self.table.setRowCount(len(custom_projects))
        
        for row, project in enumerate(custom_projects):
            # تطبيق نفس منطق التحميل من الكلاس الأساسي
            self.table.setItem(row, 0, self.create_table_item(project["name"]))
            self.table.setItem(row, 1, self.create_table_item(project["client"]))
            
            status_item = self.create_table_item(project["status"])
            self.apply_status_color(status_item, project["status"])
            self.table.setItem(row, 2, status_item)
            
            priority_item = self.create_table_item(project["priority"])
            self.apply_priority_color(priority_item, project["priority"])
            self.table.setItem(row, 3, priority_item)
            
            progress_item = self.create_table_item("")
            progress_item.setData(Qt.UserRole, {
                "progress": project["progress"],
                "days_left": project["days_left"],
                "status": project["status"],
                "priority": project["priority"]
            })
            self.table.setItem(row, 4, progress_item)
            
            self.table.setItem(row, 5, self.create_table_item(project["deadline"]))
            self.table.setItem(row, 6, self.create_table_item(project["notes"]))
            
        self.update_statistics()
    
    def create_table_item(self, text):
        """إنشاء عنصر جدول مع تنسيق أساسي"""
        from PySide6.QtWidgets import QTableWidgetItem
        from PySide6.QtCore import Qt
        
        item = QTableWidgetItem(str(text))
        item.setTextAlignment(Qt.AlignCenter)
        return item

class MinimalProgressExample:
    """مثال مبسط لاستخدام مكون شريط التقدم فقط"""
    
    @staticmethod
    def create_simple_progress_table():
        from PySide6.QtWidgets import QTableWidget, QTableWidgetItem, QMainWindow
        from PySide6.QtCore import Qt
        from اختبار import AdvancedProgressBarDelegate
        
        # إنشاء نافذة بسيطة
        window = QMainWindow()
        window.setWindowTitle("مثال مبسط - شريط التقدم")
        window.resize(800, 400)
        
        # إنشاء جدول بسيط
        table = QTableWidget(3, 2)
        table.setHorizontalHeaderLabels(["المهمة", "التقدم"])
        window.setCentralWidget(table)
        
        # بيانات تجريبية بسيطة
        tasks = [
            {"name": "مهمة سريعة", "progress": 90, "days_left": 1, "status": "قيد الانجاز", "priority": "عالي"},
            {"name": "مهمة متوسطة", "progress": 50, "days_left": 5, "status": "قيد الانجاز", "priority": "متوسط"},
            {"name": "مهمة طويلة", "progress": 20, "days_left": 15, "status": "قيد الانجاز", "priority": "عادي"}
        ]
        
        # إضافة البيانات
        for row, task in enumerate(tasks):
            table.setItem(row, 0, QTableWidgetItem(task["name"]))
            
            progress_item = QTableWidgetItem()
            progress_item.setData(Qt.UserRole, {
                "progress": task["progress"],
                "days_left": task["days_left"],
                "status": task["status"],
                "priority": task["priority"]
            })
            table.setItem(row, 1, progress_item)
        
        # تطبيق المفوض المخصص
        delegate = AdvancedProgressBarDelegate()
        table.setItemDelegateForColumn(1, delegate)
        table.verticalHeader().setDefaultSectionSize(60)
        
        return window

def run_custom_example():
    """تشغيل المثال المخصص"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = CustomProgressWindow()
    window.show()
    
    return app.exec()

def run_minimal_example():
    """تشغيل المثال المبسط"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = MinimalProgressExample.create_simple_progress_table()
    window.show()
    
    return app.exec()

def run_animated_example():
    """مثال مع تحديث تلقائي للبيانات"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = AdvancedProgressWindow()
    
    def update_progress():
        """تحديث التقدم تلقائياً"""
        for row in range(window.table.rowCount()):
            progress_item = window.table.item(row, 4)
            if progress_item:
                data = progress_item.data(Qt.UserRole)
                if data and data.get("progress", 0) < 100:
                    # زيادة التقدم بـ 1%
                    new_progress = min(data["progress"] + 1, 100)
                    data["progress"] = new_progress
                    progress_item.setData(Qt.UserRole, data)
                    
                    # تحديث الأيام المتبقية
                    if new_progress == 100:
                        data["days_left"] = 0
                        data["status"] = "مكتمل"
                        progress_item.setData(Qt.UserRole, data)
        
        window.table.viewport().update()
        window.update_statistics()
    
    # تحديث كل ثانيتين
    timer = QTimer()
    timer.timeout.connect(update_progress)
    timer.start(2000)
    
    window.show()
    return app.exec()

if __name__ == "__main__":
    print("أمثلة نظام عرض الإنجاز المتقدم")
    print("=" * 40)
    print("1. المثال المخصص (بيانات مختلفة)")
    print("2. المثال المبسط (جدول أساسي)")
    print("3. المثال المتحرك (تحديث تلقائي)")
    print("4. النظام الأساسي")
    
    choice = input("\nاختر رقم المثال (1-4): ").strip()
    
    if choice == "1":
        print("تشغيل المثال المخصص...")
        run_custom_example()
    elif choice == "2":
        print("تشغيل المثال المبسط...")
        run_minimal_example()
    elif choice == "3":
        print("تشغيل المثال المتحرك...")
        run_animated_example()
    elif choice == "4":
        print("تشغيل النظام الأساسي...")
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        window = AdvancedProgressWindow()
        window.show()
        sys.exit(app.exec())
    else:
        print("اختيار غير صحيح!")
