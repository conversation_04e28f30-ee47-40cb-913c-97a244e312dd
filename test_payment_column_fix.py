#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة عمود اسم_العميل في دفعات المشروع
"""

import mysql.connector
from متغيرات import host, user, password

def test_payment_reports_queries():
    """اختبار استعلامات تقارير الدفعات"""
    print("🧪 اختبار استعلامات تقارير الدفعات...")
    
    try:
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # اختبار الاستعلام الأول (المستخدم في load_reports_Payments)
        print("\n📋 اختبار الاستعلام الأول:")
        cursor.execute("""
            SELECT dp.id, c.اسم_العميل, p.اسم_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع, dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم 
            FROM المشاريع_المدفوعات dp
            LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
            LEFT JOIN العملاء c ON dp.معرف_العميل = c.id
            LIMIT 5
        """)
        
        results1 = cursor.fetchall()
        print(f"✅ الاستعلام الأول نجح: {len(results1)} سجل")
        if results1:
            print("   عينة من النتائج:")
            for i, row in enumerate(results1[:2]):
                print(f"     {i+1}. ID: {row[0]}, العميل: {row[1]}, المشروع: {row[2]}, المبلغ: {row[4]}")
        
        # اختبار الاستعلام الثاني (المستخدم في load_reports_all_Payments)
        print("\n📋 اختبار الاستعلام الثاني:")
        cursor.execute("""
            SELECT dp.id, c.اسم_العميل, p.اسم_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع,
                   dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم, p.التصنيف
            FROM المشاريع_المدفوعات dp
            LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
            LEFT JOIN العملاء c ON dp.معرف_العميل = c.id
            LIMIT 5
        """)
        
        results2 = cursor.fetchall()
        print(f"✅ الاستعلام الثاني نجح: {len(results2)} سجل")
        if results2:
            print("   عينة من النتائج:")
            for i, row in enumerate(results2[:2]):
                print(f"     {i+1}. ID: {row[0]}, العميل: {row[1]}, المشروع: {row[2]}, التصنيف: {row[8]}")
        
        # اختبار جلب اسم العميل مباشرة
        print("\n📋 اختبار جلب اسم العميل مباشرة:")
        cursor.execute("SELECT id, اسم_العميل FROM العملاء LIMIT 3")
        clients = cursor.fetchall()
        print(f"✅ جلب أسماء العملاء نجح: {len(clients)} عميل")
        for client in clients:
            print(f"     العميل ID: {client[0]}, الاسم: {client[1]}")
        
        # اختبار الربط بين الجداول
        print("\n📋 اختبار الربط بين الجداول:")
        cursor.execute("""
            SELECT p.id, p.اسم_المشروع, c.اسم_العميل, p.معرف_العميل
            FROM المشاريع p
            LEFT JOIN العملاء c ON p.معرف_العميل = c.id
            LIMIT 3
        """)
        
        projects = cursor.fetchall()
        print(f"✅ ربط المشاريع والعملاء نجح: {len(projects)} مشروع")
        for project in projects:
            print(f"     المشروع ID: {project[0]}, الاسم: {project[1]}, العميل: {project[2]}")
        
        conn.close()
        print("\n✅ تم إغلاق الاتصال بنجاح")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        print(f"Error Code: {err.errno}")
        if hasattr(err, 'sqlstate'):
            print(f"SQL State: {err.sqlstate}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_client_name_retrieval():
    """اختبار جلب اسم العميل من معرف العميل"""
    print("\n🔍 اختبار جلب اسم العميل من معرف العميل...")
    
    try:
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        
        # جلب أول عميل للاختبار
        cursor.execute("SELECT id FROM العملاء LIMIT 1")
        client_result = cursor.fetchone()
        
        if client_result:
            client_id = client_result[0]
            print(f"📋 اختبار على العميل ID: {client_id}")
            
            # جلب اسم العميل
            cursor.execute("SELECT c.اسم_العميل FROM العملاء c WHERE c.id = %s", (client_id,))
            name_result = cursor.fetchone()
            
            if name_result:
                client_name = name_result[0]
                print(f"✅ تم جلب اسم العميل بنجاح: {client_name}")
            else:
                print("⚠️ لم يتم العثور على اسم العميل")
        else:
            print("⚠️ لا توجد عملاء في قاعدة البيانات للاختبار")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في جلب اسم العميل: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("اختبار إصلاح مشكلة عمود اسم_العميل في دفعات المشروع")
    print("=" * 60)
    
    # اختبار استعلامات التقارير
    reports_test_success = test_payment_reports_queries()
    
    # اختبار جلب اسم العميل
    client_name_test_success = test_client_name_retrieval()
    
    print("\n" + "=" * 60)
    if reports_test_success and client_name_test_success:
        print("✅ جميع الاختبارات نجحت - تم إصلاح مشكلة عمود اسم_العميل!")
        print("✅ استعلامات تقارير الدفعات تعمل بشكل صحيح")
        print("✅ جلب أسماء العملاء يعمل بشكل صحيح")
    else:
        print("❌ بعض الاختبارات فشلت - قد تحتاج إلى مراجعة إضافية")
    print("=" * 60)
