#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ستايل شيت مميز واحترافي لشجرة الحسابات
"""

# الألوان الرئيسية
PRIMARY_COLOR = "#2c3e50"       # لون أساسي داكن
SECONDARY_COLOR = "#3498db"     # لون ثانوي أزرق
ACCENT_COLOR = "#e74c3c"        # لون التأكيد أحمر
SUCCESS_COLOR = "#2ecc71"       # لون النجاح أخضر
WARNING_COLOR = "#f39c12"       # لون التحذير برتقالي
BACKGROUND_COLOR = "#ecf0f1"    # لون الخلفية فاتح
TEXT_COLOR = "#2c3e50"          # لون النص داكن
BORDER_COLOR = "#bdc3c7"        # لون الحدود رمادي فاتح

# ستايل شيت التطبيق الرئيسي
MAIN_STYLE = f"""
QMainWindow, QDialog {{
    background-color: {BACKGROUND_COLOR};
}}

QLabel {{
    color: {TEXT_COLOR};
    font-size: 12px;
}}

QLabel[title="true"] {{
    font-size: 18px;
    font-weight: bold;
    color: {PRIMARY_COLOR};
    padding: 10px;
}}

QPushButton {{
    background-color: {SECONDARY_COLOR};
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}}

QPushButton:hover {{
    background-color: #2980b9;
}}

QPushButton:pressed {{
    background-color: #1f6dad;
}}

QPushButton[accent="true"] {{
    background-color: {ACCENT_COLOR};
}}

QPushButton[accent="true"]:hover {{
    background-color: #c0392b;
}}

QPushButton[success="true"] {{
    background-color: {SUCCESS_COLOR};
}}

QPushButton[success="true"]:hover {{
    background-color: #27ae60;
}}

QPushButton[warning="true"] {{
    background-color: {WARNING_COLOR};
}}

QPushButton[warning="true"]:hover {{
    background-color: #e67e22;
}}

QLineEdit, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
    border: 1px solid {BORDER_COLOR};
    border-radius: 4px;
    padding: 8px;
    background-color: white;
    color: {TEXT_COLOR};
}}

QLineEdit:focus, QTextEdit:focus, QDateEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
    border: 2px solid {SECONDARY_COLOR};
}}

QTreeWidget {{
    border: 1px solid {BORDER_COLOR};
    border-radius: 4px;
    background-color: white;
    alternate-background-color: #f9f9f9;
    selection-background-color: {SECONDARY_COLOR};
    selection-color: white;
}}

QTreeWidget::item {{
    padding: 5px;
    border-bottom: 1px solid #f0f0f0;
}}

QTreeWidget::item:selected {{
    background-color: {SECONDARY_COLOR};
    color: white;
}}

QTableWidget {{
    border: 1px solid {BORDER_COLOR};
    border-radius: 4px;
    background-color: white;
    alternate-background-color: #f9f9f9;
    gridline-color: #f0f0f0;
    selection-background-color: {SECONDARY_COLOR};
    selection-color: white;
}}

QTableWidget::item {{
    padding: 5px;
}}

QTableWidget::item:selected {{
    background-color: {SECONDARY_COLOR};
    color: white;
}}

QHeaderView::section {{
    background-color: {PRIMARY_COLOR};
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
}}

QTabWidget::pane {{
    border: 1px solid {BORDER_COLOR};
    border-radius: 4px;
    background-color: white;
}}

QTabBar::tab {{
    background-color: #f0f0f0;
    color: {TEXT_COLOR};
    padding: 10px 20px;
    border: 1px solid {BORDER_COLOR};
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}}

QTabBar::tab:selected {{
    background-color: white;
    border-bottom: 2px solid {SECONDARY_COLOR};
}}

QTabBar::tab:hover {{
    background-color: #e0e0e0;
}}

QGroupBox {{
    border: 1px solid {BORDER_COLOR};
    border-radius: 4px;
    margin-top: 20px;
    font-weight: bold;
    color: {PRIMARY_COLOR};
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 10px;
    background-color: {BACKGROUND_COLOR};
}}

QRadioButton, QCheckBox {{
    color: {TEXT_COLOR};
    spacing: 8px;
}}

QRadioButton::indicator, QCheckBox::indicator {{
    width: 18px;
    height: 18px;
}}

QRadioButton::indicator:checked, QCheckBox::indicator:checked {{
    background-color: {SECONDARY_COLOR};
}}

QStatusBar {{
    background-color: {PRIMARY_COLOR};
    color: white;
    padding: 5px;
}}

QSplitter::handle {{
    background-color: {BORDER_COLOR};
}}

QScrollBar:vertical {{
    border: none;
    background-color: #f0f0f0;
    width: 12px;
    margin: 0px;
}}

QScrollBar::handle:vertical {{
    background-color: {SECONDARY_COLOR};
    min-height: 20px;
    border-radius: 6px;
}}

QScrollBar:horizontal {{
    border: none;
    background-color: #f0f0f0;
    height: 12px;
    margin: 0px;
}}

QScrollBar::handle:horizontal {{
    background-color: {SECONDARY_COLOR};
    min-width: 20px;
    border-radius: 6px;
}}

QScrollBar::add-line, QScrollBar::sub-line {{
    border: none;
    background: none;
}}

QScrollBar::add-page, QScrollBar::sub-page {{
    background: none;
}}

QMenu {{
    background-color: white;
    border: 1px solid {BORDER_COLOR};
    border-radius: 4px;
}}

QMenu::item {{
    padding: 8px 20px;
}}

QMenu::item:selected {{
    background-color: {SECONDARY_COLOR};
    color: white;
}}

QToolTip {{
    background-color: {PRIMARY_COLOR};
    color: white;
    border: none;
    padding: 5px;
    border-radius: 4px;
}}
"""

# ستايل خاص بالحسابات المدينة والدائنة
ACCOUNT_STYLES = {
    "مدين": f"""
        QLabel[account="true"] {{
            color: {ACCENT_COLOR};
            font-weight: bold;
        }}
        QTableWidgetItem[account="مدين"] {{
            color: {ACCENT_COLOR};
        }}
    """,
    "دائن": f"""
        QLabel[account="true"] {{
            color: {SUCCESS_COLOR};
            font-weight: bold;
        }}
        QTableWidgetItem[account="دائن"] {{
            color: {SUCCESS_COLOR};
        }}
    """
}

# ستايل خاص بالأرصدة
BALANCE_STYLES = {
    "موجب": f"""
        QLabel[balance="true"] {{
            color: {SUCCESS_COLOR};
            font-weight: bold;
        }}
    """,
    "سالب": f"""
        QLabel[balance="true"] {{
            color: {ACCENT_COLOR};
            font-weight: bold;
        }}
    """
}

def apply_stylesheet(widget):
    """تطبيق الستايل شيت على الويدجت"""
    widget.setStyleSheet(MAIN_STYLE)

def set_account_style(widget, account_type):
    """تطبيق ستايل الحساب (مدين/دائن) على الويدجت"""
    if account_type in ACCOUNT_STYLES:
        current_style = widget.styleSheet()
        widget.setStyleSheet(current_style + ACCOUNT_STYLES[account_type])

def set_balance_style(widget, balance):
    """تطبيق ستايل الرصيد (موجب/سالب) على الويدجت"""
    balance_type = "موجب" if balance >= 0 else "سالب"
    current_style = widget.styleSheet()
    widget.setStyleSheet(current_style + BALANCE_STYLES[balance_type])
