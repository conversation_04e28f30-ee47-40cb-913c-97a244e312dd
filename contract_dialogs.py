#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
حوارات العقود مع الربط المحاسبي
"""

import sys
import os
from datetime import datetime, date
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QPushButton, QLabel, 
    QLineEdit, QDateEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QFileDialog
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont

# استيراد الدوال المساعدة
try:
    from functions import *
    from db import *
    from ستايل import *
except ImportError:
    print("تعذر استيراد الوحدات المطلوبة")

class ContractDialog(QDialog):
    """حوار إضافة/تعديل العقد"""
    
    def __init__(self, parent=None, contract_id=None):
        super().__init__(parent)
        self.contract_id = contract_id
        self.is_edit_mode = contract_id is not None
        
        self.setup_dialog()
        self.create_ui()
        
        if self.is_edit_mode:
            self.load_contract_data()
    
    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل العقد" if self.is_edit_mode else "إضافة عقد جديد"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # التصنيف
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("كود العقد")
        form_layout.addRow("التصنيف:", self.code_edit)
        
        # نوع العقد
        self.contract_type_combo = QComboBox()
        self.contract_type_combo.addItems([
            "عقد تصميم هندسي", "عقد تنفيذ أعمال", "عقد استشارات", 
            "عقد صيانة", "عقد توريد", "عقد أخرى"
        ])
        form_layout.addRow("نوع العقد:", self.contract_type_combo)
        
        # الطرف الأول
        self.party1_edit = QLineEdit()
        self.party1_edit.setPlaceholderText("اسم الطرف الأول")
        form_layout.addRow("الطرف الأول:", self.party1_edit)
        
        # الطرف الثاني
        self.party2_edit = QLineEdit()
        self.party2_edit.setPlaceholderText("اسم الطرف الثاني")
        form_layout.addRow("الطرف الثاني:", self.party2_edit)
        
        # موضوع العقد
        self.subject_edit = QTextEdit()
        self.subject_edit.setMaximumHeight(80)
        self.subject_edit.setPlaceholderText("وصف موضوع العقد")
        form_layout.addRow("موضوع العقد:", self.subject_edit)
        
        # قيمة العقد
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setMaximum(999999999.99)
        self.amount_spin.setSuffix(" ريال")
        form_layout.addRow("قيمة العقد:", self.amount_spin)
        
        # تاريخ البداية
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate())
        self.start_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ البداية:", self.start_date_edit)
        
        # تاريخ النهاية
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate().addDays(30))
        self.end_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ النهاية:", self.end_date_edit)
        
        # المدة بالأيام
        self.duration_spin = QSpinBox()
        self.duration_spin.setMaximum(9999)
        self.duration_spin.setSuffix(" يوم")
        form_layout.addRow("المدة:", self.duration_spin)
        
        # حالة العقد
        self.status_combo = QComboBox()
        self.status_combo.addItems(["ساري", "منتهي", "ملغي", "معلق"])
        form_layout.addRow("حالة العقد:", self.status_combo)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية")
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_contract)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_contract_data(self):
        """تحميل بيانات العقد للتعديل"""
        try:
            conn, cursor = con_db()
            
            cursor.execute("""
                SELECT التصنيف, نوع_العقد, الطرف_الأول, الطرف_الثاني, موضوع_العقد,
                       قيمة_العقد, تاريخ_البداية, تاريخ_النهاية, المدة, حالة_العقد, ملاحظات
                FROM العقود
                WHERE id = %s
            """, (self.contract_id,))
            
            contract = cursor.fetchone()
            
            if contract:
                code, contract_type, party1, party2, subject, amount, start_date, end_date, duration, status, notes = contract
                
                self.code_edit.setText(code or "")
                
                # تحديد نوع العقد
                type_index = self.contract_type_combo.findText(contract_type or "")
                if type_index >= 0:
                    self.contract_type_combo.setCurrentIndex(type_index)
                
                self.party1_edit.setText(party1 or "")
                self.party2_edit.setText(party2 or "")
                self.subject_edit.setPlainText(subject or "")
                self.amount_spin.setValue(float(amount) if amount else 0)
                
                if start_date:
                    self.start_date_edit.setDate(QDate.fromString(start_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
                if end_date:
                    self.end_date_edit.setDate(QDate.fromString(end_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
                
                self.duration_spin.setValue(duration if duration else 0)
                
                # تحديد حالة العقد
                status_index = self.status_combo.findText(status or "")
                if status_index >= 0:
                    self.status_combo.setCurrentIndex(status_index)
                
                self.notes_edit.setPlainText(notes or "")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات العقد: {str(e)}")
    
    def save_contract(self):
        """حفظ بيانات العقد"""
        code = self.code_edit.text().strip()
        contract_type = self.contract_type_combo.currentText()
        party1 = self.party1_edit.text().strip()
        party2 = self.party2_edit.text().strip()
        subject = self.subject_edit.toPlainText().strip()
        amount = self.amount_spin.value()
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
        duration = self.duration_spin.value()
        status = self.status_combo.currentText()
        notes = self.notes_edit.toPlainText().strip()
        
        # التحقق من صحة البيانات
        if not code:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال كود العقد")
            return
        
        if not party1:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال الطرف الأول")
            return
        
        if not party2:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال الطرف الثاني")
            return
        
        if amount <= 0:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال قيمة عقد أكبر من صفر")
            return
        
        try:
            conn, cursor = con_db()
            
            username = "admin"  # يمكن الحصول عليه من النظام
            year = datetime.now().year
            
            if self.is_edit_mode:  # تعديل عقد موجود
                cursor.execute("""
                    UPDATE العقود
                    SET التصنيف = %s, نوع_العقد = %s, الطرف_الأول = %s, الطرف_الثاني = %s,
                        موضوع_العقد = %s, قيمة_العقد = %s, تاريخ_البداية = %s, تاريخ_النهاية = %s,
                        المدة = %s, حالة_العقد = %s, ملاحظات = %s, المستخدم = %s, السنة = %s
                    WHERE id = %s
                """, (
                    code, contract_type, party1, party2, subject, amount, start_date, end_date,
                    duration, status, notes, username, year, self.contract_id
                ))
            else:  # إضافة عقد جديد
                cursor.execute("""
                    INSERT INTO العقود
                    (التصنيف, نوع_العقد, الطرف_الأول, الطرف_الثاني, موضوع_العقد, قيمة_العقد,
                     تاريخ_البداية, تاريخ_النهاية, المدة, حالة_العقد, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    code, contract_type, party1, party2, subject, amount, start_date, end_date,
                    duration, status, notes, username, year
                ))
                
                # الحصول على id العقد الجديد
                self.contract_id = cursor.lastrowid
            
            conn.commit()
            cursor.close()
            conn.close()
            
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} العقد بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في حفظ البيانات", f"حدث خطأ أثناء حفظ بيانات العقد: {str(e)}")


class ContractPaymentsDialog(QDialog):
    """حوار عرض وإدارة دفعات العقد"""
    
    def __init__(self, parent=None, contract_id=None, contract_code=""):
        super().__init__(parent)
        self.contract_id = contract_id
        self.contract_code = contract_code
        
        self.setup_dialog()
        self.create_ui()
        self.load_payments()
    
    def setup_dialog(self):
        """إعداد الحوار"""
        self.setWindowTitle(f"دفعات العقد - {self.contract_code}")
        self.setGeometry(200, 200, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        self.add_payment_btn = QPushButton("إضافة دفعة")
        self.add_payment_btn.clicked.connect(self.add_payment)
        toolbar_layout.addWidget(self.add_payment_btn)
        
        self.edit_payment_btn = QPushButton("تعديل دفعة")
        self.edit_payment_btn.clicked.connect(self.edit_payment)
        toolbar_layout.addWidget(self.edit_payment_btn)
        
        self.delete_payment_btn = QPushButton("حذف دفعة")
        self.delete_payment_btn.clicked.connect(self.delete_payment)
        toolbar_layout.addWidget(self.delete_payment_btn)
        
        layout.addLayout(toolbar_layout)
        
        # جدول الدفعات
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(6)
        self.payments_table.setHorizontalHeaderLabels([
            "وصف الدفعة", "المبلغ", "تاريخ الدفعة", "حالة الدفعة", "تاريخ السداد", "ملاحظات"
        ])
        self.payments_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.payments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.payments_table.doubleClicked.connect(self.edit_payment)
        layout.addWidget(self.payments_table)
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
    
    def load_payments(self):
        """تحميل دفعات العقد"""
        try:
            conn, cursor = con_db()
            
            cursor.execute("""
                SELECT id, وصف_الدفعة, المبلغ, تاريخ_الدفعة, حالة_الدفعة, تاريخ_السداد, ملاحظات
                FROM دفعات_العقود
                WHERE معرف_العقد = %s
                ORDER BY تاريخ_الدفعة DESC
            """, (self.contract_id,))
            
            payments = cursor.fetchall()
            
            self.payments_table.setRowCount(len(payments))
            
            for row, payment in enumerate(payments):
                payment_id, description, amount, payment_date, status, payment_date_actual, notes = payment
                
                self.payments_table.setItem(row, 0, QTableWidgetItem(description or ""))
                self.payments_table.setItem(row, 1, QTableWidgetItem(f"{amount:,.2f}" if amount else "0"))
                self.payments_table.setItem(row, 2, QTableWidgetItem(payment_date.strftime("%Y-%m-%d") if payment_date else ""))
                self.payments_table.setItem(row, 3, QTableWidgetItem(status or ""))
                self.payments_table.setItem(row, 4, QTableWidgetItem(payment_date_actual.strftime("%Y-%m-%d") if payment_date_actual else ""))
                self.payments_table.setItem(row, 5, QTableWidgetItem(notes or ""))
                
                # تخزين id الدفعة
                self.payments_table.item(row, 0).setData(Qt.UserRole, payment_id)
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل دفعات العقد: {str(e)}")
    
    def add_payment(self):
        """إضافة دفعة جديدة"""
        dialog = ContractPaymentDialog(self, self.contract_id)
        if dialog.exec_():
            self.load_payments()
    
    def edit_payment(self):
        """تعديل الدفعة المحددة"""
        selected_rows = self.payments_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد دفعة للتعديل")
            return
        
        selected_row = self.payments_table.currentRow()
        payment_id = self.payments_table.item(selected_row, 0).data(Qt.UserRole)
        
        dialog = ContractPaymentDialog(self, self.contract_id, payment_id)
        if dialog.exec_():
            self.load_payments()
    
    def delete_payment(self):
        """حذف الدفعة المحددة"""
        selected_rows = self.payments_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد دفعة للحذف")
            return
        
        selected_row = self.payments_table.currentRow()
        payment_id = self.payments_table.item(selected_row, 0).data(Qt.UserRole)
        payment_description = self.payments_table.item(selected_row, 0).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", f"هل أنت متأكد من حذف الدفعة '{payment_description}'؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                conn, cursor = con_db()
                
                cursor.execute("DELETE FROM دفعات_العقود WHERE id = %s", (payment_id,))
                conn.commit()
                
                cursor.close()
                conn.close()
                
                self.load_payments()
                QMessageBox.information(self, "نجح", "تم حذف الدفعة بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ في حذف البيانات", f"حدث خطأ أثناء حذف الدفعة: {str(e)}")


class ContractPaymentDialog(QDialog):
    """حوار إضافة/تعديل دفعة العقد مع الربط المحاسبي"""

    def __init__(self, parent=None, contract_id=None, payment_id=None):
        super().__init__(parent)
        self.contract_id = contract_id
        self.payment_id = payment_id
        self.is_edit_mode = payment_id is not None

        self.setup_dialog()
        self.create_ui()

        if self.is_edit_mode:
            self.load_payment_data()

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل الدفعة" if self.is_edit_mode else "إضافة دفعة جديدة"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # وصف الدفعة
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف الدفعة")
        form_layout.addRow("وصف الدفعة:", self.description_edit)

        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setMaximum(999999999.99)
        self.amount_spin.setSuffix(" ريال")
        form_layout.addRow("المبلغ:", self.amount_spin)

        # تاريخ الدفعة
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ الدفعة:", self.payment_date_edit)

        # حالة الدفعة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["غير مدفوعة", "مدفوعة", "مدفوعة جزئياً", "ملغية"])
        form_layout.addRow("حالة الدفعة:", self.status_combo)

        # تاريخ السداد
        self.payment_actual_date_edit = QDateEdit()
        self.payment_actual_date_edit.setDate(QDate.currentDate())
        self.payment_actual_date_edit.setCalendarPopup(True)
        self.payment_actual_date_edit.setEnabled(False)  # يتم تفعيله عند تغيير الحالة
        form_layout.addRow("تاريخ السداد:", self.payment_actual_date_edit)

        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية")
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # ربط تغيير حالة الدفعة بتفعيل تاريخ السداد
        self.status_combo.currentTextChanged.connect(self.on_status_changed)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_payment)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def on_status_changed(self, status):
        """معالجة تغيير حالة الدفعة"""
        self.payment_actual_date_edit.setEnabled(status == "مدفوعة")

    def load_payment_data(self):
        """تحميل بيانات الدفعة للتعديل"""
        try:
            conn, cursor = con_db()

            cursor.execute("""
                SELECT وصف_الدفعة, المبلغ, تاريخ_الدفعة, حالة_الدفعة, تاريخ_السداد, ملاحظات
                FROM دفعات_العقود
                WHERE id = %s
            """, (self.payment_id,))

            payment = cursor.fetchone()

            if payment:
                description, amount, payment_date, status, payment_actual_date, notes = payment

                self.description_edit.setText(description or "")
                self.amount_spin.setValue(float(amount) if amount else 0)

                if payment_date:
                    self.payment_date_edit.setDate(QDate.fromString(payment_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

                # تحديد حالة الدفعة
                status_index = self.status_combo.findText(status or "")
                if status_index >= 0:
                    self.status_combo.setCurrentIndex(status_index)

                if payment_actual_date:
                    self.payment_actual_date_edit.setDate(QDate.fromString(payment_actual_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

                self.notes_edit.setPlainText(notes or "")

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات الدفعة: {str(e)}")

    def save_payment(self):
        """حفظ بيانات الدفعة مع الربط المحاسبي"""
        description = self.description_edit.text().strip()
        amount = self.amount_spin.value()
        payment_date = self.payment_date_edit.date().toString("yyyy-MM-dd")
        status = self.status_combo.currentText()
        payment_actual_date = self.payment_actual_date_edit.date().toString("yyyy-MM-dd") if status == "مدفوعة" else None
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not description:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال وصف الدفعة")
            return

        if amount <= 0:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال مبلغ أكبر من صفر")
            return

        try:
            conn, cursor = con_db()

            username = "admin"  # يمكن الحصول عليه من النظام
            year = datetime.now().year

            # بيانات الدفعة
            payment_data = (
                self.contract_id,
                description,
                amount,
                payment_date,
                status,
                payment_actual_date,
                notes,
                username,
                year
            )

            if self.is_edit_mode:
                # تحديث الدفعة
                cursor.execute("""
                    UPDATE دفعات_العقود SET
                        معرف_العقد = %s, وصف_الدفعة = %s, المبلغ = %s, تاريخ_الدفعة = %s,
                        حالة_الدفعة = %s, تاريخ_السداد = %s, ملاحظات = %s, المستخدم = %s, السنة = %s
                    WHERE id = %s
                """, payment_data + (self.payment_id,))
            else:
                # إضافة دفعة جديدة
                cursor.execute("""
                    INSERT INTO دفعات_العقود
                    (معرف_العقد, وصف_الدفعة, المبلغ, تاريخ_الدفعة, حالة_الدفعة,
                     تاريخ_السداد, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, payment_data)

                # الحصول على id الدفعة الجديدة
                self.payment_id = cursor.lastrowid

            conn.commit()

            # ===== الربط المحاسبي =====
            # تسجيل الدفعة محاسبياً فقط إذا كانت مدفوعة
            if status == "مدفوعة":
                try:
                    # إنشاء نظام الربط المحاسبي
                    from accounting_integration import AccountingIntegration
                    accounting = AccountingIntegration()

                    # إعداد بيانات دفعة العقد للنظام المحاسبي
                    contract_payment_data = {
                        'id': self.payment_id,
                        'معرف_العقد': self.contract_id,
                        'وصف_الدفعة': description,
                        'المبلغ': amount,
                        'تاريخ_الدفعة': payment_actual_date or payment_date,
                        'حالة_الدفعة': status,
                        'ملاحظات': notes,
                        'المستخدم': username
                    }

                    # تسجيل دفعة العقد محاسبياً
                    success, message = accounting.record_contract_payment(contract_payment_data)

                    if success:
                        print(f"تم تسجيل دفعة العقد محاسبياً: {message}")
                    else:
                        print(f"خطأ في التسجيل المحاسبي: {message}")
                        # يمكن إضافة تحذير للمستخدم هنا إذا أردت

                    accounting.close_connection()

                except Exception as e:
                    print(f"خطأ في الربط المحاسبي: {e}")
                    # الاستمرار حتى لو فشل الربط المحاسبي
            # ===== نهاية الربط المحاسبي =====

            cursor.close()
            conn.close()

            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} الدفعة بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في حفظ البيانات", f"حدث خطأ أثناء حفظ بيانات الدفعة: {str(e)}")
