from for_all import *
from ستايل import *
from ui_boton import *
from db import *
import qtawesome as qta
from datetime import date

"""
نافذة إدارة العهد المالية للمشاريع
"""
class CustodyManagementWindow(QDialog):
    def __init__(self, parent=None, project_id=None, project_name=None, client_name=None):
        super().__init__(parent)
        self.parent = parent
        self.project_id = project_id
        self.project_name = project_name
        self.client_name = client_name
        self.current_custody_id = None

        self.setup_window()
        self.create_ui()
        self.load_project_client_info()
        self.load_custody_data()
        apply_stylesheet(self)
        

    def setup_window(self):
        """إعداد النافذة الأساسية"""
        self.setWindowTitle(f"إدارة العهد المالية - {self.project_name or 'جميع المشاريع'}")
        self.setGeometry(100, 100, 1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        main_layout = QVBoxLayout(self)

        # بطاقات المعلومات والأزرار في الأعلى
        self.create_info_cards_section(main_layout)

        # التابات الرئيسية مع تحسينات
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setTabsClosable(False)
        
        # تطبيق ستايل مخصص للتابات
        self.tab_widget.setStyleSheet("""
            QTabWidget {
                background-color: transparent;
                border: none;
            }
            QTabWidget::pane {
                background-color: #ffffff;
                border: 1px solid #4D2D7F;
                border-radius: 12px;
                padding: 10px;
                margin-top: 8px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ffffff, stop:1 #f8f9fa);
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-bottom: none;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                padding: 5px 5px;
                margin-right: 3px;
                margin-left: 3px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
                min-height: 1px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: 3px solid #2980b9;
                border-bottom: none;
                font-size: 16px;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ecf0f1, stop:1 #d5dbdb);
                color: #2980b9;
                border: 2px solid #3498db;
                border-bottom: none;
            }
        """)
        
        main_layout.addWidget(self.tab_widget)

        # إنشاء التابات
        self.create_custody_tab()
        self.create_expenses_tab()
        self.create_payments_tab()
        self.create_reports_tab()

    def create_info_cards_section(self, main_layout):
        """إنشاء قسم بطاقات المعلومات والأزرار"""
        # حاوية رئيسية للقسم العلوي
        top_section = QWidget()
        top_layout = QVBoxLayout(top_section)
        top_layout.setSpacing(15)
        top_layout.setContentsMargins(20, 20, 20, 10)

        # صف بطاقات المعلومات
        info_cards_layout = QHBoxLayout()
        info_cards_layout.setSpacing(15)

        # بطاقة معلومات المشروع والعميل
        self.create_project_info_card(info_cards_layout)

        # بطاقات الإحصائيات
        self.create_statistics_cards(info_cards_layout)

        top_layout.addLayout(info_cards_layout)

        # صف الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(0, 10, 0, 0)

        # أزرار العمليات الرئيسية
        self.create_action_buttons(buttons_layout)

        top_layout.addLayout(buttons_layout)

        main_layout.addWidget(top_section)

    def create_project_info_card(self, layout):
        """إنشاء بطاقة معلومات المشروع والعميل"""
        info_card = QGroupBox("معلومات المشروع")
        info_card.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 4px solid #3498db;
                border-radius: 10px;
                margin: 5px 0px;
                padding: 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ffffff, stop:1 #f8f9fa);
                box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                background-color: #ffffff;
                color: #2c3e50;
                font-size: 14px;
                font-weight: bold;
                border: 1px solid #3498db;
                border-radius: 6px;
            }
        """)

        card_layout = QGridLayout(info_card)
        card_layout.setSpacing(8)
        card_layout.setContentsMargins(15, 18, 15, 10)

        # اسم المشروع
        project_label = QLabel("اسم المشروع:")
        project_label.setStyleSheet("""
            font-weight: bold; 
            color: #34495e; 
            font-size: 16px;
            padding: 3px;
        """)
        self.project_name_value = QLabel(self.project_name or "غير محدد")
        self.project_name_value.setStyleSheet("""
            font-weight: bold; 
            color: #2980b9; 
            font-size: 16px;
            padding: 3px;
            background-color: #ecf0f1;
            border-radius: 4px;
        """)

        # اسم العميل
        client_label = QLabel("اسم العميل:")
        client_label.setStyleSheet("""
            font-weight: bold; 
            color: #34495e; 
            font-size: 16px;
            padding: 3px;
        """)
        self.client_name_value = QLabel(self.client_name or "غير محدد")
        self.client_name_value.setStyleSheet("""
            font-weight: bold; 
            color: #27ae60; 
            font-size: 16px;
            padding: 3px;
            background-color: #ecf0f1;
            border-radius: 4px;
        """)

        card_layout.addWidget(project_label, 0, 0)
        card_layout.addWidget(self.project_name_value, 0, 1)
        card_layout.addWidget(client_label, 1, 0)
        card_layout.addWidget(self.client_name_value, 1, 1)

        layout.addWidget(info_card)

    def create_statistics_cards(self, layout):
        """إنشاء بطاقات الإحصائيات"""
        # بطاقة إجمالي العهد
        total_custody_card = self.create_stat_card("إجمالي العهد", "0", "#3498db")
        self.total_custody_label = total_custody_card.findChild(QLabel, "value_label")
        layout.addWidget(total_custody_card)

        # بطاقة إجمالي المصروفات
        total_expenses_card = self.create_stat_card("إجمالي المصروفات", "0", "#e74c3c")
        self.total_expenses_label = total_expenses_card.findChild(QLabel, "value_label")
        layout.addWidget(total_expenses_card)

        # بطاقة إجمالي المتبقي
        total_remaining_card = self.create_stat_card("إجمالي المتبقي", "0", "#27ae60")
        self.total_remaining_label = total_remaining_card.findChild(QLabel, "value_label")
        layout.addWidget(total_remaining_card)

        # بطاقة عدد العهد
        count_custody_card = self.create_stat_card("عدد العهد", "0", "#f39c12")
        self.count_custody_label = count_custody_card.findChild(QLabel, "value_label")
        layout.addWidget(count_custody_card)

    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية واحدة"""
        card = QGroupBox(title)
        card.setStyleSheet(f"""
            QGroupBox {{
                font-size: 12px;
                font-weight: bold;
                color: {color};
                border: 2px solid {color};
                border-radius: 8px;
                margin: 4px;
                padding: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ffffff, stop:1 #f8f9fa);
                min-width: 150px;
                max-width: 200px;
                min-height: 50px;
                max-height: 70px;
                box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                background-color: #ffffff;
                color: {color};
                font-size: 11px;
                font-weight: bold;
                border: 1px solid {color};
                border-radius: 4px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(8, 15, 8, 8)
        layout.setSpacing(4)

        value_label = QLabel(value)
        value_label.setObjectName("value_label")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: bold;
            color: {color};
            padding: 4px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 6px;
            border: 1px solid {color};
        """)

        layout.addWidget(value_label)
        return card

    def create_action_buttons(self, layout):
        """إنشاء أزرار العمليات الرئيسية"""
        layout.addStretch()
         # الصف الثاني من الأزرار
        second_row_layout = QHBoxLayout()
        
        # تم إزالة زر طباعة سند صرف من هنا - سيتم نقله إلى تاب المصروفات
        # تم إزالة زر طباعة سند قبض من هنا - سيتم نقله إلى تاب دفعات العهدة

        # زر طباعة عام
        

        layout.addLayout(second_row_layout)
        layout.addStretch()

    def get_button_style(self, bg_color, hover_color, pressed_color):
        """إرجاع ستايل موحد للأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: bold;
                min-width: 110px;
                max-width: 140px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
        """

    def load_project_client_info(self):
        """تحميل معلومات المشروع والعميل"""
        if not self.project_id:
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.اسم_المشروع, c.اسم_العميل
                FROM المشاريع p
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                WHERE p.id = %s
            """, (self.project_id,))

            result = cursor.fetchone()
            if result:
                self.project_name = result[0]
                self.client_name = result[1]

                # تحديث النصوص في البطاقات
                if hasattr(self, 'project_name_value'):
                    self.project_name_value.setText(self.project_name or "غير محدد")
                if hasattr(self, 'client_name_value'):
                    self.client_name_value.setText(self.client_name or "غير محدد")

        except Exception as e:
            print(f"خطأ في تحميل معلومات المشروع: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()


    def create_custody_tab(self):
        """إنشاء تاب العهد المالية"""
        custody_widget = QWidget()
        layout = QVBoxLayout(custody_widget)
        
        # الصف الأول من الأزرار
        first_row_layout = QHBoxLayout()

        # زر إضافة عهدة جديدة
        self.add_custody_btn = QPushButton(qta.icon('fa5s.plus', color='white'), "عهدة جديدة")
        self.add_custody_btn.setStyleSheet(self.get_button_style("#27ae60", "#229954", "#1e8449"))
        self.add_custody_btn.clicked.connect(self.add_new_custody)
        first_row_layout.addWidget(self.add_custody_btn)

        # زر تعديل العهدة
        self.edit_custody_btn = QPushButton(qta.icon('fa5s.edit', color='white'), "تعديل العهدة")
        self.edit_custody_btn.setStyleSheet(self.get_button_style("#3498db", "#2980b9", "#21618c"))
        self.edit_custody_btn.clicked.connect(self.edit_custody)
        first_row_layout.addWidget(self.edit_custody_btn)

        # زر حذف العهدة
        self.delete_custody_btn = QPushButton(qta.icon('fa5s.trash', color='white'), "حذف العهدة")
        self.delete_custody_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b", "#a93226"))
        self.delete_custody_btn.clicked.connect(self.delete_custody)
        first_row_layout.addWidget(self.delete_custody_btn)

        # زر إدراج النسبة
        self.add_percentage_btn = QPushButton(qta.icon('fa5s.percentage', color='white'), "إدراج النسبة")
        self.add_percentage_btn.setStyleSheet(self.get_button_style("#f39c12", "#e67e22", "#d35400"))
        self.add_percentage_btn.clicked.connect(self.add_percentage)
        first_row_layout.addWidget(self.add_percentage_btn)

        # زر إدراج المبلغ
        self.add_amount_btn = QPushButton(qta.icon('fa5s.money-bill', color='white'), "إدراج المبلغ")
        self.add_amount_btn.setStyleSheet(self.get_button_style("#16a085", "#138d75", "#117a65"))
        self.add_amount_btn.clicked.connect(self.add_amount)
        first_row_layout.addWidget(self.add_amount_btn)

        layout.addLayout(first_row_layout)

        # الصف الثاني من الأزرار
        second_row_layout = QHBoxLayout()

        # زر إغلاق العهدة
        self.close_custody_btn = QPushButton(qta.icon('fa5s.lock', color='white'), "إغلاق العهدة")
        self.close_custody_btn.setStyleSheet(self.get_button_style("#8e44ad", "#7d3c98", "#6c3483"))
        self.close_custody_btn.clicked.connect(self.close_custody)
        second_row_layout.addWidget(self.close_custody_btn)

        # زر ترحيل العهدة
        self.transfer_custody_btn = QPushButton(qta.icon('fa5s.exchange-alt', color='white'), "ترحيل العهدة")
        self.transfer_custody_btn.setStyleSheet(self.get_button_style("#2980b9", "#2471a3", "#1f618d"))
        self.transfer_custody_btn.clicked.connect(self.transfer_custody)
        second_row_layout.addWidget(self.transfer_custody_btn)

        self.print_btn = QPushButton(qta.icon('fa5s.print', color='white'), "طباعة")
        self.print_btn.setStyleSheet(self.get_button_style("#9b59b6", "#8e44ad", "#7d3c98"))
        self.print_btn.clicked.connect(self.print_report)
        second_row_layout.addWidget(self.print_btn)

        
        first_row_layout.addLayout(second_row_layout)
        layout.addLayout(first_row_layout)
        
        # إضافة مساحة فاصلة
        layout.addSpacing(10)

        # جدول العهد المالية
        self.custody_table = QTableWidget()
        self.custody_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.custody_table.setAlternatingRowColors(True)
        self.custody_table.itemSelectionChanged.connect(self.on_custody_selected)
        self.custody_table.itemDoubleClicked.connect(self.edit_custody)

        # أعمدة الجدول (تم إخفاء اسم المشروع واسم العميل)
        custody_columns = [
            "id", 
            "   رقم العهدة   ",
            "      اسم المشروع      ",
            "      اسم العميل      ", 
            "      مبلغ العهدة      ",
            "      نسبة المكتب %      ",
            "      اجمالي المكتب      ",
            "      المبلغ الصافي      ",
            "      المصروف      ",
            "      مبلغ المكتب       ",
            "      المتبقي      ",
            "      تاريخ العهدة      ",
            "      حالة العهدة      ",
            "ملاحظات"
        ]

        self.custody_table.setColumnCount(len(custody_columns))
        self.custody_table.setHorizontalHeaderLabels(custody_columns)
        self.custody_table.hideColumn(0)  # إخفاء عمود id
        self.custody_table.hideColumn(2)  # إخفاء عمود اسم المشروع
        self.custody_table.hideColumn(3)  # إخفاء عمود اسم العميل

        layout.addWidget(self.custody_table)
        table_setting(self.custody_table)

        self.tab_widget.addTab(custody_widget, "العهد المالية")

    def create_expenses_tab(self):
        """إنشاء تاب المصروفات"""
        expenses_widget = QWidget()
        layout = QVBoxLayout(expenses_widget)

        # معلومات العهدة المحددة
        info_layout = QHBoxLayout()
        self.custody_info_label = QLabel("اختر عهدة لعرض مصروفاتها")
        self.custody_info_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50;")
        info_layout.addWidget(self.custody_info_label)
        info_layout.addStretch()

        # زر إضافة مصروف
        add_expense_btn = QPushButton(qta.icon('fa5s.plus', color='green'), "إضافة مصروف")
        add_expense_btn.clicked.connect(self.add_expense)
        info_layout.addWidget(add_expense_btn)

        # زر طباعة سند صرف - تم نقله هنا
        self.print_expense_voucher_btn = QPushButton(qta.icon('fa5s.file-invoice', color='white'), "طباعة سند صرف")
        self.print_expense_voucher_btn.setStyleSheet(self.get_button_style("#dc7633", "#ca6f1e", "#b9770e"))
        self.print_expense_voucher_btn.clicked.connect(self.print_expense_voucher)
        info_layout.addWidget(self.print_expense_voucher_btn)

        self.print_btn = QPushButton(qta.icon('fa5s.print', color='white'), "طباعة")
        self.print_btn.setStyleSheet(self.get_button_style("#9b59b6", "#8e44ad", "#7d3c98"))
        self.print_btn.clicked.connect(self.print_report)
        info_layout.addWidget(self.print_btn)

        layout.addLayout(info_layout)

        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.itemDoubleClicked.connect(self.edit_expense)

        # أعمدة جدول المصروفات
        expenses_columns = [
            "id",
            "               وصف المصروف               ", 
            "     المبلغ     ",
            "     تاريخ المصروف     ",
            "          المستلم          ",
            "     طريقة الدفع     ",
            "     رقم الفاتورة     ",
            "     المورد     ", 
            "          فئة المصروف          ", 
            "ملاحظات"
        ]

        self.expenses_table.setColumnCount(len(expenses_columns))
        self.expenses_table.setHorizontalHeaderLabels(expenses_columns)
        self.expenses_table.hideColumn(0)  # إخفاء عمود id

        layout.addWidget(self.expenses_table)
        

        # أزرار عمليات المصروفات
        exp_buttons_layout = QHBoxLayout()

        edit_exp_btn = QPushButton(qta.icon('fa5s.edit', color='blue'), "تعديل مصروف")
        edit_exp_btn.clicked.connect(self.edit_expense)
        exp_buttons_layout.addWidget(edit_exp_btn)

        delete_exp_btn = QPushButton(qta.icon('fa5s.trash', color='red'), "حذف مصروف")
        delete_exp_btn.clicked.connect(self.delete_expense)
        exp_buttons_layout.addWidget(delete_exp_btn)

        exp_buttons_layout.addStretch()
        layout.addLayout(exp_buttons_layout)

        self.tab_widget.addTab(expenses_widget, "المصروفات")
        table_setting(self.expenses_table)
    
    def create_payments_tab(self):
        """إنشاء تاب دفعات العهدة"""
        
        payments_widget = QWidget()
        layout = QVBoxLayout(payments_widget)

        # معلومات العهدة المحددة
        info_layout = QHBoxLayout()
        self.payments_custody_info_label = QLabel("اختر عهدة لعرض دفعاتها")
        self.payments_custody_info_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50;")
        info_layout.addWidget(self.payments_custody_info_label)
        info_layout.addStretch()

        # زر إضافة دفعة
        add_payment_btn = QPushButton(qta.icon('fa5s.plus', color='green'), "إضافة دفعة")
        add_payment_btn.clicked.connect(self.add_payment)
        info_layout.addWidget(add_payment_btn)

        edit_payment_btn = QPushButton(qta.icon('fa5s.edit', color='blue'), "تعديل دفعة")
        edit_payment_btn.clicked.connect(self.edit_payment)
        info_layout.addWidget(edit_payment_btn)

        delete_payment_btn = QPushButton(qta.icon('fa5s.trash', color='red'), "حذف دفعة")
        delete_payment_btn.clicked.connect(self.delete_payment)
        info_layout.addWidget(delete_payment_btn)

        self.print_btn = QPushButton(qta.icon('fa5s.print', color='white'), "طباعة")
        self.print_btn.setStyleSheet(self.get_button_style("#9b59b6", "#8e44ad", "#7d3c98"))
        self.print_btn.clicked.connect(self.print_report)
        info_layout.addWidget(self.print_btn)

        # زر طباعة سند قبض - تم نقله هنا
        self.print_receipt_voucher_btn = QPushButton(qta.icon('fa5s.receipt', color='white'), "طباعة سند قبض")
        self.print_receipt_voucher_btn.setStyleSheet(self.get_button_style("#28b463", "#239b56", "#1e8449"))
        self.print_receipt_voucher_btn.clicked.connect(self.print_receipt_voucher)
        info_layout.addWidget(self.print_receipt_voucher_btn)

        layout.addLayout(info_layout)

        # جدول الدفعات
        self.payments_table = QTableWidget()
        self.payments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.itemDoubleClicked.connect(self.edit_payment)

        # أعمدة جدول الدفعات
        payments_columns = [
            "id",
            "   رقم الدفعة   ",
            "               وصف الدفعة               ",
            "      المبلغ      ", 
            "     تاريخ الدفعة     ",
            "     نوع الدفعة       ",
            "    طريقة الدفع       ",
            "          المستلم          ",
             "ملاحظات"
        ]

        self.payments_table.setColumnCount(len(payments_columns))
        self.payments_table.setHorizontalHeaderLabels(payments_columns)
        self.payments_table.hideColumn(0)  # إخفاء عمود id

        layout.addWidget(self.payments_table)
        
        self.tab_widget.addTab(payments_widget, "دفعات العهدة")
        table_setting(self.payments_table)

    def create_reports_tab(self):
        """إنشاء تاب التقارير"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)

        # فلاتر التقارير
        filters_layout = QGridLayout()

        # فلتر التاريخ
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addMonths(-1))
        self.from_date.setCalendarPopup(True)
        filters_layout.addWidget(self.from_date, 0, 1)

        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        filters_layout.addWidget(self.to_date, 0, 3)

        # فلتر حالة العهدة
        filters_layout.addWidget(QLabel("حالة العهدة:"), 1, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "مفتوحة", "مغلقة", "مرحلة"])
        filters_layout.addWidget(self.status_filter, 1, 1)

        # زر تطبيق الفلتر
        apply_filter_btn = QPushButton("تطبيق الفلتر")
        apply_filter_btn.clicked.connect(self.apply_report_filter)
        filters_layout.addWidget(apply_filter_btn, 1, 2)

        layout.addLayout(filters_layout)

        # ملخص التقرير
        summary_layout = QHBoxLayout()

        self.total_custody_label = QLabel("إجمالي العهد: 0")
        self.total_expenses_label = QLabel("إجمالي المصروفات: 0")
        self.total_remaining_label = QLabel("إجمالي المتبقي: 0")

        for label in [self.total_custody_label, self.total_expenses_label, self.total_remaining_label]:
            label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; border: 1px solid #bdc3c7; border-radius: 5px;")

        summary_layout.addWidget(self.total_custody_label)
        summary_layout.addWidget(self.total_expenses_label)
        summary_layout.addWidget(self.total_remaining_label)

        layout.addLayout(summary_layout)

        # جدول التقرير
        self.report_table = QTableWidget()
        self.report_table.setAlternatingRowColors(True)
        layout.addWidget(self.report_table)

        self.tab_widget.addTab(reports_widget, "التقارير")


    """تحميل بيانات العهد المالية"""
    def load_custody_data(self):
        
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # استعلام العهد المالية
            query = """
                SELECT
                    id, رقم_العهدة, اسم_المشروع, اسم_العميل, مبلغ_العهدة,
                    نسبة_المكتب, مبلغ_نسبة_المكتب, المبلغ_الصافي, المصروف,
                    مبلغ_المكتب_من_المصروف, المتبقي, تاريخ_العهدة, حالة_العهدة, ملاحظات
                FROM المقاولات_العهد
            """

            params = []
            if self.project_id:
                query += " WHERE معرف_المشروع = %s"
                params.append(self.project_id)

            query += " ORDER BY تاريخ_العهدة DESC"

            cursor.execute(query, params)
            custody_data = cursor.fetchall()

            # تحديث الجدول
            self.custody_table.setRowCount(len(custody_data))

            for row, data in enumerate(custody_data):
                for col, value in enumerate(data):
                    if value is None:
                        value = ""
                    elif isinstance(value, (int, float)) and col in [4, 6, 7, 8, 9, 10]:  # أعمدة المبالغ (تم إضافة العمود الجديد)
                        value = f"{value:,.2f}"
                    elif isinstance(value, date) and col == 11:  # عمود التاريخ (تم تحديث الفهرس)
                        value = value.strftime("%Y-%m-%d")

                    item = QTableWidgetItem(str(value))

                    # تلوين الأرقام السالبة باللون الأحمر
                    if isinstance(data[col], (int, float)) and col in [4, 6, 7, 8, 9, 10] and data[col] < 0:
                        item.setForeground(QColor("#e74c3c"))  # أحمر للقيم السالبة
                    elif isinstance(data[col], (int, float)) and col in [4, 6, 7, 8, 9, 10] and data[col] > 0:
                        item.setForeground(QColor("#27ae60"))  # أخضر للقيم الموجبة

                    self.custody_table.setItem(row, col, item)

            # تنسيق الجدول
            #self.custody_table.resizeColumnsToContents()

            # تحديث الإحصائيات
            self.update_statistics()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل بيانات العهد: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    """تحديث إحصائيات البطاقات"""
    def update_statistics(self):
        
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # استعلام الإحصائيات
            query = """
                SELECT
                    COUNT(*) as count_custody,
                    COALESCE(SUM(مبلغ_العهدة), 0) as total_custody,
                    COALESCE(SUM(المصروف), 0) as total_expenses,
                    COALESCE(SUM(المتبقي), 0) as total_remaining
                FROM المقاولات_العهد
            """

            params = []
            if self.project_id:
                query += " WHERE معرف_المشروع = %s"
                params.append(self.project_id)

            cursor.execute(query, params)
            result = cursor.fetchone()

            if result:
                count_custody, total_custody, total_expenses, total_remaining = result

                # تحديث البطاقات
                if hasattr(self, 'total_custody_label'):
                    self.total_custody_label.setText(f"{total_custody:,.0f}")

                if hasattr(self, 'total_expenses_label'):
                    self.total_expenses_label.setText(f"{total_expenses:,.0f}")

                if hasattr(self, 'total_remaining_label'):
                    self.total_remaining_label.setText(f"{total_remaining:,.0f}")
                    # تغيير لون المتبقي حسب القيمة
                    if total_remaining < 0:
                        self.total_remaining_label.setStyleSheet("""
                            font-size: 18px;
                            font-weight: bold;
                            color: #e74c3c;
                            padding: 5px;
                        """)
                    else:
                        self.total_remaining_label.setStyleSheet("""
                            font-size: 18px;
                            font-weight: bold;
                            color: #27ae60;
                            padding: 5px;
                        """)

                if hasattr(self, 'count_custody_label'):
                    self.count_custody_label.setText(str(count_custody))

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def on_custody_selected(self):
        """عند تحديد عهدة من الجدول"""
        current_row = self.custody_table.currentRow()
        if current_row >= 0:
            self.current_custody_id = int(self.custody_table.item(current_row, 0).text())
            custody_number = self.custody_table.item(current_row, 1).text()
            project_name = self.custody_table.item(current_row, 2).text()

            # تحديث معلومات العهدة في تاب المصروفات
            self.custody_info_label.setText(f"العهدة: {custody_number} - المشروع: {project_name}")

            # تحديث معلومات العهدة في تاب الدفعات
            self.payments_custody_info_label.setText(f"العهدة: {custody_number} - المشروع: {project_name}")

            # تحميل مصروفات العهدة
            self.load_expenses_data()

            # تحميل دفعات العهدة
            self.load_payments_data()

    def load_expenses_data(self):
        """تحميل مصروفات العهدة المحددة"""
        if not self.current_custody_id:
            self.expenses_table.setRowCount(0)
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    id, وصف_المصروف, المبلغ, تاريخ_المصروف, المستلم,
                    طريقة_الدفع, رقم_الفاتورة, المورد, فئة_المصروف, ملاحظات
                FROM مصروفات_العهد
                WHERE معرف_العهدة = %s
                ORDER BY تاريخ_المصروف DESC
            """, (self.current_custody_id,))

            expenses_data = cursor.fetchall()

            # تحديث جدول المصروفات
            self.expenses_table.setRowCount(len(expenses_data))

            for row, data in enumerate(expenses_data):
                for col, value in enumerate(data):
                    if value is None:
                        value = ""
                    elif isinstance(value, (int, float)) and col == 2:  # عمود المبلغ
                        value = f"{value:,.2f}"
                    elif isinstance(value, date) and col == 3:  # عمود التاريخ
                        value = value.strftime("%Y-%m-%d")

                    item = QTableWidgetItem(str(value))
                    self.expenses_table.setItem(row, col, item)

            # تنسيق الجدول
            #self.expenses_table.resizeColumnsToContents()
            table_setting(self.expenses_table)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المصروفات: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()
                

    def load_payments_data(self):
        """تحميل دفعات العهدة المحددة"""
        if not self.current_custody_id:
            self.payments_table.setRowCount(0)
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    id, وصف_الدفعة, المبلغ, تاريخ_الدفعة, نوع_الدفعة,
                    طريقة_الدفع, المستلم, ملاحظات
                FROM دفعات_العهد
                WHERE معرف_العهدة = %s
                ORDER BY تاريخ_الدفعة DESC
            """, (self.current_custody_id,))

            payments_data = cursor.fetchall()

            # تحديث جدول الدفعات
            self.payments_table.setRowCount(len(payments_data))

            for row, data in enumerate(payments_data):
                for col, value in enumerate(data):
                    if value is None:
                        value = ""
                    elif isinstance(value, (int, float)) and col == 2:  # عمود المبلغ
                        value = f"{value:,.2f}"
                    elif isinstance(value, date) and col == 3:  # عمود التاريخ
                        value = value.strftime("%Y-%m-%d")

                    item = QTableWidgetItem(str(value))

                    # تلوين المبالغ
                    if isinstance(data[col], (int, float)) and col == 3:
                        if data[col] < 0:
                            item.setForeground(QColor("#e74c3c"))  # أحمر للقيم السالبة
                        else:
                            item.setForeground(QColor("#27ae60"))  # أخضر للقيم الموجبة

                    self.payments_table.setItem(row, col, item)

            # تنسيق الجدول
            #self.payments_table.resizeColumnsToContents()
            table_setting(self.payments_table)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الدفعات: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def add_new_custody(self):
        """إضافة عهدة جديدة"""
        try:
            from حوارات_العهد import CustodyDialog
            dialog = CustodyDialog(self, project_id=self.project_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_custody_data()
                self.update_statistics()  # تحديث الإحصائيات
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استيراد حوار العهدة: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إضافة العهدة: {str(e)}")

    def edit_custody(self):
        """تعديل العهدة المحددة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة للتعديل")
            return

        try:
            from حوارات_العهد import CustodyDialog
            custody_id = int(self.custody_table.item(current_row, 0).text())
            dialog = CustodyDialog(self, custody_id=custody_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_custody_data()
                self.update_statistics()  # تحديث الإحصائيات
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استيراد حوار العهدة: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تعديل العهدة: {str(e)}")

    def delete_custody(self):
        """حذف العهدة المحددة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة للحذف")
            return

        custody_id = int(self.custody_table.item(current_row, 0).text())
        custody_number = self.custody_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف العهدة رقم: {custody_number}؟\n"
            "سيتم حذف جميع المصروفات المرتبطة بها أيضاً",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user, password=password,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                cursor.execute("DELETE FROM المقاولات_العهد WHERE id = %s", (custody_id,))
                conn.commit()

                QMessageBox.information(self, "نجح", "تم حذف العهدة بنجاح")
                self.load_custody_data()
                self.expenses_table.setRowCount(0)
                self.current_custody_id = None
                self.update_statistics()  # تحديث الإحصائيات

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في حذف العهدة: {str(e)}")
            finally:
                if 'conn' in locals():
                    conn.close()

    def close_custody(self):
        """إغلاق العهدة المحددة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة للإغلاق")
            return

        custody_id = int(self.custody_table.item(current_row, 0).text())
        custody_number = self.custody_table.item(current_row, 1).text()
        current_status = self.custody_table.item(current_row, 11).text()

        if current_status == "مغلقة":
            QMessageBox.information(self, "تنبيه", "العهدة مغلقة بالفعل")
            return

        reply = QMessageBox.question(
            self, "تأكيد الإغلاق",
            f"هل أنت متأكد من إغلاق العهدة رقم: {custody_number}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user, password=password,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                cursor.execute("""
                    UPDATE المقاولات_العهد
                    SET حالة_العهدة = 'مغلقة', تاريخ_الإغلاق = %s
                    WHERE id = %s
                """, (date.today(), custody_id))
                conn.commit()

                QMessageBox.information(self, "نجح", "تم إغلاق العهدة بنجاح")
                self.load_custody_data()
                self.update_statistics()  # تحديث الإحصائيات

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في إغلاق العهدة: {str(e)}")
            finally:
                if 'conn' in locals():
                    conn.close()

    def transfer_custody(self):
        """ترحيل العهدة إلى عهدة جديدة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة للترحيل")
            return

        try:
            from حوارات_العهد import TransferCustodyDialog
            custody_id = int(self.custody_table.item(current_row, 0).text())
            remaining_amount = float(self.custody_table.item(current_row, 10).text().replace(',', ''))  # تم تحديث الفهرس

            # السماح بترحيل القيم السالبة والموجبة
            if remaining_amount == 0:
                reply = QMessageBox.question(self, "تأكيد",
                                           "المبلغ المتبقي صفر. هل تريد إنشاء عهدة جديدة؟",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.No:
                    return
            elif remaining_amount < 0:
                reply = QMessageBox.question(self, "ترحيل عجز",
                                           f"المبلغ المتبقي سالب ({remaining_amount:,.2f}).\n"
                                           "هذا يعني وجود عجز في العهدة.\n"
                                           "هل تريد ترحيل هذا العجز إلى عهدة جديدة؟",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.No:
                    return

            dialog = TransferCustodyDialog(self, custody_id, remaining_amount)
            if dialog.exec_() == QDialog.Accepted:
                self.load_custody_data()
                self.update_statistics()  # تحديث الإحصائيات
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استيراد حوار الترحيل: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في ترحيل العهدة: {str(e)}")

    def add_expense(self):
        """إضافة مصروف جديد"""
        if not self.current_custody_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة أولاً")
            return

        try:
            from حوارات_العهد import ExpenseDialog
            dialog = ExpenseDialog(self, self.current_custody_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_expenses_data()
                self.load_custody_data()  # لتحديث المبالغ
                self.update_statistics()  # تحديث الإحصائيات
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استيراد حوار المصروف: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إضافة المصروف: {str(e)}")

    def edit_expense(self):
        """تعديل المصروف المحدد"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للتعديل")
            return

        try:
            from حوارات_العهد import ExpenseDialog
            expense_id = int(self.expenses_table.item(current_row, 0).text())
            dialog = ExpenseDialog(self, self.current_custody_id, expense_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_expenses_data()
                self.load_custody_data()  # لتحديث المبالغ
                self.update_statistics()  # تحديث الإحصائيات
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استيراد حوار المصروف: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تعديل المصروف: {str(e)}")

    def delete_expense(self):
        """حذف المصروف المحدد"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للحذف")
            return

        expense_id = int(self.expenses_table.item(current_row, 0).text())
        expense_desc = self.expenses_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المصروف: {expense_desc}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user, password=password,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                cursor.execute("DELETE FROM مصروفات_العهد WHERE id = %s", (expense_id,))
                conn.commit()

                QMessageBox.information(self, "نجح", "تم حذف المصروف بنجاح")
                self.load_expenses_data()
                self.load_custody_data()  # لتحديث المبالغ
                self.update_statistics()  # تحديث الإحصائيات

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في حذف المصروف: {str(e)}")
            finally:
                if 'conn' in locals():
                    conn.close()

    def add_payment(self):
        """إضافة دفعة جديدة"""
        if not self.current_custody_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة أولاً")
            return

        try:
            from حوارات_العهد import PaymentDialog
            dialog = PaymentDialog(self, self.current_custody_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_payments_data()
                self.load_custody_data()  # لتحديث المبالغ
                self.update_statistics()  # تحديث الإحصائيات
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استيراد حوار الدفعة: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إضافة الدفعة: {str(e)}")

    def edit_payment(self):
        """تعديل الدفعة المحددة"""
        current_row = self.payments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة للتعديل")
            return

        try:
            from حوارات_العهد import PaymentDialog
            payment_id = int(self.payments_table.item(current_row, 0).text())
            dialog = PaymentDialog(self, self.current_custody_id, payment_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_payments_data()
                self.load_custody_data()  # لتحديث المبالغ
                self.update_statistics()  # تحديث الإحصائيات
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استيراد حوار الدفعة: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تعديل الدفعة: {str(e)}")

    def delete_payment(self):
        """حذف الدفعة المحددة"""
        current_row = self.payments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة للحذف")
            return

        payment_id = int(self.payments_table.item(current_row, 0).text())
        payment_desc = self.payments_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الدفعة: {payment_desc}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user, password=password,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                cursor.execute("DELETE FROM دفعات_العهد WHERE id = %s", (payment_id,))
                conn.commit()

                QMessageBox.information(self, "نجح", "تم حذف الدفعة بنجاح")
                self.load_payments_data()
                self.load_custody_data()  # لتحديث المبالغ
                self.update_statistics()  # تحديث الإحصائيات

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في حذف الدفعة: {str(e)}")
            finally:
                if 'conn' in locals():
                    conn.close()

    def add_percentage(self):
        """إدراج نسبة المكتب للعهدة المحددة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة لإدراج النسبة")
            return

        try:
            from PySide6.QtWidgets import QInputDialog

            # الحصول على النسبة من المستخدم
            percentage, ok = QInputDialog.getDouble(
                self, "إدراج نسبة المكتب",
                "أدخل نسبة المكتب (%):",
                value=0.0, min=0.0, max=100.0, decimals=2
            )

            if ok:
                custody_id = int(self.custody_table.item(current_row, 0).text())
                custody_amount = float(self.custody_table.item(current_row, 4).text().replace(',', ''))

                # حساب مبلغ النسبة
                percentage_amount = (custody_amount * percentage) / 100
                net_amount = custody_amount - percentage_amount

                # تحديث قاعدة البيانات
                conn = mysql.connector.connect(
                    host=host, user=user, password=password,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                cursor.execute("""
                    UPDATE المقاولات_العهد
                    SET نسبة_المكتب = %s, مبلغ_نسبة_المكتب = %s, المبلغ_الصافي = %s
                    WHERE id = %s
                """, (percentage, percentage_amount, net_amount, custody_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج نسبة المكتب {percentage}% بنجاح")
                self.load_custody_data()
                self.update_statistics()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج النسبة: {str(e)}")

    def add_amount(self):
        pass
        """إدراج مبلغ إضافي للعهدة المحددة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة لإدراج المبلغ")
            return

    def print_expense_voucher(self):
        """طباعة سند صرف للعهدة المحددة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة لطباعة سند الصرف")
            return

        try:
            custody_number = self.custody_table.item(current_row, 1).text()

            # هنا يمكن إضافة كود طباعة سند الصرف
            # يمكن استخدام مكتبة reportlab أو أي مكتبة طباعة أخرى

            QMessageBox.information(self, "طباعة سند صرف",
                                  f"سيتم طباعة سند صرف للعهدة رقم: {custody_number}\n"
                                  "هذه الميزة قيد التطوير")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في طباعة سند الصرف: {str(e)}")

    def print_receipt_voucher(self):
        """طباعة سند قبض للعهدة المحددة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة لطباعة سند القبض")
            return

        try:
            custody_number = self.custody_table.item(current_row, 1).text()
            custody_amount = self.custody_table.item(current_row, 4).text()

            # هنا يمكن إضافة كود طباعة سند القبض
            # يمكن استخدام مكتبة reportlab أو أي مكتبة طباعة أخرى

            QMessageBox.information(self, "طباعة سند قبض",
                                  f"سيتم طباعة سند قبض للعهدة رقم: {custody_number}\n"
                                  f"المبلغ: {custody_amount}\n"
                                  "هذه الميزة قيد التطوير")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في طباعة سند القبض: {str(e)}")

    def apply_report_filter(self):
        """تطبيق فلتر التقارير"""
        # سيتم تنفيذها لاحقاً
        pass

    def print_report(self):
        """طباعة التقرير"""
        # سيتم تنفيذها لاحقاً
        pass
