#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص جميع الـ triggers في قاعدة البيانات
"""

import mysql.connector
from متغيرات import host, user, password

def check_all_triggers():
    """فحص جميع الـ triggers"""
    print("🔍 فحص جميع الـ triggers...")
    
    try:
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        # عرض جميع الـ triggers
        cursor.execute("""
            SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER
            FROM information_schema.TRIGGERS 
            WHERE TRIGGER_SCHEMA = 'project_manager_V2'
            ORDER BY EVENT_OBJECT_TABLE, EVENT_MANIPULATION
        """)
        
        triggers = cursor.fetchall()
        if triggers:
            print("📋 الـ Triggers الموجودة:")
            for trigger in triggers:
                trigger_name, event, table, definer = trigger
                print(f"  - {trigger_name}")
                print(f"    Table: {table}")
                print(f"    Event: {event}")
                print(f"    Definer: {definer}")
                
                # عرض تعريف الـ trigger
                try:
                    cursor.execute(f"SHOW CREATE TRIGGER {trigger_name}")
                    trigger_def = cursor.fetchone()
                    if trigger_def:
                        print(f"    Definition:")
                        print(f"    {trigger_def[2][:200]}...")  # أول 200 حرف
                except Exception as e:
                    print(f"    خطأ في عرض التعريف: {e}")
                print()
        else:
            print("  لا توجد triggers")
        
        conn.close()
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        return False

def drop_problematic_triggers():
    """حذف الـ triggers المشكلة"""
    print("\n🗑️ حذف الـ triggers المشكلة...")
    
    try:
        from for_all import host, user_r, password_r
        
        conn = mysql.connector.connect(
            host=host, 
            user=user_r, 
            password=password_r,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        # قائمة الـ triggers التي قد تسبب مشاكل
        problematic_triggers = [
            'update_project_paid_insert',
            'update_project_paid_update', 
            'update_project_paid_delete',
            'تحديث_التصميم',
            'update_custody_project_info',
            'تحديث_الموظفين_معاملات_مالية',
            'تحديث_التصميم_للموظف',
            'update_employee_balance_insert',
            'update_employee_balance_update',
            'update_employee_balance_delete'
        ]
        
        for trigger_name in problematic_triggers:
            try:
                cursor.execute(f"DROP TRIGGER IF EXISTS {trigger_name}")
                print(f"✅ تم حذف {trigger_name}")
            except mysql.connector.Error as e:
                print(f"⚠️ خطأ في حذف {trigger_name}: {e}")
        
        conn.commit()
        conn.close()
        print("✅ تم حذف الـ triggers المشكلة")
        return True
        
    except Exception as e:
        print(f"❌ فشل في حذف الـ triggers: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("فحص وإصلاح الـ Triggers")
    print("=" * 60)
    
    # فحص الـ triggers الحالية
    check_all_triggers()
    
    # حذف الـ triggers المشكلة
    drop_problematic_triggers()
    
    print("\n" + "=" * 60)
    print("انتهى الفحص والإصلاح")
    print("=" * 60)
