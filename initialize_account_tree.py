#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دالة لتهيئة شجرة الحسابات بالقيم الافتراضية
"""

from datetime import date

# تعريف الأصناف الرئيسية للحسابات
ACCOUNT_TYPES = {
    "أصول": {
        "نوع": "مدين",
        "أصناف_فرعية": {
            "أصول ثابتة": ["أراضي", "مباني", "سيارات", "أثاث", "أجهزة وحواسيب", "معدات"],
            "أصول متداولة": ["نقدية بالصندوق", "نقدية بالبنك", "عملاء", "مدينون", "مخزون"]
        }
    },
    "خصوم": {
        "نوع": "دائن",
        "أصناف_فرعية": {
            "خصوم طويلة الأجل": ["قروض طويلة الأجل", "التزامات طويلة الأجل"],
            "خصوم متداولة": ["موردون", "دائنون", "التزامات قصيرة الأجل"]
        }
    },
    "إيرادات": {
        "نوع": "دائن",
        "أصناف_فرعية": {
            "إيرادات تشغيلية": ["إيرادات المشاريع", "إيرادات التصميم", "إيرادات الإشراف", "إيرادات المقاولات", "إيرادات التدريب"],
            "إيرادات أخرى": ["إيرادات استثمارات", "إيرادات متنوعة"]
        }
    },
    "مصروفات": {
        "نوع": "مدين",
        "أصناف_فرعية": {
            "مصروفات تشغيلية": ["رواتب", "إيجارات", "مستلزمات مكتبية", "صيانة", "مرافق", "وقود ومحروقات"],
            "مصروفات إدارية": ["مصروفات إدارية", "مصروفات تسويق", "مصروفات سفر"],
            "مصروفات مشاريع": ["مواد", "عمالة", "مقاولات", "نقل"]
        }
    }
}

def initialize_account_tree(cursor, year, username="النظام"):
    """
    تهيئة شجرة الحسابات بالقيم الافتراضية
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    year: السنة الحالية
    username: اسم المستخدم الذي يقوم بالتهيئة
    """
    today = date.today().isoformat()
    
    # التحقق من وجود بيانات في شجرة الحسابات
    cursor.execute("SELECT COUNT(*) FROM شجرة_الحسابات")
    count = cursor.fetchone()[0]
    
    if count > 0:
        print("شجرة الحسابات تحتوي بالفعل على بيانات. لن يتم إعادة التهيئة.")
        return
    
    print("بدء تهيئة شجرة الحسابات...")
    
    # إضافة الحسابات الرئيسية
    for i, (account_type, details) in enumerate(ACCOUNT_TYPES.items(), 1):
        account_code = f"{i}"
        cursor.execute("""
            INSERT INTO شجرة_الحسابات 
            (كود_الحساب, اسم_الحساب, نوع_الحساب, المستوى, الحساب_الأب, تاريخ_الإنشاء, المستخدم, السنة) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (account_code, account_type, details["نوع"], 1, None, today, username, year))
        
        # إضافة الأصناف الفرعية
        for j, (sub_type, sub_accounts) in enumerate(details["أصناف_فرعية"].items(), 1):
            sub_code = f"{i}.{j}"
            cursor.execute("""
                INSERT INTO شجرة_الحسابات 
                (كود_الحساب, اسم_الحساب, نوع_الحساب, المستوى, الحساب_الأب, تاريخ_الإنشاء, المستخدم, السنة) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (sub_code, sub_type, details["نوع"], 2, account_code, today, username, year))
            
            # إضافة الحسابات الفرعية
            for k, account_name in enumerate(sub_accounts, 1):
                account_sub_code = f"{i}.{j}.{k}"
                cursor.execute("""
                    INSERT INTO شجرة_الحسابات 
                    (كود_الحساب, اسم_الحساب, نوع_الحساب, المستوى, الحساب_الأب, تاريخ_الإنشاء, المستخدم, السنة) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (account_sub_code, account_name, details["نوع"], 3, sub_code, today, username, year))
    
    print("تم تهيئة شجرة الحسابات بنجاح.")

def get_account_code_by_name(cursor, account_name):
    """
    الحصول على كود الحساب بناءً على اسمه
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    account_name: اسم الحساب
    
    العائد:
    كود الحساب أو None إذا لم يتم العثور على الحساب
    """
    cursor.execute("SELECT كود_الحساب FROM شجرة_الحسابات WHERE اسم_الحساب = %s", (account_name,))
    result = cursor.fetchone()
    return result[0] if result else None

def get_account_code_by_path(cursor, path):
    """
    الحصول على كود الحساب بناءً على المسار (مثال: أصول/أصول متداولة/نقدية بالصندوق)
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    path: مسار الحساب
    
    العائد:
    كود الحساب أو None إذا لم يتم العثور على الحساب
    """
    parts = path.split('/')
    if len(parts) < 1:
        return None
    
    # البحث عن الحساب الرئيسي
    cursor.execute("SELECT كود_الحساب FROM شجرة_الحسابات WHERE اسم_الحساب = %s AND المستوى = 1", (parts[0],))
    result = cursor.fetchone()
    if not result:
        return None
    
    current_code = result[0]
    
    # البحث عن الحسابات الفرعية
    for i in range(1, len(parts)):
        cursor.execute("""
            SELECT كود_الحساب FROM شجرة_الحسابات 
            WHERE اسم_الحساب = %s AND الحساب_الأب = %s
        """, (parts[i], current_code))
        result = cursor.fetchone()
        if not result:
            return None
        current_code = result[0]
    
    return current_code

def update_account_balance(cursor, account_code, amount, is_debit=True):
    """
    تحديث رصيد الحساب
    
    المعلمات:
    cursor: مؤشر قاعدة البيانات
    account_code: كود الحساب
    amount: المبلغ
    is_debit: هل هو مدين (True) أم دائن (False)
    """
    # الحصول على نوع الحساب
    cursor.execute("SELECT نوع_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
    result = cursor.fetchone()
    if not result:
        return
    
    account_type = result[0]
    
    # تحديث الرصيد
    if is_debit:
        if account_type == "مدين":
            # زيادة الرصيد المدين والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_مدين = رصيد_مدين + %s, الرصيد_الحالي = الرصيد_الحالي + %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
        else:
            # نقص الرصيد الدائن والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_مدين = رصيد_مدين + %s, الرصيد_الحالي = الرصيد_الحالي - %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
    else:  # دائن
        if account_type == "دائن":
            # زيادة الرصيد الدائن والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_دائن = رصيد_دائن + %s, الرصيد_الحالي = الرصيد_الحالي + %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
        else:
            # نقص الرصيد المدين والرصيد الحالي
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET رصيد_دائن = رصيد_دائن + %s, الرصيد_الحالي = الرصيد_الحالي - %s
                WHERE كود_الحساب = %s
            """, (amount, amount, account_code))
