#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شجرة الحسابات - نظام محاسبي متكامل
تم تطويره لإدارة الحسابات بطريقة شجرية مترابطة
"""

import sys
import os
import mysql.connector
from datetime import datetime, date
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QTreeWidget, QTreeWidgetItem, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QComboBox, QDateEdit, QMessageBox, QDialog,
    QFormLayout, QTabWidget, QWidget, QSplitter, QFrame, QTableWidget, QTableWidgetItem,
    QHeaderView, QTextEdit, QGroupBox, QRadioButton, QCheckBox, QSpinBox, QDoubleSpinBox,
    Q<PERSON><PERSON><PERSON>, Q<PERSON>oolBar, <PERSON><PERSON><PERSON>usBar, QFileDialog
)
from PySide6.QtCore import Qt, QDate, Signal, Slot, QSize
from PySide6.QtGui import QIcon, QPixmap, QFont, QColor, QPalette, QAction

# استيراد الدوال المساعدة من المشروع الرئيسي
try:
    from functions import *
    from db import *
    from ستايل import *
except ImportError:
    print("تعذر استيراد الوحدات المطلوبة. سيتم استخدام الإعدادات الافتراضية.")
    # لن نخرج من البرنامج في حالة عدم وجود الوحدات، بل سنستخدم الإعدادات الافتراضية

# استيراد ستايل شيت شجرة الحسابات
try:
    from AccountTreeStyleSheet import apply_stylesheet, set_account_style, set_balance_style
    CUSTOM_STYLE_AVAILABLE = True
    print("تم استيراد ستايل شيت شجرة الحسابات بنجاح")
except ImportError:
    CUSTOM_STYLE_AVAILABLE = False
    print("تعذر استيراد ستايل شيت شجرة الحسابات. سيتم استخدام الستايل الافتراضي.")

# تعريف الأصناف الرئيسية للحسابات
ACCOUNT_TYPES = {
    "أصول": {
        "نوع": "مدين",
        "أصناف_فرعية": {
            "أصول ثابتة": ["أراضي", "مباني", "سيارات", "أثاث", "أجهزة وحواسيب", "معدات"],
            "أصول متداولة": ["نقدية بالصندوق", "نقدية بالبنك", "عملاء", "مدينون", "مخزون"]
        }
    },
    "خصوم": {
        "نوع": "دائن",
        "أصناف_فرعية": {
            "خصوم طويلة الأجل": ["قروض طويلة الأجل", "التزامات طويلة الأجل"],
            "خصوم متداولة": ["موردون", "دائنون", "التزامات قصيرة الأجل"]
        }
    },
    "إيرادات": {
        "نوع": "دائن",
        "أصناف_فرعية": {
            "إيرادات تشغيلية": ["إيرادات المشاريع", "إيرادات التصميم", "إيرادات الإشراف", "إيرادات المقاولات", "إيرادات التدريب"],
            "إيرادات أخرى": ["إيرادات استثمارات", "إيرادات متنوعة"]
        }
    },
    "مصروفات": {
        "نوع": "مدين",
        "أصناف_فرعية": {
            "مصروفات تشغيلية": ["رواتب", "إيجارات", "مستلزمات مكتبية", "صيانة", "مرافق", "وقود ومحروقات"],
            "مصروفات إدارية": ["مصروفات إدارية", "مصروفات تسويق", "مصروفات سفر"],
            "مصروفات مشاريع": ["مواد", "عمالة", "مقاولات", "نقل"]
        }
    }
}

# تعريف جدول شجرة الحسابات
ACCOUNT_TREE_TABLE = """
CREATE TABLE IF NOT EXISTS `شجرة_الحسابات` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `كود_الحساب` VARCHAR(20) UNIQUE,
    `اسم_الحساب` VARCHAR(255) NOT NULL,
    `نوع_الحساب` VARCHAR(50) NOT NULL,
    `طبيعة_الحساب` ENUM('مدين', 'دائن') DEFAULT 'مدين',
    `حساب_نهائي` BOOLEAN DEFAULT FALSE,
    `المستوى` INT NOT NULL,
    `الحساب_الأب` VARCHAR(20),
    `الرصيد_الافتتاحي` DECIMAL(15,2) DEFAULT 0,
    `رصيد_مدين` DECIMAL(15,2) DEFAULT 0,
    `رصيد_دائن` DECIMAL(15,2) DEFAULT 0,
    `الرصيد_الحالي` DECIMAL(15,2) DEFAULT 0,
    `ملاحظات` TEXT,
    `تاريخ_الإنشاء` DATE,
    `آخر_تحديث` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_كود_الحساب` (`كود_الحساب`),
    INDEX `idx_الحساب_الأب` (`الحساب_الأب`),
    INDEX `idx_طبيعة_الحساب` (`طبيعة_الحساب`),
    INDEX `idx_حساب_نهائي` (`حساب_نهائي`)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
"""

# تعريف جدول حركات الحسابات
ACCOUNT_MOVEMENTS_TABLE = """
CREATE TABLE IF NOT EXISTS `حركات_الحسابات` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `رقم_القيد` VARCHAR(20) NOT NULL,
    `تاريخ_القيد` DATE NOT NULL,
    `كود_الحساب` VARCHAR(20) NOT NULL,
    `وصف_الحركة` VARCHAR(255) NOT NULL,
    `مدين` DECIMAL(15,2) DEFAULT 0,
    `دائن` DECIMAL(15,2) DEFAULT 0,
    `المرجع` VARCHAR(50),
    `نوع_المستند` VARCHAR(50),
    `رقم_المستند` VARCHAR(50),
    `مركز_التكلفة` VARCHAR(50),
    `ملاحظات` TEXT,
    `حالة_الحركة` ENUM('نشطة', 'ملغية') DEFAULT 'نشطة',
    `المستخدم` VARCHAR(50),
    `السنة` INT,
    `تاريخ_الإنشاء` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_كود_الحساب` (`كود_الحساب`),
    INDEX `idx_تاريخ_القيد` (`تاريخ_القيد`),
    INDEX `idx_رقم_القيد` (`رقم_القيد`),
    INDEX `idx_المستخدم` (`المستخدم`),
    INDEX `idx_السنة` (`السنة`),
    INDEX `idx_حالة_الحركة` (`حالة_الحركة`),
    CONSTRAINT `fk_حركات_الحسابات_كود_الحساب`
    FOREIGN KEY (`كود_الحساب`)
    REFERENCES `شجرة_الحسابات`(`كود_الحساب`)
    ON DELETE RESTRICT
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
"""

# تعريف جدول القيود المحاسبية
JOURNAL_ENTRIES_TABLE = """
CREATE TABLE IF NOT EXISTS `القيود_المحاسبية` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `رقم_القيد` VARCHAR(20) UNIQUE NOT NULL,
    `تاريخ_القيد` DATE NOT NULL,
    `وصف_القيد` VARCHAR(255) NOT NULL,
    `إجمالي_مدين` DECIMAL(15,2) DEFAULT 0,
    `إجمالي_دائن` DECIMAL(15,2) DEFAULT 0,
    `حالة_القيد` ENUM('مسودة', 'معتمد', 'ملغي') DEFAULT 'مسودة',
    `نوع_القيد` VARCHAR(50),
    `المرجع_الخارجي` VARCHAR(50),
    `مركز_التكلفة` VARCHAR(50),
    `ملاحظات` TEXT,
    `المستخدم` VARCHAR(50),
    `السنة` INT,
    `تاريخ_الإنشاء` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `تاريخ_التعديل` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_رقم_القيد` (`رقم_القيد`),
    INDEX `idx_تاريخ_القيد` (`تاريخ_القيد`),
    INDEX `idx_المستخدم` (`المستخدم`),
    INDEX `idx_السنة` (`السنة`)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
"""

# تعريف جدول تفاصيل القيود المحاسبية
JOURNAL_ENTRY_DETAILS_TABLE = """
CREATE TABLE IF NOT EXISTS `تفاصيل_القيود_المحاسبية` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `معرف_القيد` INT NOT NULL,
    `رقم_القيد` VARCHAR(20) NOT NULL,
    `كود_الحساب` VARCHAR(20) NOT NULL,
    `اسم_الحساب` VARCHAR(255),
    `وصف_التفصيل` VARCHAR(255),
    `مدين` DECIMAL(15,2) DEFAULT 0,
    `دائن` DECIMAL(15,2) DEFAULT 0,
    `مركز_التكلفة` VARCHAR(50),
    `ملاحظات` TEXT,
    `ترتيب_السطر` INT DEFAULT 1,
    INDEX `idx_معرف_القيد` (`معرف_القيد`),
    INDEX `idx_كود_الحساب` (`كود_الحساب`),
    INDEX `idx_رقم_القيد` (`رقم_القيد`),
    CONSTRAINT `fk_تفاصيل_القيود_معرف_القيد`
    FOREIGN KEY (`معرف_القيد`)
    REFERENCES `القيود_المحاسبية`(`id`)
    ON DELETE CASCADE,
    CONSTRAINT `fk_تفاصيل_القيود_كود_الحساب`
    FOREIGN KEY (`كود_الحساب`)
    REFERENCES `شجرة_الحسابات`(`كود_الحساب`)
    ON DELETE RESTRICT
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
"""

# تعريف جدول مراكز التكلفة
COST_CENTERS_TABLE = """
CREATE TABLE IF NOT EXISTS `مراكز_التكلفة` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `كود_المركز` VARCHAR(20) UNIQUE NOT NULL,
    `اسم_المركز` VARCHAR(255) NOT NULL,
    `نوع_المركز` VARCHAR(50),
    `المركز_الأب` VARCHAR(20),
    `المستوى` INT DEFAULT 1,
    `وصف_المركز` TEXT,
    `حالة_المركز` ENUM('نشط', 'غير نشط') DEFAULT 'نشط',
    `المستخدم` VARCHAR(50),
    `تاريخ_الإنشاء` DATE,
    `آخر_تحديث` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_كود_المركز` (`كود_المركز`),
    INDEX `idx_المركز_الأب` (`المركز_الأب`)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
"""

class AccountTreeApp(QMainWindow):
    """تطبيق شجرة الحسابات الرئيسي"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("شجرة الحسابات - نظام محاسبي متكامل")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الستايل شيت
        if CUSTOM_STYLE_AVAILABLE:
            apply_stylesheet(self)

        # إنشاء قاعدة البيانات وجداول الحسابات إذا لم تكن موجودة
        self.setup_database()

        # إعداد واجهة المستخدم
        self.setup_ui()

        # تحميل بيانات شجرة الحسابات
        self.load_account_tree()

    def setup_database(self):
        """إنشاء جداول قاعدة البيانات اللازمة إذا لم تكن موجودة"""
        try:
            conn, cursor = con_db()

            if not conn or not cursor:
                QMessageBox.critical(self, "خطأ في الاتصال", "تعذر الاتصال بقاعدة البيانات")
                return

            try:
                # إنشاء جدول شجرة الحسابات
                cursor.execute(ACCOUNT_TREE_TABLE)

                # إنشاء جدول حركات الحسابات
                cursor.execute(ACCOUNT_MOVEMENTS_TABLE)

                # إنشاء جدول القيود المحاسبية
                cursor.execute(JOURNAL_ENTRIES_TABLE)

                # إنشاء جدول تفاصيل القيود المحاسبية
                cursor.execute(JOURNAL_ENTRY_DETAILS_TABLE)

                # إنشاء جدول مراكز التكلفة
                cursor.execute(COST_CENTERS_TABLE)

                # تحديث الجداول الموجودة بالأعمدة الجديدة
                self.update_existing_tables(cursor)

                # التحقق من وجود بيانات في شجرة الحسابات
                cursor.execute("SELECT COUNT(*) FROM شجرة_الحسابات")
                count = cursor.fetchone()[0]

                # إذا كانت الشجرة فارغة، قم بإنشاء الحسابات الرئيسية
                if count == 0:
                    print("تهيئة شجرة الحسابات...")
                    self.initialize_account_tree(cursor)
                    print("تم تهيئة شجرة الحسابات بنجاح")

                # التحقق من وجود مراكز التكلفة وإنشاؤها إذا لم تكن موجودة
                cursor.execute("SELECT COUNT(*) FROM مراكز_التكلفة")
                cost_centers_count = cursor.fetchone()[0]

                if cost_centers_count == 0:
                    print("تهيئة مراكز التكلفة...")
                    self.initialize_cost_centers(cursor)
                    print("تم تهيئة مراكز التكلفة بنجاح")

                conn.commit()
                print("تم إعداد قاعدة البيانات بنجاح")

            except Exception as e:
                conn.rollback()
                QMessageBox.critical(self, "خطأ في قاعدة البيانات", f"حدث خطأ أثناء إعداد جداول قاعدة البيانات: {str(e)}")
                print(f"خطأ في إعداد قاعدة البيانات: {str(e)}")
            finally:
                cursor.close()
                conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في قاعدة البيانات", f"حدث خطأ أثناء إعداد قاعدة البيانات: {str(e)}")
            print(f"خطأ في إعداد قاعدة البيانات: {str(e)}")

    def update_existing_tables(self, cursor):
        """تحديث الجداول الموجودة بإضافة الأعمدة الجديدة"""
        try:
            # التحقق من وجود الأعمدة الجديدة في جدول شجرة الحسابات
            cursor.execute("SHOW COLUMNS FROM شجرة_الحسابات")
            existing_columns = [row[0] for row in cursor.fetchall()]

            # إضافة عمود طبيعة الحساب إذا لم يكن موجوداً
            if 'طبيعة_الحساب' not in existing_columns:
                cursor.execute("""
                    ALTER TABLE شجرة_الحسابات
                    ADD COLUMN طبيعة_الحساب ENUM('مدين', 'دائن') DEFAULT 'مدين'
                    AFTER نوع_الحساب
                """)
                print("تم إضافة عمود طبيعة_الحساب")

            # إضافة عمود حساب نهائي إذا لم يكن موجوداً
            if 'حساب_نهائي' not in existing_columns:
                cursor.execute("""
                    ALTER TABLE شجرة_الحسابات
                    ADD COLUMN حساب_نهائي BOOLEAN DEFAULT FALSE
                    AFTER طبيعة_الحساب
                """)
                print("تم إضافة عمود حساب_نهائي")

            # التحقق من وجود الأعمدة الجديدة في جدول حركات الحسابات
            cursor.execute("SHOW COLUMNS FROM حركات_الحسابات")
            movements_columns = [row[0] for row in cursor.fetchall()]

            # إضافة عمود حالة الحركة إذا لم يكن موجوداً
            if 'حالة_الحركة' not in movements_columns:
                cursor.execute("""
                    ALTER TABLE حركات_الحسابات
                    ADD COLUMN حالة_الحركة ENUM('نشطة', 'ملغية') DEFAULT 'نشطة'
                    AFTER ملاحظات
                """)
                print("تم إضافة عمود حالة_الحركة")

            # تحديث القيم الافتراضية للحسابات الموجودة
            self.update_account_properties(cursor)

        except Exception as e:
            print(f"خطأ في تحديث الجداول: {e}")

    def update_account_properties(self, cursor):
        """تحديث خصائص الحسابات الموجودة"""
        try:
            # تحديد طبيعة الحسابات حسب النوع
            account_nature_map = {
                'أصول': 'مدين',
                'خصوم': 'دائن',
                'إيرادات': 'دائن',
                'مصروفات': 'مدين',
                'حقوق الملكية': 'دائن'
            }

            # تحديث طبيعة الحسابات
            for account_type, nature in account_nature_map.items():
                cursor.execute("""
                    UPDATE شجرة_الحسابات
                    SET طبيعة_الحساب = %s
                    WHERE نوع_الحساب = %s
                """, (nature, account_type))

            # تحديد الحسابات النهائية (المستوى الثالث أو الحسابات التي ليس لها أطفال)
            cursor.execute("""
                UPDATE شجرة_الحسابات
                SET حساب_نهائي = TRUE
                WHERE المستوى >= 3
                OR كود_الحساب NOT IN (
                    SELECT DISTINCT الحساب_الأب
                    FROM (SELECT الحساب_الأب FROM شجرة_الحسابات WHERE الحساب_الأب IS NOT NULL) AS temp
                )
            """)

            print("تم تحديث خصائص الحسابات")

        except Exception as e:
            print(f"خطأ في تحديث خصائص الحسابات: {e}")

    def initialize_account_tree(self, cursor):
        """إنشاء الحسابات الرئيسية في شجرة الحسابات"""
        today = date.today().isoformat()

        # تحديد طبيعة الحسابات حسب النوع
        account_nature_map = {
            'أصول': 'مدين',
            'خصوم': 'دائن',
            'إيرادات': 'دائن',
            'مصروفات': 'مدين',
            'حقوق الملكية': 'دائن'
        }

        # إضافة الحسابات الرئيسية
        for i, (account_type, details) in enumerate(ACCOUNT_TYPES.items(), 1):
            account_code = f"{i}"
            nature = account_nature_map.get(details["نوع"], 'مدين')
            cursor.execute("""
                INSERT INTO شجرة_الحسابات
                (كود_الحساب, اسم_الحساب, نوع_الحساب, طبيعة_الحساب, حساب_نهائي, المستوى, الحساب_الأب, تاريخ_الإنشاء)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (account_code, account_type, details["نوع"], nature, False, 1, None, today))

            # إضافة الأصناف الفرعية
            for j, (sub_type, sub_accounts) in enumerate(details["أصناف_فرعية"].items(), 1):
                sub_code = f"{i}.{j}"
                cursor.execute("""
                    INSERT INTO شجرة_الحسابات
                    (كود_الحساب, اسم_الحساب, نوع_الحساب, طبيعة_الحساب, حساب_نهائي, المستوى, الحساب_الأب, تاريخ_الإنشاء)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (sub_code, sub_type, details["نوع"], nature, False, 2, account_code, today))

                # إضافة الحسابات الفرعية (الحسابات النهائية)
                for k, account_name in enumerate(sub_accounts, 1):
                    account_sub_code = f"{i}.{j}.{k}"
                    cursor.execute("""
                        INSERT INTO شجرة_الحسابات
                        (كود_الحساب, اسم_الحساب, نوع_الحساب, طبيعة_الحساب, حساب_نهائي, المستوى, الحساب_الأب, تاريخ_الإنشاء)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (account_sub_code, account_name, details["نوع"], nature, True, 3, sub_code, today))

    def initialize_cost_centers(self, cursor):
        """إنشاء مراكز التكلفة الافتراضية"""
        today = date.today().isoformat()

        cost_centers = [
            ("CC001", "المشاريع الهندسية", "مشاريع", None, 1),
            ("CC001.01", "مشاريع التصميم", "مشاريع", "CC001", 2),
            ("CC001.02", "مشاريع الإشراف", "مشاريع", "CC001", 2),
            ("CC001.03", "مشاريع المقاولات", "مشاريع", "CC001", 2),
            ("CC002", "التدريب", "تدريب", None, 1),
            ("CC002.01", "الدورات التقنية", "تدريب", "CC002", 2),
            ("CC002.02", "الدورات الإدارية", "تدريب", "CC002", 2),
            ("CC003", "الإدارة العامة", "إدارة", None, 1),
            ("CC003.01", "الموارد البشرية", "إدارة", "CC003", 2),
            ("CC003.02", "المالية والمحاسبة", "إدارة", "CC003", 2),
            ("CC003.03", "التسويق والمبيعات", "إدارة", "CC003", 2),
        ]

        for code, name, type_center, parent, level in cost_centers:
            cursor.execute("""
                INSERT INTO مراكز_التكلفة
                (كود_المركز, اسم_المركز, نوع_المركز, المركز_الأب, المستوى, المستخدم, تاريخ_الإنشاء)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (code, name, type_center, parent, level, "النظام", today))

    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إنشاء الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)

        # إنشاء مقسم لتقسيم الواجهة
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # الجزء الأيمن - شجرة الحسابات
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # عنوان شجرة الحسابات
        tree_title = QLabel("شجرة الحسابات")
        tree_title.setAlignment(Qt.AlignCenter)
        tree_title.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        right_layout.addWidget(tree_title)

        # شجرة الحسابات
        self.account_tree = QTreeWidget()
        self.account_tree.setHeaderLabels(["الحساب", "الكود", "النوع", "الرصيد"])
        self.account_tree.setColumnWidth(0, 250)
        self.account_tree.setColumnWidth(1, 100)
        self.account_tree.setColumnWidth(2, 100)
        self.account_tree.setColumnWidth(3, 150)
        self.account_tree.itemClicked.connect(self.on_account_selected)
        right_layout.addWidget(self.account_tree)

        # أزرار التحكم بالشجرة
        tree_buttons_layout = QHBoxLayout()

        self.add_account_btn = QPushButton("إضافة حساب")
        self.add_account_btn.clicked.connect(self.add_account)
        tree_buttons_layout.addWidget(self.add_account_btn)

        self.edit_account_btn = QPushButton("تعديل حساب")
        self.edit_account_btn.clicked.connect(self.edit_account)
        tree_buttons_layout.addWidget(self.edit_account_btn)

        self.delete_account_btn = QPushButton("حذف حساب")
        self.delete_account_btn.clicked.connect(self.delete_account)
        tree_buttons_layout.addWidget(self.delete_account_btn)

        right_layout.addLayout(tree_buttons_layout)

        # الجزء الأيسر - تفاصيل الحساب والعمليات
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # تبويبات العمليات
        self.tabs = QTabWidget()

        # تبويب تفاصيل الحساب
        account_details_tab = QWidget()
        account_details_layout = QVBoxLayout(account_details_tab)

        # عنوان تفاصيل الحساب
        details_title = QLabel("تفاصيل الحساب")
        details_title.setAlignment(Qt.AlignCenter)
        details_title.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        account_details_layout.addWidget(details_title)

        # نموذج تفاصيل الحساب
        details_form = QFormLayout()

        self.account_name_label = QLabel("")
        details_form.addRow("اسم الحساب:", self.account_name_label)

        self.account_code_label = QLabel("")
        details_form.addRow("كود الحساب:", self.account_code_label)

        self.account_type_label = QLabel("")
        details_form.addRow("نوع الحساب:", self.account_type_label)

        self.account_level_label = QLabel("")
        details_form.addRow("المستوى:", self.account_level_label)

        self.account_parent_label = QLabel("")
        details_form.addRow("الحساب الأب:", self.account_parent_label)

        self.account_balance_label = QLabel("")
        details_form.addRow("الرصيد الحالي:", self.account_balance_label)

        account_details_layout.addLayout(details_form)

        # تبويب حركات الحساب
        movements_tab = QWidget()
        movements_layout = QVBoxLayout(movements_tab)

        # عنوان حركات الحساب
        movements_title = QLabel("حركات الحساب")
        movements_title.setAlignment(Qt.AlignCenter)
        movements_title.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        movements_layout.addWidget(movements_title)

        # جدول حركات الحساب
        self.movements_table = QTableWidget()
        self.movements_table.setColumnCount(7)
        self.movements_table.setHorizontalHeaderLabels(["تاريخ القيد", "رقم القيد", "الوصف", "مدين", "دائن", "الرصيد", "المرجع"])
        self.movements_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        movements_layout.addWidget(self.movements_table)

        # أزرار التحكم بالحركات
        movements_buttons_layout = QHBoxLayout()

        self.add_movement_btn = QPushButton("إضافة حركة")
        self.add_movement_btn.clicked.connect(self.add_movement)
        movements_buttons_layout.addWidget(self.add_movement_btn)

        self.print_movements_btn = QPushButton("طباعة كشف حساب")
        self.print_movements_btn.clicked.connect(self.print_account_statement)
        movements_buttons_layout.addWidget(self.print_movements_btn)

        movements_layout.addLayout(movements_buttons_layout)

        # تبويب القيود المحاسبية
        entries_tab = QWidget()
        entries_layout = QVBoxLayout(entries_tab)

        # عنوان القيود المحاسبية
        entries_title = QLabel("القيود المحاسبية")
        entries_title.setAlignment(Qt.AlignCenter)
        entries_title.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        entries_layout.addWidget(entries_title)

        # أزرار التحكم بالقيود
        entries_buttons_layout = QHBoxLayout()

        self.add_entry_btn = QPushButton("إضافة قيد محاسبي")
        self.add_entry_btn.clicked.connect(self.add_accounting_entry)
        entries_buttons_layout.addWidget(self.add_entry_btn)

        self.view_entries_btn = QPushButton("عرض دفتر اليومية")
        self.view_entries_btn.clicked.connect(self.view_journal)
        entries_buttons_layout.addWidget(self.view_entries_btn)

        entries_layout.addLayout(entries_buttons_layout)

        # إضافة التبويبات
        self.tabs.addTab(account_details_tab, "تفاصيل الحساب")
        self.tabs.addTab(movements_tab, "حركات الحساب")
        self.tabs.addTab(entries_tab, "القيود المحاسبية")

        left_layout.addWidget(self.tabs)

        # إضافة الأجزاء إلى المقسم
        splitter.addWidget(right_widget)
        splitter.addWidget(left_widget)

        # ضبط نسب المقسم
        splitter.setSizes([400, 800])

        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز")

    def load_account_tree(self):
        """تحميل بيانات شجرة الحسابات من قاعدة البيانات"""
        try:
            self.account_tree.clear()
            conn, cursor = con_db()

            if not conn or not cursor:
                QMessageBox.critical(self, "خطأ في الاتصال", "تعذر الاتصال بقاعدة البيانات")
                self.status_bar.showMessage("تعذر تحميل شجرة الحسابات")
                return

            try:
                # جلب الحسابات من المستوى الأول
                cursor.execute("""
                    SELECT كود_الحساب, اسم_الحساب, نوع_الحساب, الرصيد_الحالي
                    FROM شجرة_الحسابات
                    WHERE المستوى = 1
                    ORDER BY كود_الحساب
                """)
                level1_accounts = cursor.fetchall()

                # إنشاء عناصر المستوى الأول
                for account in level1_accounts:
                    code, name, account_type, balance = account
                    item = QTreeWidgetItem(self.account_tree)
                    item.setText(0, name)
                    item.setText(1, code)
                    item.setText(2, account_type)
                    item.setText(3, f"{balance:,.2f}" if balance is not None else "0.00")
                    item.setData(0, Qt.UserRole, code)

                    # تحميل الحسابات الفرعية
                    self.load_sub_accounts(cursor, item, code)

                # توسيع الشجرة
                self.account_tree.expandAll()
                self.status_bar.showMessage("تم تحميل شجرة الحسابات بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل شجرة الحسابات: {str(e)}")
                self.status_bar.showMessage("تعذر تحميل شجرة الحسابات")
                print(f"خطأ في تحميل شجرة الحسابات: {str(e)}")
            finally:
                cursor.close()
                conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل شجرة الحسابات: {str(e)}")
            self.status_bar.showMessage("تعذر تحميل شجرة الحسابات")
            print(f"خطأ في تحميل شجرة الحسابات: {str(e)}")

    def load_sub_accounts(self, cursor, parent_item, parent_code):
        """تحميل الحسابات الفرعية لحساب معين"""
        cursor.execute("""
            SELECT كود_الحساب, اسم_الحساب, نوع_الحساب, الرصيد_الحالي
            FROM شجرة_الحسابات
            WHERE الحساب_الأب = %s
            ORDER BY كود_الحساب
        """, (parent_code,))
        sub_accounts = cursor.fetchall()

        for account in sub_accounts:
            code, name, account_type, balance = account
            item = QTreeWidgetItem(parent_item)
            item.setText(0, name)
            item.setText(1, code)
            item.setText(2, account_type)
            item.setText(3, f"{balance:,.2f}")
            item.setData(0, Qt.UserRole, code)

            # تحميل الحسابات الفرعية بشكل متكرر
            self.load_sub_accounts(cursor, item, code)

    def on_account_selected(self, item, column):
        """معالجة حدث اختيار حساب من الشجرة"""
        account_code = item.data(0, Qt.UserRole)
        self.load_account_details(account_code)
        self.load_account_movements(account_code)
        self.status_bar.showMessage(f"تم تحديد الحساب: {item.text(0)} ({account_code})")

    def load_account_details(self, account_code):
        """تحميل تفاصيل الحساب المحدد"""
        try:
            conn, cursor = con_db()

            cursor.execute("""
                SELECT اسم_الحساب, كود_الحساب, نوع_الحساب, المستوى, الحساب_الأب, الرصيد_الحالي
                FROM شجرة_الحسابات
                WHERE كود_الحساب = %s
            """, (account_code,))

            account = cursor.fetchone()
            if account:
                name, code, account_type, level, parent, balance = account

                self.account_name_label.setText(name)
                self.account_code_label.setText(code)
                self.account_type_label.setText(account_type)
                self.account_level_label.setText(str(level))

                # جلب اسم الحساب الأب إذا كان موجوداً
                if parent:
                    cursor.execute("SELECT اسم_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (parent,))
                    parent_name = cursor.fetchone()[0]
                    self.account_parent_label.setText(f"{parent_name} ({parent})")
                else:
                    self.account_parent_label.setText("لا يوجد")

                # عرض الرصيد وتطبيق الستايل المناسب
                balance_value = balance if balance is not None else 0
                self.account_balance_label.setText(f"{balance_value:,.2f}")

                # تطبيق الستايل على نوع الحساب والرصيد
                if CUSTOM_STYLE_AVAILABLE:
                    # تعيين خاصية account للتمييز في الستايل شيت
                    self.account_type_label.setProperty("account", "true")
                    set_account_style(self.account_type_label, account_type)

                    # تعيين خاصية balance للتمييز في الستايل شيت
                    self.account_balance_label.setProperty("balance", "true")
                    set_balance_style(self.account_balance_label, balance_value)

                    # تطبيق التغييرات على الستايل
                    self.account_type_label.style().unpolish(self.account_type_label)
                    self.account_type_label.style().polish(self.account_type_label)
                    self.account_balance_label.style().unpolish(self.account_balance_label)
                    self.account_balance_label.style().polish(self.account_balance_label)

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل تفاصيل الحساب: {str(e)}")

    def load_account_movements(self, account_code):
        """تحميل حركات الحساب المحدد"""
        try:
            conn, cursor = con_db()

            cursor.execute("""
                SELECT تاريخ_القيد, رقم_القيد, وصف_الحركة, مدين, دائن, المرجع
                FROM حركات_الحسابات
                WHERE كود_الحساب = %s
                ORDER BY تاريخ_القيد, id
            """, (account_code,))

            movements = cursor.fetchall()

            self.movements_table.setRowCount(len(movements))

            balance = 0
            for row, movement in enumerate(movements):
                date_val, entry_no, description, debit, credit, reference = movement

                # حساب الرصيد التراكمي
                if debit:
                    if self.account_type_label.text() == "مدين":
                        balance += debit
                    else:
                        balance -= debit

                if credit:
                    if self.account_type_label.text() == "دائن":
                        balance += credit
                    else:
                        balance -= credit

                # إضافة البيانات إلى الجدول
                date_item = QTableWidgetItem(date_val.strftime("%Y-%m-%d"))
                self.movements_table.setItem(row, 0, date_item)

                entry_item = QTableWidgetItem(entry_no)
                self.movements_table.setItem(row, 1, entry_item)

                desc_item = QTableWidgetItem(description)
                self.movements_table.setItem(row, 2, desc_item)

                debit_item = QTableWidgetItem(f"{debit:,.2f}" if debit else "")
                self.movements_table.setItem(row, 3, debit_item)

                credit_item = QTableWidgetItem(f"{credit:,.2f}" if credit else "")
                self.movements_table.setItem(row, 4, credit_item)

                balance_item = QTableWidgetItem(f"{balance:,.2f}")
                self.movements_table.setItem(row, 5, balance_item)

                ref_item = QTableWidgetItem(reference or "")
                self.movements_table.setItem(row, 6, ref_item)

                # تطبيق الستايل على الخلايا
                if CUSTOM_STYLE_AVAILABLE:
                    # تمييز الخلايا المدينة والدائنة
                    if debit:
                        debit_item.setForeground(QColor("#e74c3c"))  # لون أحمر للمدين
                        debit_item.setFont(QFont("", -1, QFont.Bold))

                    if credit:
                        credit_item.setForeground(QColor("#2ecc71"))  # لون أخضر للدائن
                        credit_item.setFont(QFont("", -1, QFont.Bold))

                    # تمييز الرصيد
                    if balance >= 0:
                        balance_item.setForeground(QColor("#2ecc71"))  # لون أخضر للرصيد الموجب
                    else:
                        balance_item.setForeground(QColor("#e74c3c"))  # لون أحمر للرصيد السالب

                    balance_item.setFont(QFont("", -1, QFont.Bold))

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل حركات الحساب: {str(e)}")

    def add_account(self):
        """إضافة حساب جديد"""
        dialog = AccountDialog(self, "إضافة حساب جديد")
        if dialog.exec_():
            self.load_account_tree()
            self.status_bar.showMessage("تم إضافة الحساب بنجاح")

    def edit_account(self):
        """تعديل الحساب المحدد"""
        selected_items = self.account_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد حساب للتعديل")
            return

        account_code = selected_items[0].data(0, Qt.UserRole)
        dialog = AccountDialog(self, "تعديل حساب", account_code)
        if dialog.exec_():
            self.load_account_tree()
            self.load_account_details(account_code)
            self.status_bar.showMessage("تم تعديل الحساب بنجاح")

    def delete_account(self):
        """حذف الحساب المحدد"""
        selected_items = self.account_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد حساب للحذف")
            return

        account_code = selected_items[0].data(0, Qt.UserRole)
        account_name = selected_items[0].text(0)

        # التحقق من وجود حسابات فرعية
        conn, cursor = con_db()
        cursor.execute("SELECT COUNT(*) FROM شجرة_الحسابات WHERE الحساب_الأب = %s", (account_code,))
        sub_accounts_count = cursor.fetchone()[0]

        if sub_accounts_count > 0:
            QMessageBox.warning(self, "تحذير", f"لا يمكن حذف الحساب '{account_name}' لأنه يحتوي على {sub_accounts_count} حساب فرعي")
            cursor.close()
            conn.close()
            return

        # التحقق من وجود حركات للحساب
        cursor.execute("SELECT COUNT(*) FROM حركات_الحسابات WHERE كود_الحساب = %s", (account_code,))
        movements_count = cursor.fetchone()[0]

        if movements_count > 0:
            QMessageBox.warning(self, "تحذير", f"لا يمكن حذف الحساب '{account_name}' لأنه يحتوي على {movements_count} حركة")
            cursor.close()
            conn.close()
            return

        # تأكيد الحذف
        reply = QMessageBox.question(self, "تأكيد الحذف", f"هل أنت متأكد من حذف الحساب '{account_name}'؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                cursor.execute("DELETE FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
                conn.commit()
                self.load_account_tree()
                self.status_bar.showMessage(f"تم حذف الحساب '{account_name}' بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف الحساب: {str(e)}")

        cursor.close()
        conn.close()

    def add_movement(self):
        """إضافة حركة جديدة للحساب المحدد"""
        selected_items = self.account_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد حساب لإضافة حركة")
            return

        account_code = selected_items[0].data(0, Qt.UserRole)
        account_name = selected_items[0].text(0)

        dialog = MovementDialog(self, account_code, account_name)
        if dialog.exec_():
            self.load_account_movements(account_code)
            self.load_account_details(account_code)
            self.load_account_tree()  # لتحديث الأرصدة في الشجرة
            self.status_bar.showMessage("تم إضافة الحركة بنجاح")

    def print_account_statement(self):
        """طباعة كشف حساب للحساب المحدد"""
        selected_items = self.account_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد حساب لطباعة كشف الحساب")
            return

        account_code = selected_items[0].data(0, Qt.UserRole)
        account_name = selected_items[0].text(0)

        # هنا يمكن إضافة كود لطباعة كشف الحساب
        QMessageBox.information(self, "طباعة كشف حساب", f"سيتم طباعة كشف حساب لـ {account_name} ({account_code})")

    def add_accounting_entry(self):
        """إضافة قيد محاسبي جديد"""
        dialog = AccountingEntryDialog(self)
        if dialog.exec_():
            # تحديث البيانات بعد إضافة القيد
            selected_items = self.account_tree.selectedItems()
            if selected_items:
                account_code = selected_items[0].data(0, Qt.UserRole)
                self.load_account_movements(account_code)
                self.load_account_details(account_code)

            self.load_account_tree()  # لتحديث الأرصدة في الشجرة
            self.status_bar.showMessage("تم إضافة القيد المحاسبي بنجاح")

    def view_journal(self):
        """عرض دفتر اليومية"""
        dialog = JournalDialog(self)
        dialog.exec_()


class AccountDialog(QDialog):
    """حوار إضافة أو تعديل حساب"""

    def __init__(self, parent=None, title="إضافة حساب", account_code=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setGeometry(300, 300, 500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.account_code = account_code

        # تطبيق الستايل شيت
        if CUSTOM_STYLE_AVAILABLE:
            apply_stylesheet(self)

        self.setup_ui()

        if account_code:
            self.load_account_data()

    def setup_ui(self):
        """إعداد واجهة الحوار"""
        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # اسم الحساب
        self.name_edit = QLineEdit()
        form_layout.addRow("اسم الحساب:", self.name_edit)

        # كود الحساب
        self.code_edit = QLineEdit()
        if self.account_code:  # في حالة التعديل، لا يمكن تغيير الكود
            self.code_edit.setText(self.account_code)
            self.code_edit.setReadOnly(True)
        form_layout.addRow("كود الحساب:", self.code_edit)

        # نوع الحساب
        self.type_combo = QComboBox()
        self.type_combo.addItems(["مدين", "دائن"])
        form_layout.addRow("نوع الحساب:", self.type_combo)

        # الحساب الأب
        self.parent_combo = QComboBox()
        self.load_parent_accounts()
        form_layout.addRow("الحساب الأب:", self.parent_combo)

        # الرصيد الافتتاحي
        self.opening_balance_spin = QDoubleSpinBox()
        self.opening_balance_spin.setRange(0, **********)
        self.opening_balance_spin.setDecimals(2)
        self.opening_balance_spin.setSingleStep(100)
        form_layout.addRow("الرصيد الافتتاحي:", self.opening_balance_spin)

        # ملاحظات
        self.notes_edit = QTextEdit()
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_account)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def load_parent_accounts(self):
        """تحميل قائمة الحسابات الأب المحتملة"""
        try:
            conn, cursor = con_db()

            # إضافة خيار "لا يوجد" للحسابات من المستوى الأول
            self.parent_combo.addItem("لا يوجد", None)

            # جلب الحسابات من المستويين الأول والثاني فقط (لا يمكن إضافة حساب تحت المستوى الثالث)
            cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب, المستوى
                FROM شجرة_الحسابات
                WHERE المستوى < 3
                ORDER BY كود_الحساب
            """)

            accounts = cursor.fetchall()
            for account in accounts:
                code, name, level = account
                # تجاهل الحساب الحالي في حالة التعديل
                if code != self.account_code:
                    self.parent_combo.addItem(f"{name} ({code})", code)

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل قائمة الحسابات: {str(e)}")

    def load_account_data(self):
        """تحميل بيانات الحساب للتعديل"""
        try:
            conn, cursor = con_db()

            cursor.execute("""
                SELECT اسم_الحساب, نوع_الحساب, الحساب_الأب, الرصيد_الافتتاحي, ملاحظات
                FROM شجرة_الحسابات
                WHERE كود_الحساب = %s
            """, (self.account_code,))

            account = cursor.fetchone()
            if account:
                name, account_type, parent, opening_balance, notes = account

                self.name_edit.setText(name)
                self.type_combo.setCurrentText(account_type)

                # تحديد الحساب الأب في القائمة المنسدلة
                if parent:
                    for i in range(self.parent_combo.count()):
                        if self.parent_combo.itemData(i) == parent:
                            self.parent_combo.setCurrentIndex(i)
                            break
                else:
                    self.parent_combo.setCurrentIndex(0)  # "لا يوجد"

                self.opening_balance_spin.setValue(float(opening_balance) if opening_balance else 0)
                self.notes_edit.setText(notes or "")

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات الحساب: {str(e)}")

    def save_account(self):
        """حفظ بيانات الحساب"""
        name = self.name_edit.text().strip()
        code = self.code_edit.text().strip()
        account_type = self.type_combo.currentText()
        parent_code = self.parent_combo.currentData()
        opening_balance = self.opening_balance_spin.value()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not name:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال اسم الحساب")
            return

        if not code:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال كود الحساب")
            return

        try:
            conn, cursor = con_db()

            # تحديد مستوى الحساب بناءً على الحساب الأب
            level = 1  # المستوى الافتراضي
            if parent_code:
                cursor.execute("SELECT المستوى FROM شجرة_الحسابات WHERE كود_الحساب = %s", (parent_code,))
                parent_level = cursor.fetchone()[0]
                level = parent_level + 1

            today = date.today().isoformat()

            if self.account_code:  # تعديل حساب موجود
                cursor.execute("""
                    UPDATE شجرة_الحسابات
                    SET اسم_الحساب = %s, نوع_الحساب = %s, المستوى = %s, الحساب_الأب = %s,
                        الرصيد_الافتتاحي = %s, ملاحظات = %s
                    WHERE كود_الحساب = %s
                """, (name, account_type, level, parent_code, opening_balance, notes, code))
            else:  # إضافة حساب جديد
                # التحقق من عدم وجود حساب بنفس الكود
                cursor.execute("SELECT COUNT(*) FROM شجرة_الحسابات WHERE كود_الحساب = %s", (code,))
                if cursor.fetchone()[0] > 0:
                    QMessageBox.warning(self, "تحذير", f"يوجد حساب بالكود {code} بالفعل")
                    cursor.close()
                    conn.close()
                    return

                cursor.execute("""
                    INSERT INTO شجرة_الحسابات
                    (كود_الحساب, اسم_الحساب, نوع_الحساب, المستوى, الحساب_الأب, الرصيد_الافتتاحي,
                     رصيد_مدين, رصيد_دائن, الرصيد_الحالي, ملاحظات, تاريخ_الإنشاء)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    code, name, account_type, level, parent_code, opening_balance,
                    opening_balance if account_type == "مدين" else 0,
                    opening_balance if account_type == "دائن" else 0,
                    opening_balance, notes, today
                ))

            conn.commit()
            cursor.close()
            conn.close()

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في حفظ البيانات", f"حدث خطأ أثناء حفظ بيانات الحساب: {str(e)}")


class MovementDialog(QDialog):
    """حوار إضافة حركة جديدة لحساب"""

    def __init__(self, parent=None, account_code=None, account_name=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة حركة جديدة")
        self.setGeometry(300, 300, 500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.account_code = account_code
        self.account_name = account_name

        # التحقق من وجود البيانات المطلوبة
        if not account_code or not account_name:
            QMessageBox.critical(self, "خطأ", "لم يتم تحديد الحساب بشكل صحيح")
            self.reject()
            return

        # تطبيق الستايل شيت
        if CUSTOM_STYLE_AVAILABLE:
            apply_stylesheet(self)

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة الحوار"""
        layout = QVBoxLayout(self)

        # عنوان الحساب
        account_label = QLabel(f"إضافة حركة لحساب: {self.account_name} ({self.account_code})")
        account_label.setAlignment(Qt.AlignCenter)
        account_label.setStyleSheet("font-size: 14pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(account_label)

        # نموذج البيانات
        form_layout = QFormLayout()

        # تاريخ الحركة
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("تاريخ الحركة:", self.date_edit)

        # رقم القيد
        self.entry_no_edit = QLineEdit()
        form_layout.addRow("رقم القيد:", self.entry_no_edit)

        # وصف الحركة
        self.description_edit = QLineEdit()
        form_layout.addRow("وصف الحركة:", self.description_edit)

        # نوع الحركة
        self.movement_type_group = QGroupBox("نوع الحركة")
        movement_type_layout = QHBoxLayout()

        self.debit_radio = QRadioButton("مدين")
        self.debit_radio.setChecked(True)
        movement_type_layout.addWidget(self.debit_radio)

        self.credit_radio = QRadioButton("دائن")
        movement_type_layout.addWidget(self.credit_radio)

        self.movement_type_group.setLayout(movement_type_layout)
        form_layout.addRow("", self.movement_type_group)

        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, **********)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSingleStep(100)
        form_layout.addRow("المبلغ:", self.amount_spin)

        # المرجع
        self.reference_edit = QLineEdit()
        form_layout.addRow("المرجع:", self.reference_edit)

        # نوع المستند
        self.document_type_combo = QComboBox()
        self.document_type_combo.addItems(["فاتورة", "إيصال", "شيك", "تحويل بنكي", "أخرى"])
        form_layout.addRow("نوع المستند:", self.document_type_combo)

        # رقم المستند
        self.document_no_edit = QLineEdit()
        form_layout.addRow("رقم المستند:", self.document_no_edit)

        # ملاحظات
        self.notes_edit = QTextEdit()
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_movement)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def save_movement(self):
        """حفظ بيانات الحركة"""
        date_val = self.date_edit.date().toString("yyyy-MM-dd")
        entry_no = self.entry_no_edit.text().strip()
        description = self.description_edit.text().strip()
        amount = self.amount_spin.value()
        reference = self.reference_edit.text().strip()
        document_type = self.document_type_combo.currentText()
        document_no = self.document_no_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not entry_no:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال رقم القيد")
            return

        if not description:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال وصف الحركة")
            return

        if amount <= 0:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال مبلغ أكبر من صفر")
            return

        try:
            conn, cursor = con_db()

            # تحديد نوع الحركة (مدين/دائن)
            debit_amount = amount if self.debit_radio.isChecked() else 0
            credit_amount = amount if self.credit_radio.isChecked() else 0

            # إضافة الحركة إلى جدول حركات الحسابات
            cursor.execute("""
                INSERT INTO حركات_الحسابات
                (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن, المرجع, نوع_المستند, رقم_المستند, ملاحظات)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                entry_no, date_val, self.account_code, description, debit_amount, credit_amount,
                reference, document_type, document_no, notes
            ))

            # تحديث رصيد الحساب
            cursor.execute("SELECT نوع_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (self.account_code,))
            account_type = cursor.fetchone()[0]

            if account_type == "مدين":
                if self.debit_radio.isChecked():
                    # زيادة الرصيد المدين والرصيد الحالي
                    cursor.execute("""
                        UPDATE شجرة_الحسابات
                        SET رصيد_مدين = رصيد_مدين + %s, الرصيد_الحالي = الرصيد_الحالي + %s
                        WHERE كود_الحساب = %s
                    """, (amount, amount, self.account_code))
                else:
                    # نقص الرصيد المدين والرصيد الحالي
                    cursor.execute("""
                        UPDATE شجرة_الحسابات
                        SET رصيد_دائن = رصيد_دائن + %s, الرصيد_الحالي = الرصيد_الحالي - %s
                        WHERE كود_الحساب = %s
                    """, (amount, amount, self.account_code))
            else:  # دائن
                if self.credit_radio.isChecked():
                    # زيادة الرصيد الدائن والرصيد الحالي
                    cursor.execute("""
                        UPDATE شجرة_الحسابات
                        SET رصيد_دائن = رصيد_دائن + %s, الرصيد_الحالي = الرصيد_الحالي + %s
                        WHERE كود_الحساب = %s
                    """, (amount, amount, self.account_code))
                else:
                    # نقص الرصيد الدائن والرصيد الحالي
                    cursor.execute("""
                        UPDATE شجرة_الحسابات
                        SET رصيد_مدين = رصيد_مدين + %s, الرصيد_الحالي = الرصيد_الحالي - %s
                        WHERE كود_الحساب = %s
                    """, (amount, amount, self.account_code))

            conn.commit()
            cursor.close()
            conn.close()

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في حفظ البيانات", f"حدث خطأ أثناء حفظ بيانات الحركة: {str(e)}")


class AccountingEntryDialog(QDialog):
    """حوار إضافة قيد محاسبي جديد"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة قيد محاسبي جديد")
        self.setGeometry(300, 300, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الستايل شيت
        if CUSTOM_STYLE_AVAILABLE:
            apply_stylesheet(self)

        # تهيئة قائمة الحسابات قبل إعداد واجهة المستخدم
        self.accounts = []
        self.load_accounts()
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة الحوار"""
        layout = QVBoxLayout(self)

        # عنوان القيد
        entry_title = QLabel("إضافة قيد محاسبي جديد")
        entry_title.setAlignment(Qt.AlignCenter)
        entry_title.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(entry_title)

        # معلومات القيد
        entry_info_layout = QHBoxLayout()

        # تاريخ القيد
        date_layout = QFormLayout()
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        date_layout.addRow("تاريخ القيد:", self.date_edit)
        entry_info_layout.addLayout(date_layout)

        # رقم القيد
        entry_no_layout = QFormLayout()
        self.entry_no_edit = QLineEdit()
        entry_no_layout.addRow("رقم القيد:", self.entry_no_edit)
        entry_info_layout.addLayout(entry_no_layout)

        # وصف القيد
        description_layout = QFormLayout()
        self.description_edit = QLineEdit()
        description_layout.addRow("وصف القيد:", self.description_edit)
        entry_info_layout.addLayout(description_layout)

        layout.addLayout(entry_info_layout)

        # جدول القيد
        self.entry_table = QTableWidget()
        self.entry_table.setColumnCount(5)
        self.entry_table.setHorizontalHeaderLabels(["الحساب", "الكود", "البيان", "مدين", "دائن"])
        self.entry_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.entry_table)

        # أزرار التحكم بالجدول
        table_buttons_layout = QHBoxLayout()

        self.add_row_btn = QPushButton("إضافة سطر")
        self.add_row_btn.clicked.connect(self.add_table_row)
        table_buttons_layout.addWidget(self.add_row_btn)

        self.remove_row_btn = QPushButton("حذف سطر")
        self.remove_row_btn.clicked.connect(self.remove_table_row)
        table_buttons_layout.addWidget(self.remove_row_btn)

        layout.addLayout(table_buttons_layout)

        # إجماليات القيد
        totals_layout = QHBoxLayout()

        self.total_debit_label = QLabel("إجمالي المدين: 0.00")
        totals_layout.addWidget(self.total_debit_label)

        self.total_credit_label = QLabel("إجمالي الدائن: 0.00")
        totals_layout.addWidget(self.total_credit_label)

        self.balance_label = QLabel("الفرق: 0.00")
        totals_layout.addWidget(self.balance_label)

        layout.addLayout(totals_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("حفظ القيد")
        self.save_btn.clicked.connect(self.save_entry)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

        # إضافة صفين افتراضيين
        self.add_table_row()
        self.add_table_row()

    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        try:
            conn, cursor = con_db()

            cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب
                FROM شجرة_الحسابات
                ORDER BY كود_الحساب
            """)

            self.accounts = cursor.fetchall()

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل قائمة الحسابات: {str(e)}")

    def add_table_row(self):
        """إضافة سطر جديد إلى جدول القيد"""
        row = self.entry_table.rowCount()
        self.entry_table.insertRow(row)

        # إنشاء قائمة منسدلة للحسابات
        account_combo = QComboBox()
        for account in self.accounts:
            code, name = account
            account_combo.addItem(f"{name} ({code})", code)

        self.entry_table.setCellWidget(row, 0, account_combo)

        # عند تغيير الحساب، تحديث كود الحساب
        account_combo.currentIndexChanged.connect(lambda: self.update_account_code(row))

        # إضافة كود الحساب
        code_item = QTableWidgetItem("")
        code_item.setFlags(code_item.flags() & ~Qt.ItemIsEditable)  # جعل الخلية غير قابلة للتعديل
        self.entry_table.setItem(row, 1, code_item)

        # تحديث كود الحساب للقيمة الافتراضية
        self.update_account_code(row)

        # إضافة خلايا البيان والمدين والدائن
        self.entry_table.setItem(row, 2, QTableWidgetItem(""))

        debit_item = QTableWidgetItem("0.00")
        self.entry_table.setItem(row, 3, debit_item)

        credit_item = QTableWidgetItem("0.00")
        self.entry_table.setItem(row, 4, credit_item)

        # ربط تغيير قيم المدين والدائن بتحديث الإجماليات
        self.entry_table.itemChanged.connect(self.update_totals)

    def remove_table_row(self):
        """حذف السطر المحدد من جدول القيد"""
        selected_rows = self.entry_table.selectedIndexes()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد سطر للحذف")
            return

        row = selected_rows[0].row()
        self.entry_table.removeRow(row)
        self.update_totals()

    def update_account_code(self, row):
        """تحديث كود الحساب عند تغيير الحساب"""
        account_combo = self.entry_table.cellWidget(row, 0)
        code = account_combo.currentData()

        code_item = self.entry_table.item(row, 1)
        if code_item:
            code_item.setText(code)

    def update_totals(self):
        """تحديث إجماليات القيد"""
        total_debit = 0
        total_credit = 0

        for row in range(self.entry_table.rowCount()):
            debit_item = self.entry_table.item(row, 3)
            credit_item = self.entry_table.item(row, 4)

            if debit_item and debit_item.text():
                try:
                    total_debit += float(debit_item.text())
                except ValueError:
                    pass

            if credit_item and credit_item.text():
                try:
                    total_credit += float(credit_item.text())
                except ValueError:
                    pass

        self.total_debit_label.setText(f"إجمالي المدين: {total_debit:,.2f}")
        self.total_credit_label.setText(f"إجمالي الدائن: {total_credit:,.2f}")
        self.balance_label.setText(f"الفرق: {total_debit - total_credit:,.2f}")

        # تغيير لون الفرق إذا كان غير متوازن
        if total_debit != total_credit:
            self.balance_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.balance_label.setStyleSheet("color: green; font-weight: bold;")

    def save_entry(self):
        """حفظ القيد المحاسبي"""
        date_val = self.date_edit.date().toString("yyyy-MM-dd")
        entry_no = self.entry_no_edit.text().strip()
        description = self.description_edit.text().strip()

        # التحقق من صحة البيانات
        if not entry_no:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال رقم القيد")
            return

        if not description:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال وصف القيد")
            return

        # التحقق من توازن القيد
        total_debit = 0
        total_credit = 0

        for row in range(self.entry_table.rowCount()):
            debit_item = self.entry_table.item(row, 3)
            credit_item = self.entry_table.item(row, 4)

            if debit_item and debit_item.text():
                try:
                    total_debit += float(debit_item.text())
                except ValueError:
                    pass

            if credit_item and credit_item.text():
                try:
                    total_credit += float(credit_item.text())
                except ValueError:
                    pass

        if total_debit != total_credit:
            QMessageBox.warning(self, "تحذير", "القيد غير متوازن. يجب أن يتساوى إجمالي المدين مع إجمالي الدائن")
            return

        try:
            conn, cursor = con_db()

            # إضافة حركات القيد
            for row in range(self.entry_table.rowCount()):
                account_code = self.entry_table.item(row, 1).text()
                statement = self.entry_table.item(row, 2).text()

                debit_text = self.entry_table.item(row, 3).text()
                credit_text = self.entry_table.item(row, 4).text()

                debit = float(debit_text) if debit_text else 0
                credit = float(credit_text) if credit_text else 0

                if debit == 0 and credit == 0:
                    continue  # تجاهل الصفوف الفارغة

                # إضافة الحركة إلى جدول حركات الحسابات
                cursor.execute("""
                    INSERT INTO حركات_الحسابات
                    (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (entry_no, date_val, account_code, statement or description, debit, credit))

                # تحديث رصيد الحساب
                cursor.execute("SELECT نوع_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
                account_type = cursor.fetchone()[0]

                if account_type == "مدين":
                    # تحديث الأرصدة
                    cursor.execute("""
                        UPDATE شجرة_الحسابات
                        SET رصيد_مدين = رصيد_مدين + %s,
                            رصيد_دائن = رصيد_دائن + %s,
                            الرصيد_الحالي = الرصيد_الحالي + %s - %s
                        WHERE كود_الحساب = %s
                    """, (debit, credit, debit, credit, account_code))
                else:  # دائن
                    # تحديث الأرصدة
                    cursor.execute("""
                        UPDATE شجرة_الحسابات
                        SET رصيد_مدين = رصيد_مدين + %s,
                            رصيد_دائن = رصيد_دائن + %s,
                            الرصيد_الحالي = الرصيد_الحالي + %s - %s
                        WHERE كود_الحساب = %s
                    """, (debit, credit, credit, debit, account_code))

            conn.commit()
            cursor.close()
            conn.close()

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في حفظ البيانات", f"حدث خطأ أثناء حفظ القيد المحاسبي: {str(e)}")


class JournalDialog(QDialog):
    """حوار عرض دفتر اليومية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("دفتر اليومية")
        self.setGeometry(100, 100, 1000, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الستايل شيت
        if CUSTOM_STYLE_AVAILABLE:
            apply_stylesheet(self)

        self.setup_ui()

        # تأخير تحميل البيانات حتى يتم إنشاء واجهة المستخدم بالكامل
        QApplication.processEvents()
        try:
            self.load_journal()
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"تعذر تحميل بيانات دفتر اليومية: {str(e)}")

    def setup_ui(self):
        """إعداد واجهة الحوار"""
        layout = QVBoxLayout(self)

        # عنوان الدفتر
        journal_title = QLabel("دفتر اليومية")
        journal_title.setAlignment(Qt.AlignCenter)
        journal_title.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(journal_title)

        # فلاتر البحث
        filters_layout = QHBoxLayout()

        # فلتر التاريخ
        date_filter_layout = QHBoxLayout()
        date_filter_layout.addWidget(QLabel("من:"))
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setCalendarPopup(True)
        self.from_date_edit.setDate(QDate.currentDate().addMonths(-1))
        date_filter_layout.addWidget(self.from_date_edit)

        date_filter_layout.addWidget(QLabel("إلى:"))
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setCalendarPopup(True)
        self.to_date_edit.setDate(QDate.currentDate())
        date_filter_layout.addWidget(self.to_date_edit)

        filters_layout.addLayout(date_filter_layout)

        # فلتر رقم القيد
        entry_no_filter_layout = QHBoxLayout()
        entry_no_filter_layout.addWidget(QLabel("رقم القيد:"))
        self.entry_no_edit = QLineEdit()
        entry_no_filter_layout.addWidget(self.entry_no_edit)

        filters_layout.addLayout(entry_no_filter_layout)

        # زر البحث
        self.search_btn = QPushButton("بحث")
        self.search_btn.clicked.connect(self.load_journal)
        filters_layout.addWidget(self.search_btn)

        layout.addLayout(filters_layout)

        # جدول دفتر اليومية
        self.journal_table = QTableWidget()
        self.journal_table.setColumnCount(7)
        self.journal_table.setHorizontalHeaderLabels(["تاريخ القيد", "رقم القيد", "الحساب", "البيان", "مدين", "دائن", "المرجع"])
        self.journal_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.journal_table)

        # إجماليات الدفتر
        totals_layout = QHBoxLayout()

        self.total_debit_label = QLabel("إجمالي المدين: 0.00")
        totals_layout.addWidget(self.total_debit_label)

        self.total_credit_label = QLabel("إجمالي الدائن: 0.00")
        totals_layout.addWidget(self.total_credit_label)

        layout.addLayout(totals_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.print_btn = QPushButton("طباعة")
        self.print_btn.clicked.connect(self.print_journal)
        buttons_layout.addWidget(self.print_btn)

        self.close_btn = QPushButton("إغلاق")
        self.close_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.close_btn)

        layout.addLayout(buttons_layout)

    def load_journal(self):
        """تحميل بيانات دفتر اليومية"""
        try:
            from_date = self.from_date_edit.date().toString("yyyy-MM-dd")
            to_date = self.to_date_edit.date().toString("yyyy-MM-dd")
            entry_no = self.entry_no_edit.text().strip()

            conn, cursor = con_db()

            # بناء استعلام البحث
            query = """
                SELECT ح.تاريخ_القيد, ح.رقم_القيد, ش.اسم_الحساب, ح.وصف_الحركة, ح.مدين, ح.دائن, ح.المرجع
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ح.تاريخ_القيد BETWEEN %s AND %s
            """
            params = [from_date, to_date]

            if entry_no:
                query += " AND ح.رقم_القيد = %s"
                params.append(entry_no)

            query += " ORDER BY ح.تاريخ_القيد, ح.رقم_القيد, ح.id"

            cursor.execute(query, params)
            movements = cursor.fetchall()

            self.journal_table.setRowCount(len(movements))

            total_debit = 0
            total_credit = 0

            for row, movement in enumerate(movements):
                date_val, entry_no, account_name, description, debit, credit, reference = movement

                self.journal_table.setItem(row, 0, QTableWidgetItem(date_val.strftime("%Y-%m-%d")))
                self.journal_table.setItem(row, 1, QTableWidgetItem(entry_no))
                self.journal_table.setItem(row, 2, QTableWidgetItem(account_name))
                self.journal_table.setItem(row, 3, QTableWidgetItem(description))
                self.journal_table.setItem(row, 4, QTableWidgetItem(f"{debit:,.2f}" if debit else ""))
                self.journal_table.setItem(row, 5, QTableWidgetItem(f"{credit:,.2f}" if credit else ""))
                self.journal_table.setItem(row, 6, QTableWidgetItem(reference or ""))

                total_debit += debit or 0
                total_credit += credit or 0

            self.total_debit_label.setText(f"إجمالي المدين: {total_debit:,.2f}")
            self.total_credit_label.setText(f"إجمالي الدائن: {total_credit:,.2f}")

            cursor.close()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات دفتر اليومية: {str(e)}")

    def print_journal(self):
        """طباعة دفتر اليومية"""
        # هنا يمكن إضافة كود لطباعة دفتر اليومية
        QMessageBox.information(self, "طباعة دفتر اليومية", "سيتم طباعة دفتر اليومية")


# تعريف دالة الاتصال بقاعدة البيانات
def con_db():
    """دالة الاتصال بقاعدة البيانات"""
    try:
        # استخدام قاعدة البيانات الحالية
        from db import get_db_connection
        return get_db_connection()
    except ImportError:
        # في حالة عدم وجود الدالة، إنشاء اتصال مباشر
        try:
            import mysql.connector
            conn = mysql.connector.connect(
                host="localhost",
                user="pme",
                password="kh123456",
                database=f"project_manager_v2"
            )
            cursor = conn.cursor()
            return conn, cursor
        except Exception as e:
            QMessageBox.critical(None, "خطأ في الاتصال بقاعدة البيانات", f"حدث خطأ أثناء الاتصال بقاعدة البيانات: {str(e)}")
            return None, None


# تشغيل التطبيق
if __name__ == "__main__":
    try:
        print("بدء تشغيل التطبيق...")
        app = QApplication(sys.argv)
        print("تم إنشاء التطبيق")
        window = AccountTreeApp()
        print("تم إنشاء النافذة الرئيسية")
        window.show()
        print("تم عرض النافذة")
        sys.exit(app.exec_())
    except Exception as e:
        print(f"حدث خطأ أثناء تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
