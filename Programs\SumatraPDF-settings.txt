﻿# For documentation, see https://www.sumatrapdfreader.org/settings/settings3-5-1.html
Theme = Light
FixedPageUI [
	TextColor = #000000
	BackgroundColor = #ffffff
	SelectionColor = #f5fc0c
	WindowMargin = 2 4 2 4
	PageSpacing = 4 4
	InvertColors = false
	HideScrollbars = false
]
ComicBookUI [
	WindowMargin = 0 0 0 0
	PageSpacing = 4 4
	CbxMangaMode = false
]
ChmUI [
	UseFixedPageUI = false
]

SelectionHandlers [
]
ExternalViewers [
]

ZoomLevels = 8.33 12.5 18 25 33.33 50 66.67 75 100 125 150 200 300 400 600 800 1000 1200 1600 2000 2400 3200 4800 6400
ZoomIncrement = 0

PrinterDefaults [
	PrintScale = shrink
]
ForwardSearch [
	HighlightOffset = 0
	HighlightWidth = 15
	HighlightColor = #6581ff
	HighlightPermanent = false
]
Annotations [
	HighlightColor = #ffff00
	UnderlineColor = #849c79
	SquigglyColor = #ff00ff
	StrikeOutColor = #ff0000
	FreeTextColor = 
	FreeTextSize = 12
	FreeTextBorderWidth = 1
	TextIconColor = 
	TextIconType = 
	DefaultAuthor = 
]

RememberOpenedFiles = false
RememberStatePerDocument = true
RestoreSession = true
UiLanguage = ar
EnableTeXEnhancements = false
DefaultDisplayMode = single page
DefaultZoom = fit page
Shortcuts [
]
EscToExit = false
ReuseInstance = false
ReloadModifiedDocuments = true

MainWindowBackground = #80fff200
FullPathInTitle = false
ShowMenubar = true
ShowToolbar = true
ShowFavorites = false
ShowToc = false
NoHomeTab = false
TocDy = 0
SidebarDx = 0
ToolbarSize = 18
TabWidth = 300
TreeFontSize = 0
TreeFontWeightOffset = 0
TreeFontName = automatic
SmoothScroll = false
ShowStartPage = true
CheckForUpdates = false
WindowState = 1
WindowPos = 313 57 1429 929
UseTabs = true
UseSysColors = false
CustomScreenDPI = 0

FileStates [
]
SessionData [
]
TimeOfLastUpdateCheck = 0 0
OpenCountWeek = 751

# Settings below are not recognized by the current version
