#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء triggers بسيطة وصحيحة لدفعات المشروع
"""

import mysql.connector
from for_all import host, user_r, password_r

def create_simple_triggers():
    """إنشاء triggers بسيطة وصحيحة"""
    print("🔨 إنشاء triggers بسيطة وصحيحة...")
    
    try:
        conn = mysql.connector.connect(
            host=host, 
            user=user_r, 
            password=password_r,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال كـ root")
        
        # Trigger بسيط للـ INSERT - يحدث المدفوع فقط
        insert_trigger = """
        CREATE TRIGGER update_project_paid_insert
        AFTER INSERT ON المشاريع_المدفوعات
        FOR EACH ROW
        BEGIN
            UPDATE المشاريع 
            SET المدفوع = (
                SELECT COALESCE(SUM(المبلغ_المدفوع), 0) 
                FROM المشاريع_المدفوعات 
                WHERE معرف_المشروع = NEW.معرف_المشروع
            )
            WHERE id = NEW.معرف_المشروع;
        END
        """
        
        # Trigger بسيط للـ UPDATE
        update_trigger = """
        CREATE TRIGGER update_project_paid_update
        AFTER UPDATE ON المشاريع_المدفوعات
        FOR EACH ROW
        BEGIN
            -- تحديث المشروع الجديد
            UPDATE المشاريع 
            SET المدفوع = (
                SELECT COALESCE(SUM(المبلغ_المدفوع), 0) 
                FROM المشاريع_المدفوعات 
                WHERE معرف_المشروع = NEW.معرف_المشروع
            )
            WHERE id = NEW.معرف_المشروع;
            
            -- إذا تم تغيير معرف_المشروع، تحديث المشروع القديم أيضاً
            IF OLD.معرف_المشروع != NEW.معرف_المشروع THEN
                UPDATE المشاريع 
                SET المدفوع = (
                    SELECT COALESCE(SUM(المبلغ_المدفوع), 0) 
                    FROM المشاريع_المدفوعات 
                    WHERE معرف_المشروع = OLD.معرف_المشروع
                )
                WHERE id = OLD.معرف_المشروع;
            END IF;
        END
        """
        
        # Trigger بسيط للـ DELETE
        delete_trigger = """
        CREATE TRIGGER update_project_paid_delete
        AFTER DELETE ON المشاريع_المدفوعات
        FOR EACH ROW
        BEGIN
            UPDATE المشاريع 
            SET المدفوع = (
                SELECT COALESCE(SUM(المبلغ_المدفوع), 0) 
                FROM المشاريع_المدفوعات 
                WHERE معرف_المشروع = OLD.معرف_المشروع
            )
            WHERE id = OLD.معرف_المشروع;
        END
        """
        
        # تنفيذ الـ triggers
        try:
            cursor.execute(insert_trigger)
            print("✅ تم إنشاء update_project_paid_insert")
        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء insert trigger: {e}")
        
        try:
            cursor.execute(update_trigger)
            print("✅ تم إنشاء update_project_paid_update")
        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء update trigger: {e}")
        
        try:
            cursor.execute(delete_trigger)
            print("✅ تم إنشاء update_project_paid_delete")
        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء delete trigger: {e}")
        
        conn.commit()
        conn.close()
        print("✅ تم حفظ التغييرات وإغلاق الاتصال")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("إنشاء Triggers بسيطة وصحيحة لدفعات المشروع")
    print("=" * 60)
    
    success = create_simple_triggers()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ تم إنشاء الـ triggers بنجاح")
    else:
        print("❌ فشل في إنشاء الـ triggers")
    print("=" * 60)
