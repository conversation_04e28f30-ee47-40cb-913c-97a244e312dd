#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مكون اختيار الحساب من شجرة الحسابات
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QLineEdit,
    QTreeWidget, QTreeWidgetItem, QDialog, QHeaderView, QMessageBox
)
from PySide6.QtCore import Qt, Signal

class AccountSelector(QWidget):
    """
    مكون اختيار الحساب من شجرة الحسابات
    """
    
    # إشارة تُرسل عند اختيار حساب
    accountSelected = Signal(str, str)  # كود الحساب، اسم الحساب
    
    def __init__(self, parent=None, cursor=None):
        super().__init__(parent)
        self.cursor = cursor
        self.selected_account_code = None
        self.selected_account_name = None
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # حقل عرض الحساب المختار
        self.account_edit = QLineEdit()
        self.account_edit.setReadOnly(True)
        self.account_edit.setPlaceholderText("اختر حساب من شجرة الحسابات")
        layout.addWidget(self.account_edit)
        
        # زر اختيار الحساب
        self.select_btn = QPushButton("...")
        self.select_btn.setMaximumWidth(30)
        self.select_btn.clicked.connect(self.show_account_selector)
        layout.addWidget(self.select_btn)
        
        # زر مسح الاختيار
        self.clear_btn = QPushButton("×")
        self.clear_btn.setMaximumWidth(30)
        self.clear_btn.clicked.connect(self.clear_selection)
        layout.addWidget(self.clear_btn)
    
    def show_account_selector(self):
        """عرض نافذة اختيار الحساب"""
        if not self.cursor:
            QMessageBox.warning(self, "تحذير", "لم يتم توفير اتصال بقاعدة البيانات")
            return
        
        dialog = AccountSelectorDialog(self, self.cursor)
        if dialog.exec_():
            self.selected_account_code = dialog.selected_account_code
            self.selected_account_name = dialog.selected_account_name
            self.account_edit.setText(f"{self.selected_account_name} ({self.selected_account_code})")
            self.accountSelected.emit(self.selected_account_code, self.selected_account_name)
    
    def clear_selection(self):
        """مسح الحساب المختار"""
        self.selected_account_code = None
        self.selected_account_name = None
        self.account_edit.clear()
        self.accountSelected.emit("", "")
    
    def get_selected_account(self):
        """الحصول على الحساب المختار"""
        return self.selected_account_code, self.selected_account_name
    
    def set_selected_account(self, account_code, account_name=None):
        """تعيين الحساب المختار"""
        self.selected_account_code = account_code
        
        if account_name:
            self.selected_account_name = account_name
            self.account_edit.setText(f"{account_name} ({account_code})")
        elif account_code and self.cursor:
            # جلب اسم الحساب من قاعدة البيانات
            try:
                self.cursor.execute("SELECT اسم_الحساب FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
                result = self.cursor.fetchone()
                if result:
                    self.selected_account_name = result[0]
                    self.account_edit.setText(f"{self.selected_account_name} ({account_code})")
            except Exception as e:
                print(f"خطأ في جلب اسم الحساب: {str(e)}")
        else:
            self.account_edit.setText(account_code)


class AccountSelectorDialog(QDialog):
    """
    نافذة اختيار الحساب من شجرة الحسابات
    """
    
    def __init__(self, parent=None, cursor=None):
        super().__init__(parent)
        self.cursor = cursor
        self.selected_account_code = None
        self.selected_account_name = None
        self.setup_ui()
        self.load_account_tree()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختيار حساب من شجرة الحسابات")
        self.setGeometry(300, 300, 600, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel("اختر حساب من شجرة الحسابات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 14pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # حقل البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_layout.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("اكتب اسم أو كود الحساب للبحث")
        self.search_edit.textChanged.connect(self.filter_accounts)
        search_layout.addWidget(self.search_edit)
        
        layout.addLayout(search_layout)
        
        # شجرة الحسابات
        self.account_tree = QTreeWidget()
        self.account_tree.setHeaderLabels(["الحساب", "الكود", "النوع", "الرصيد"])
        self.account_tree.setColumnWidth(0, 250)
        self.account_tree.setColumnWidth(1, 100)
        self.account_tree.setColumnWidth(2, 100)
        self.account_tree.setColumnWidth(3, 150)
        self.account_tree.itemDoubleClicked.connect(self.on_account_selected)
        layout.addWidget(self.account_tree)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.select_btn = QPushButton("اختيار")
        self.select_btn.clicked.connect(self.accept_selection)
        buttons_layout.addWidget(self.select_btn)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_account_tree(self):
        """تحميل بيانات شجرة الحسابات من قاعدة البيانات"""
        if not self.cursor:
            QMessageBox.warning(self, "تحذير", "لم يتم توفير اتصال بقاعدة البيانات")
            return
        
        try:
            self.account_tree.clear()
            
            # جلب الحسابات من المستوى الأول
            self.cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب, نوع_الحساب, الرصيد_الحالي
                FROM شجرة_الحسابات
                WHERE المستوى = 1
                ORDER BY كود_الحساب
            """)
            level1_accounts = self.cursor.fetchall()
            
            # إنشاء عناصر المستوى الأول
            for account in level1_accounts:
                code, name, account_type, balance = account
                item = QTreeWidgetItem(self.account_tree)
                item.setText(0, name)
                item.setText(1, code)
                item.setText(2, account_type)
                item.setText(3, f"{balance:,.2f}" if balance is not None else "0.00")
                item.setData(0, Qt.UserRole, code)
                
                # تحميل الحسابات الفرعية
                self.load_sub_accounts(item, code)
            
            # توسيع الشجرة
            self.account_tree.expandAll()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل شجرة الحسابات: {str(e)}")
    
    def load_sub_accounts(self, parent_item, parent_code):
        """تحميل الحسابات الفرعية لحساب معين"""
        self.cursor.execute("""
            SELECT كود_الحساب, اسم_الحساب, نوع_الحساب, الرصيد_الحالي
            FROM شجرة_الحسابات
            WHERE الحساب_الأب = %s
            ORDER BY كود_الحساب
        """, (parent_code,))
        sub_accounts = self.cursor.fetchall()
        
        for account in sub_accounts:
            code, name, account_type, balance = account
            item = QTreeWidgetItem(parent_item)
            item.setText(0, name)
            item.setText(1, code)
            item.setText(2, account_type)
            item.setText(3, f"{balance:,.2f}" if balance is not None else "0.00")
            item.setData(0, Qt.UserRole, code)
            
            # تحميل الحسابات الفرعية بشكل متكرر
            self.load_sub_accounts(item, code)
    
    def filter_accounts(self):
        """تصفية الحسابات بناءً على نص البحث"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            # إذا كان حقل البحث فارغًا، أظهر جميع العناصر
            for i in range(self.account_tree.topLevelItemCount()):
                self.show_all_items(self.account_tree.topLevelItem(i))
            return
        
        # إخفاء جميع العناصر أولاً
        for i in range(self.account_tree.topLevelItemCount()):
            self.hide_all_items(self.account_tree.topLevelItem(i))
        
        # البحث وإظهار العناصر المطابقة
        items_found = self.account_tree.findItems(search_text, Qt.MatchContains | Qt.MatchRecursive, 0)
        items_found.extend(self.account_tree.findItems(search_text, Qt.MatchContains | Qt.MatchRecursive, 1))
        
        for item in items_found:
            self.show_item_and_parents(item)
    
    def show_all_items(self, item):
        """إظهار جميع العناصر"""
        item.setHidden(False)
        for i in range(item.childCount()):
            self.show_all_items(item.child(i))
    
    def hide_all_items(self, item):
        """إخفاء جميع العناصر"""
        item.setHidden(True)
        for i in range(item.childCount()):
            self.hide_all_items(item.child(i))
    
    def show_item_and_parents(self, item):
        """إظهار العنصر وجميع العناصر الأب"""
        item.setHidden(False)
        parent = item.parent()
        while parent:
            parent.setHidden(False)
            parent = parent.parent()
    
    def on_account_selected(self, item, column):
        """معالجة حدث اختيار حساب من الشجرة"""
        self.selected_account_code = item.data(0, Qt.UserRole)
        self.selected_account_name = item.text(0)
        self.accept()
    
    def accept_selection(self):
        """قبول الحساب المختار"""
        selected_items = self.account_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد حساب")
            return
        
        self.selected_account_code = selected_items[0].data(0, Qt.UserRole)
        self.selected_account_name = selected_items[0].text(0)
        self.accept()
