#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تقارير النظام المحاسبي المتقدمة
"""

import sys
import os
from datetime import datetime, date
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QPushButton, QLabel, 
    QLineEdit, QDateEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QFileDialog,
    QTabWidget, QWidget, QGroupBox, QCheckBox, QProgressBar
)
from PySide6.QtCore import Qt, QDate, QThread, QTimer
from PySide6.QtGui import QFont
from PySide6.QtPrintSupport import QPrinter, QPrintDialog
import mysql.connector

# استيراد الدوال المساعدة
try:
    from functions import *
    from db import *
    from ستايل import *
    from accounting_integration import AccountingIntegration
except ImportError:
    print("تعذر استيراد الوحدات المطلوبة")

class AccountingReportsDialog(QDialog):
    """حوار التقارير المحاسبية المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.accounting = AccountingIntegration()
        
        self.setup_dialog()
        self.create_ui()
        self.load_initial_data()
    
    def setup_dialog(self):
        """إعداد الحوار"""
        self.setWindowTitle("التقارير المحاسبية المتقدمة")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()
        
        # فترة التقرير
        period_group = QGroupBox("فترة التقرير")
        period_layout = QFormLayout(period_group)
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        period_layout.addRow("من تاريخ:", self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        period_layout.addRow("إلى تاريخ:", self.end_date_edit)
        
        toolbar_layout.addWidget(period_group)
        
        # خيارات التقرير
        options_group = QGroupBox("خيارات التقرير")
        options_layout = QVBoxLayout(options_group)
        
        self.include_approved_only = QCheckBox("القيود المعتمدة فقط")
        self.include_approved_only.setChecked(True)
        options_layout.addWidget(self.include_approved_only)
        
        self.group_by_account = QCheckBox("تجميع حسب الحساب")
        self.group_by_account.setChecked(True)
        options_layout.addWidget(self.group_by_account)
        
        self.show_zero_balances = QCheckBox("إظهار الأرصدة الصفرية")
        options_layout.addWidget(self.show_zero_balances)
        
        toolbar_layout.addWidget(options_group)
        
        # أزرار الإجراءات
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QVBoxLayout(actions_group)
        
        self.generate_btn = QPushButton("إنشاء التقرير")
        self.generate_btn.clicked.connect(self.generate_reports)
        actions_layout.addWidget(self.generate_btn)
        
        self.export_btn = QPushButton("تصدير إلى Excel")
        self.export_btn.clicked.connect(self.export_to_excel)
        self.export_btn.setEnabled(False)
        actions_layout.addWidget(self.export_btn)
        
        self.print_btn = QPushButton("طباعة")
        self.print_btn.clicked.connect(self.print_report)
        self.print_btn.setEnabled(False)
        actions_layout.addWidget(self.print_btn)
        
        toolbar_layout.addWidget(actions_group)
        
        layout.addLayout(toolbar_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # تبويبات التقارير
        self.tabs = QTabWidget()
        
        # تبويب ميزان المراجعة
        self.trial_balance_tab = QWidget()
        self.create_trial_balance_tab()
        self.tabs.addTab(self.trial_balance_tab, "ميزان المراجعة")
        
        # تبويب قائمة الدخل
        self.income_statement_tab = QWidget()
        self.create_income_statement_tab()
        self.tabs.addTab(self.income_statement_tab, "قائمة الدخل")
        
        # تبويب الميزانية العمومية
        self.balance_sheet_tab = QWidget()
        self.create_balance_sheet_tab()
        self.tabs.addTab(self.balance_sheet_tab, "الميزانية العمومية")
        
        # تبويب تقرير التدفقات النقدية
        self.cash_flow_tab = QWidget()
        self.create_cash_flow_tab()
        self.tabs.addTab(self.cash_flow_tab, "التدفقات النقدية")
        
        # تبويب تقرير القيود
        self.journal_entries_tab = QWidget()
        self.create_journal_entries_tab()
        self.tabs.addTab(self.journal_entries_tab, "تقرير القيود")
        
        layout.addWidget(self.tabs)
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
    
    def create_trial_balance_tab(self):
        """إنشاء تبويب ميزان المراجعة"""
        layout = QVBoxLayout(self.trial_balance_tab)
        
        # جدول ميزان المراجعة
        self.trial_balance_table = QTableWidget()
        self.trial_balance_table.setColumnCount(6)
        self.trial_balance_table.setHorizontalHeaderLabels([
            "كود الحساب", "اسم الحساب", "رصيد مدين", "رصيد دائن", 
            "حركة مدين", "حركة دائن"
        ])
        self.trial_balance_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.trial_balance_table)
        
        # إجماليات ميزان المراجعة
        totals_layout = QHBoxLayout()
        
        self.trial_balance_debit_total = QLabel("إجمالي المدين: 0.00")
        self.trial_balance_debit_total.setStyleSheet("font-weight: bold; color: green;")
        totals_layout.addWidget(self.trial_balance_debit_total)
        
        self.trial_balance_credit_total = QLabel("إجمالي الدائن: 0.00")
        self.trial_balance_credit_total.setStyleSheet("font-weight: bold; color: red;")
        totals_layout.addWidget(self.trial_balance_credit_total)
        
        layout.addLayout(totals_layout)
    
    def create_income_statement_tab(self):
        """إنشاء تبويب قائمة الدخل"""
        layout = QVBoxLayout(self.income_statement_tab)
        
        # جدول قائمة الدخل
        self.income_statement_table = QTableWidget()
        self.income_statement_table.setColumnCount(3)
        self.income_statement_table.setHorizontalHeaderLabels([
            "البند", "المبلغ", "النسبة %"
        ])
        self.income_statement_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.income_statement_table)
        
        # صافي الربح/الخسارة
        self.net_income_label = QLabel("صافي الربح/الخسارة: 0.00")
        self.net_income_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(self.net_income_label)
    
    def create_balance_sheet_tab(self):
        """إنشاء تبويب الميزانية العمومية"""
        layout = QVBoxLayout(self.balance_sheet_tab)
        
        # جدول الميزانية العمومية
        self.balance_sheet_table = QTableWidget()
        self.balance_sheet_table.setColumnCount(4)
        self.balance_sheet_table.setHorizontalHeaderLabels([
            "البند", "الأصول", "الخصوم", "حقوق الملكية"
        ])
        self.balance_sheet_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.balance_sheet_table)
        
        # إجماليات الميزانية
        balance_totals_layout = QHBoxLayout()
        
        self.assets_total = QLabel("إجمالي الأصول: 0.00")
        self.assets_total.setStyleSheet("font-weight: bold; color: blue;")
        balance_totals_layout.addWidget(self.assets_total)
        
        self.liabilities_equity_total = QLabel("إجمالي الخصوم وحقوق الملكية: 0.00")
        self.liabilities_equity_total.setStyleSheet("font-weight: bold; color: purple;")
        balance_totals_layout.addWidget(self.liabilities_equity_total)
        
        layout.addLayout(balance_totals_layout)
    
    def create_cash_flow_tab(self):
        """إنشاء تبويب التدفقات النقدية"""
        layout = QVBoxLayout(self.cash_flow_tab)
        
        # جدول التدفقات النقدية
        self.cash_flow_table = QTableWidget()
        self.cash_flow_table.setColumnCount(3)
        self.cash_flow_table.setHorizontalHeaderLabels([
            "نوع التدفق", "التدفقات الداخلة", "التدفقات الخارجة"
        ])
        self.cash_flow_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.cash_flow_table)
        
        # صافي التدفق النقدي
        self.net_cash_flow_label = QLabel("صافي التدفق النقدي: 0.00")
        self.net_cash_flow_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(self.net_cash_flow_label)
    
    def create_journal_entries_tab(self):
        """إنشاء تبويب تقرير القيود"""
        layout = QVBoxLayout(self.journal_entries_tab)
        
        # جدول القيود
        self.journal_entries_table = QTableWidget()
        self.journal_entries_table.setColumnCount(8)
        self.journal_entries_table.setHorizontalHeaderLabels([
            "رقم القيد", "التاريخ", "الوصف", "كود الحساب", 
            "اسم الحساب", "مدين", "دائن", "الحالة"
        ])
        self.journal_entries_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.journal_entries_table)
        
        # إجماليات القيود
        entries_totals_layout = QHBoxLayout()
        
        self.entries_count_label = QLabel("عدد القيود: 0")
        entries_totals_layout.addWidget(self.entries_count_label)
        
        self.entries_debit_total = QLabel("إجمالي المدين: 0.00")
        self.entries_debit_total.setStyleSheet("font-weight: bold; color: green;")
        entries_totals_layout.addWidget(self.entries_debit_total)
        
        self.entries_credit_total = QLabel("إجمالي الدائن: 0.00")
        self.entries_credit_total.setStyleSheet("font-weight: bold; color: red;")
        entries_totals_layout.addWidget(self.entries_credit_total)
        
        layout.addLayout(entries_totals_layout)
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        # يمكن إضافة تحميل أولي للبيانات هنا
        pass
    
    def generate_reports(self):
        """إنشاء جميع التقارير"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        try:
            # إنشاء ميزان المراجعة
            self.progress_bar.setValue(20)
            self.generate_trial_balance()
            
            # إنشاء قائمة الدخل
            self.progress_bar.setValue(40)
            self.generate_income_statement()
            
            # إنشاء الميزانية العمومية
            self.progress_bar.setValue(60)
            self.generate_balance_sheet()
            
            # إنشاء تقرير التدفقات النقدية
            self.progress_bar.setValue(80)
            self.generate_cash_flow()
            
            # إنشاء تقرير القيود
            self.progress_bar.setValue(90)
            self.generate_journal_entries()
            
            self.progress_bar.setValue(100)
            
            # تفعيل أزرار التصدير والطباعة
            self.export_btn.setEnabled(True)
            self.print_btn.setEnabled(True)
            
            QMessageBox.information(self, "نجح", "تم إنشاء جميع التقارير بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقارير: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)
    
    def generate_trial_balance(self):
        """إنشاء ميزان المراجعة"""
        try:
            if not self.accounting.get_connection():
                return
            
            start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
            end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
            
            # استعلام ميزان المراجعة
            query = """
                SELECT 
                    s.كود_الحساب,
                    s.اسم_الحساب,
                    s.رصيد_مدين,
                    s.رصيد_دائن,
                    COALESCE(SUM(CASE WHEN h.مدين > 0 THEN h.مدين ELSE 0 END), 0) as حركة_مدين,
                    COALESCE(SUM(CASE WHEN h.دائن > 0 THEN h.دائن ELSE 0 END), 0) as حركة_دائن
                FROM شجرة_الحسابات s
                LEFT JOIN حركات_الحسابات h ON s.كود_الحساب = h.كود_الحساب 
                    AND h.تاريخ_القيد BETWEEN %s AND %s
                WHERE s.نوع_الحساب = 'تفصيلي'
                GROUP BY s.كود_الحساب, s.اسم_الحساب, s.رصيد_مدين, s.رصيد_دائن
                ORDER BY s.كود_الحساب
            """
            
            if not self.show_zero_balances.isChecked():
                query += " HAVING (s.رصيد_مدين + s.رصيد_دائن + حركة_مدين + حركة_دائن) > 0"
            
            self.accounting.cursor.execute(query, (start_date, end_date))
            results = self.accounting.cursor.fetchall()
            
            # ملء الجدول
            self.trial_balance_table.setRowCount(len(results))
            
            total_debit = 0
            total_credit = 0
            
            for row, data in enumerate(results):
                account_code = data['كود_الحساب']
                account_name = data['اسم_الحساب']
                balance_debit = float(data['رصيد_مدين'] or 0)
                balance_credit = float(data['رصيد_دائن'] or 0)
                movement_debit = float(data['حركة_مدين'] or 0)
                movement_credit = float(data['حركة_دائن'] or 0)
                
                # حساب الرصيد النهائي
                final_debit = balance_debit + movement_debit
                final_credit = balance_credit + movement_credit
                
                total_debit += final_debit
                total_credit += final_credit
                
                self.trial_balance_table.setItem(row, 0, QTableWidgetItem(account_code))
                self.trial_balance_table.setItem(row, 1, QTableWidgetItem(account_name))
                self.trial_balance_table.setItem(row, 2, QTableWidgetItem(f"{final_debit:,.2f}"))
                self.trial_balance_table.setItem(row, 3, QTableWidgetItem(f"{final_credit:,.2f}"))
                self.trial_balance_table.setItem(row, 4, QTableWidgetItem(f"{movement_debit:,.2f}"))
                self.trial_balance_table.setItem(row, 5, QTableWidgetItem(f"{movement_credit:,.2f}"))
            
            # تحديث الإجماليات
            self.trial_balance_debit_total.setText(f"إجمالي المدين: {total_debit:,.2f}")
            self.trial_balance_credit_total.setText(f"إجمالي الدائن: {total_credit:,.2f}")
            
        except Exception as e:
            print(f"خطأ في إنشاء ميزان المراجعة: {e}")
    
    def generate_income_statement(self):
        """إنشاء قائمة الدخل"""
        # سيتم إضافة التفاصيل لاحقاً
        pass
    
    def generate_balance_sheet(self):
        """إنشاء الميزانية العمومية"""
        # سيتم إضافة التفاصيل لاحقاً
        pass
    
    def generate_cash_flow(self):
        """إنشاء تقرير التدفقات النقدية"""
        # سيتم إضافة التفاصيل لاحقاً
        pass
    
    def generate_journal_entries(self):
        """إنشاء تقرير القيود"""
        # سيتم إضافة التفاصيل لاحقاً
        pass
    
    def export_to_excel(self):
        """تصدير التقارير إلى Excel"""
        QMessageBox.information(self, "قريباً", "ميزة التصدير إلى Excel ستكون متاحة قريباً")
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "ميزة الطباعة ستكون متاحة قريباً")
    
    def closeEvent(self, event):
        """إغلاق الاتصال عند إغلاق الحوار"""
        self.accounting.close_connection()
        event.accept()
