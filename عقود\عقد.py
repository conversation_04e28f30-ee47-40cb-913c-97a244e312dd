import sys
from PySide6.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QCheckBox, QDateEdit, QMessageBox, QFileDialog, QScrollArea,
                             QGroupBox, QFormLayout, QRadioButton, QButtonGroup)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QIcon, QTextDocument
from PySide6.QtPrintSupport import QPrinter, QPrintDialog
import markdown
import os
import datetime

class ContractApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام العقود الهندسية")
        self.setGeometry(100, 100, 1000, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # إنشاء التبويبات الرئيسية
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)
        
        # إنشاء تبويبات للعقود المختلفة
        self.design_contract_tab = QWidget()
        self.implementation_contract_tab = QWidget()
        self.engineer_employment_tab = QWidget()  # تبويب جديد لعقد توظيف مهندس
        self.employee_employment_tab = QWidget()  # تبويب جديد لعقد توظيف موظف
        self.preview_tab = QWidget()
        
        # إضافة التبويبات إلى التطبيق
        self.tabs.addTab(self.design_contract_tab, "عقد تصميم هندسي")
        self.tabs.addTab(self.implementation_contract_tab, "عقد تنفيذ أعمال هندسية")
        self.tabs.addTab(self.engineer_employment_tab, "عقد توظيف مهندس")
        self.tabs.addTab(self.employee_employment_tab, "عقد توظيف موظف")
        self.tabs.addTab(self.preview_tab, "معاينة العقد")
        
        # إعداد محتوى كل تبويب
        self.setup_design_contract_tab()
        self.setup_implementation_contract_tab()
        self.setup_engineer_employment_tab()  # إعداد تبويب عقد توظيف مهندس
        self.setup_employee_employment_tab()  # إعداد تبويب عقد توظيف موظف
        self.setup_preview_tab()
        
        # تعيين مسار الملفات
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.design_template_path = os.path.join(self.current_dir, "design_contract_template.md")
        self.implementation_template_path = os.path.join(self.current_dir, "implementation_contract_template.md")
        self.engineer_employment_template_path = os.path.join(self.current_dir, "engineer_employment_contract.md")
        self.employee_employment_template_path = os.path.join(self.current_dir, "employee_employment_contract.md")
        
        # المتغير الحالي للعقد المعروض
        self.current_contract_type = None
        self.current_contract_html = None
    
    def setup_design_contract_tab(self):
        # إنشاء نموذج لعقد التصميم الهندسي
        layout = QVBoxLayout()
        scroll = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # بيانات الأطراف
        parties_group = QGroupBox("بيانات الأطراف")
        parties_layout = QFormLayout()
        
        self.design_first_party_name = QLineEdit()
        self.design_first_party_address = QLineEdit()
        self.design_first_party_phone = QLineEdit()
        self.design_first_party_email = QLineEdit()
        
        self.design_second_party_name = QLineEdit()
        self.design_second_party_address = QLineEdit()
        self.design_second_party_license = QLineEdit()
        self.design_second_party_phone = QLineEdit()
        self.design_second_party_email = QLineEdit()
        
        parties_layout.addRow("اسم الطرف الأول (المالك):", self.design_first_party_name)
        parties_layout.addRow("عنوان الطرف الأول:", self.design_first_party_address)
        parties_layout.addRow("هاتف الطرف الأول:", self.design_first_party_phone)
        parties_layout.addRow("بريد إلكتروني الطرف الأول:", self.design_first_party_email)
        
        parties_layout.addRow("اسم الطرف الثاني (المكتب الهندسي):", self.design_second_party_name)
        parties_layout.addRow("عنوان الطرف الثاني:", self.design_second_party_address)
        parties_layout.addRow("رقم ترخيص المكتب الهندسي:", self.design_second_party_license)
        parties_layout.addRow("هاتف الطرف الثاني:", self.design_second_party_phone)
        parties_layout.addRow("بريد إلكتروني الطرف الثاني:", self.design_second_party_email)
        
        parties_group.setLayout(parties_layout)
        
        # بيانات المشروع
        project_group = QGroupBox("بيانات المشروع")
        project_layout = QFormLayout()
        
        self.design_project_name = QLineEdit()
        self.design_project_location = QLineEdit()
        self.design_project_area = QDoubleSpinBox()
        self.design_project_area.setRange(0, 1000000)
        self.design_project_area.setSuffix(" متر مربع")
        
        project_layout.addRow("اسم المشروع:", self.design_project_name)
        project_layout.addRow("موقع المشروع:", self.design_project_location)
        project_layout.addRow("المساحة الإجمالية:", self.design_project_area)
        
        project_group.setLayout(project_layout)
        
        # القيمة المالية والمدة
        financial_group = QGroupBox("القيمة المالية والمدة")
        financial_layout = QFormLayout()
        
        self.design_contract_value = QDoubleSpinBox()
        self.design_contract_value.setRange(0, 10000000)
        self.design_contract_value.setSuffix(" دينار ليبي")
        
        self.design_contract_duration = QSpinBox()
        self.design_contract_duration.setRange(1, 365)
        self.design_contract_duration.setSuffix(" يوم")
        
        self.design_contract_date = QDateEdit()
        self.design_contract_date.setCalendarPopup(True)
        self.design_contract_date.setDate(QDate.currentDate())
        
        financial_layout.addRow("قيمة العقد:", self.design_contract_value)
        financial_layout.addRow("مدة التنفيذ:", self.design_contract_duration)
        financial_layout.addRow("تاريخ العقد:", self.design_contract_date)
        
        financial_group.setLayout(financial_layout)
        
        # أزرار التحكم
        control_layout = QHBoxLayout()
        
        self.design_preview_button = QPushButton("معاينة العقد")
        self.design_preview_button.clicked.connect(lambda: self.preview_contract("design"))
        
        self.design_clear_button = QPushButton("مسح البيانات")
        self.design_clear_button.clicked.connect(self.clear_design_fields)
        
        control_layout.addWidget(self.design_preview_button)
        control_layout.addWidget(self.design_clear_button)
        
        # إضافة كل المجموعات إلى التخطيط الرئيسي
        scroll_layout.addWidget(parties_group)
        scroll_layout.addWidget(project_group)
        scroll_layout.addWidget(financial_group)
        scroll_layout.addLayout(control_layout)
        
        scroll_content.setLayout(scroll_layout)
        scroll.setWidget(scroll_content)
        scroll.setWidgetResizable(True)
        
        layout.addWidget(scroll)
        self.design_contract_tab.setLayout(layout)
    
    def setup_implementation_contract_tab(self):
        # إنشاء نموذج لعقد تنفيذ الأعمال الهندسية
        layout = QVBoxLayout()
        scroll = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # بيانات الأطراف
        parties_group = QGroupBox("بيانات الأطراف")
        parties_layout = QFormLayout()
        
        self.impl_first_party_name = QLineEdit()
        self.impl_first_party_address = QLineEdit()
        self.impl_first_party_phone = QLineEdit()
        self.impl_first_party_email = QLineEdit()
        
        self.impl_second_party_name = QLineEdit()
        self.impl_second_party_address = QLineEdit()
        self.impl_second_party_license = QLineEdit()
        self.impl_second_party_phone = QLineEdit()
        self.impl_second_party_email = QLineEdit()
        
        parties_layout.addRow("اسم الطرف الأول (المالك):", self.impl_first_party_name)
        parties_layout.addRow("عنوان الطرف الأول:", self.impl_first_party_address)
        parties_layout.addRow("هاتف الطرف الأول:", self.impl_first_party_phone)
        parties_layout.addRow("بريد إلكتروني الطرف الأول:", self.impl_first_party_email)
        
        parties_layout.addRow("اسم الطرف الثاني (المقاول):", self.impl_second_party_name)
        parties_layout.addRow("عنوان الطرف الثاني:", self.impl_second_party_address)
        parties_layout.addRow("رقم ترخيص المقاول:", self.impl_second_party_license)
        parties_layout.addRow("هاتف الطرف الثاني:", self.impl_second_party_phone)
        parties_layout.addRow("بريد إلكتروني الطرف الثاني:", self.impl_second_party_email)
        
        parties_group.setLayout(parties_layout)
        
        # بيانات المشروع
        project_group = QGroupBox("بيانات المشروع")
        project_layout = QFormLayout()
        
        self.impl_project_name = QLineEdit()
        self.impl_project_location = QLineEdit()
        self.impl_project_area = QDoubleSpinBox()
        self.impl_project_area.setRange(0, 1000000)
        self.impl_project_area.setSuffix(" متر مربع")
        
        project_layout.addRow("اسم المشروع:", self.impl_project_name)
        project_layout.addRow("موقع المشروع:", self.impl_project_location)
        project_layout.addRow("المساحة الإجمالية:", self.impl_project_area)
        
        project_group.setLayout(project_layout)
        
        # القيمة المالية والمدة
        financial_group = QGroupBox("القيمة المالية والمدة")
        financial_layout = QFormLayout()
        
        self.impl_contract_value = QDoubleSpinBox()
        self.impl_contract_value.setRange(0, 100000000)
        self.impl_contract_value.setSuffix(" دينار ليبي")
        
        self.impl_contract_duration = QSpinBox()
        self.impl_contract_duration.setRange(1, 1000)
        self.impl_contract_duration.setSuffix(" يوم")
        
        self.impl_contract_date = QDateEdit()
        self.impl_contract_date.setCalendarPopup(True)
        self.impl_contract_date.setDate(QDate.currentDate())
        
        self.impl_start_date = QDateEdit()
        self.impl_start_date.setCalendarPopup(True)
        self.impl_start_date.setDate(QDate.currentDate())
        
        financial_layout.addRow("قيمة العقد:", self.impl_contract_value)
        financial_layout.addRow("مدة التنفيذ:", self.impl_contract_duration)
        financial_layout.addRow("تاريخ العقد:", self.impl_contract_date)
        financial_layout.addRow("تاريخ بدء التنفيذ:", self.impl_start_date)
        
        financial_group.setLayout(financial_layout)
        
        # أزرار التحكم
        control_layout = QHBoxLayout()
        
        self.impl_preview_button = QPushButton("معاينة العقد")
        self.impl_preview_button.clicked.connect(lambda: self.preview_contract("implementation"))
        
        self.impl_clear_button = QPushButton("مسح البيانات")
        self.impl_clear_button.clicked.connect(self.clear_implementation_fields)
        
        control_layout.addWidget(self.impl_preview_button)
        control_layout.addWidget(self.impl_clear_button)
        
        # إضافة كل المجموعات إلى التخطيط الرئيسي
        scroll_layout.addWidget(parties_group)
        scroll_layout.addWidget(project_group)
        scroll_layout.addWidget(financial_group)
        scroll_layout.addLayout(control_layout)
        
        scroll_content.setLayout(scroll_layout)
        scroll.setWidget(scroll_content)
        scroll.setWidgetResizable(True)
        
        layout.addWidget(scroll)
        self.implementation_contract_tab.setLayout(layout)
    
    def setup_engineer_employment_tab(self):
        # إنشاء نموذج لعقد توظيف مهندس
        layout = QVBoxLayout()
        scroll = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # بيانات الأطراف
        parties_group = QGroupBox("بيانات الأطراف")
        parties_layout = QFormLayout()
        
        self.eng_employer_name = QLineEdit()
        self.eng_employer_address = QLineEdit()
        self.eng_employer_commercial_reg = QLineEdit()
        self.eng_employer_license = QLineEdit()
        self.eng_employer_phone = QLineEdit()
        self.eng_employer_email = QLineEdit()
        self.eng_employer_representative = QLineEdit()
        
        self.eng_employee_name = QLineEdit()
        self.eng_employee_nationality = QLineEdit()
        self.eng_employee_birthdate = QDateEdit()
        self.eng_employee_birthdate.setCalendarPopup(True)
        self.eng_employee_birthdate.setDate(QDate.currentDate().addYears(-30))
        self.eng_employee_address = QLineEdit()
        self.eng_employee_id = QLineEdit()
        self.eng_employee_qualification = QLineEdit()
        self.eng_employee_specialization = QLineEdit()
        self.eng_employee_graduation_date = QDateEdit()
        self.eng_employee_graduation_date.setCalendarPopup(True)
        self.eng_employee_graduation_date.setDate(QDate.currentDate().addYears(-5))
        self.eng_employee_license_number = QLineEdit()
        self.eng_employee_license_date = QDateEdit()
        self.eng_employee_license_date.setCalendarPopup(True)
        self.eng_employee_license_date.setDate(QDate.currentDate().addYears(-3))
        
        parties_layout.addRow("اسم الطرف الأول (صاحب العمل):", self.eng_employer_name)
        parties_layout.addRow("عنوان الطرف الأول:", self.eng_employer_address)
        parties_layout.addRow("رقم السجل التجاري:", self.eng_employer_commercial_reg)
        parties_layout.addRow("رقم الترخيص:", self.eng_employer_license)
        parties_layout.addRow("هاتف الطرف الأول:", self.eng_employer_phone)
        parties_layout.addRow("بريد إلكتروني الطرف الأول:", self.eng_employer_email)
        parties_layout.addRow("الممثل القانوني:", self.eng_employer_representative)
        
        parties_layout.addRow("اسم الطرف الثاني (المهندس):", self.eng_employee_name)
        parties_layout.addRow("الجنسية:", self.eng_employee_nationality)
        parties_layout.addRow("تاريخ الميلاد:", self.eng_employee_birthdate)
        parties_layout.addRow("محل الإقامة:", self.eng_employee_address)
        parties_layout.addRow("رقم الهوية/جواز السفر:", self.eng_employee_id)
        parties_layout.addRow("المؤهل العلمي:", self.eng_employee_qualification)
        parties_layout.addRow("التخصص:", self.eng_employee_specialization)
        parties_layout.addRow("تاريخ الحصول على المؤهل:", self.eng_employee_graduation_date)
        parties_layout.addRow("رقم الترخيص المهني:", self.eng_employee_license_number)
        parties_layout.addRow("تاريخ الترخيص:", self.eng_employee_license_date)
        
        parties_group.setLayout(parties_layout)
        
        # بيانات الوظيفة
        job_group = QGroupBox("بيانات الوظيفة")
        job_layout = QFormLayout()
        
        self.eng_job_title = QLineEdit()
        self.eng_job_title.setText("مهندس")
        self.eng_job_location = QLineEdit()
        
        job_layout.addRow("المسمى الوظيفي:", self.eng_job_title)
        job_layout.addRow("مكان العمل:", self.eng_job_location)
        
        job_group.setLayout(job_layout)
        
        # مدة العقد
        duration_group = QGroupBox("مدة العقد")
        duration_layout = QFormLayout()
        
        self.eng_contract_duration = QSpinBox()
        self.eng_contract_duration.setRange(1, 60)
        self.eng_contract_duration.setValue(12)
        self.eng_contract_duration.setSuffix(" شهر")
        
        self.eng_contract_start_date = QDateEdit()
        self.eng_contract_start_date.setCalendarPopup(True)
        self.eng_contract_start_date.setDate(QDate.currentDate())
        
        self.eng_contract_end_date = QDateEdit()
        self.eng_contract_end_date.setCalendarPopup(True)
        self.eng_contract_end_date.setDate(QDate.currentDate().addYears(1))
        
        self.eng_contract_date = QDateEdit()
        self.eng_contract_date.setCalendarPopup(True)
        self.eng_contract_date.setDate(QDate.currentDate())
        
        duration_layout.addRow("مدة العقد:", self.eng_contract_duration)
        duration_layout.addRow("تاريخ بدء العقد:", self.eng_contract_start_date)
        duration_layout.addRow("تاريخ انتهاء العقد:", self.eng_contract_end_date)
        duration_layout.addRow("تاريخ تحرير العقد:", self.eng_contract_date)
        
        duration_group.setLayout(duration_layout)
        
        # الأجر والمزايا المالية
        salary_group = QGroupBox("الأجر والمزايا المالية")
        salary_layout = QFormLayout()
        
        self.eng_basic_salary = QDoubleSpinBox()
        self.eng_basic_salary.setRange(0, 100000)
        self.eng_basic_salary.setSuffix(" دينار ليبي")
        
        self.eng_housing_allowance = QDoubleSpinBox()
        self.eng_housing_allowance.setRange(0, 50000)
        self.eng_housing_allowance.setSuffix(" دينار ليبي")
        
        self.eng_transportation_allowance = QDoubleSpinBox()
        self.eng_transportation_allowance.setRange(0, 10000)
        self.eng_transportation_allowance.setSuffix(" دينار ليبي")
        
        self.eng_nature_of_work_allowance = QDoubleSpinBox()
        self.eng_nature_of_work_allowance.setRange(0, 20000)
        self.eng_nature_of_work_allowance.setSuffix(" دينار ليبي")
        
        self.eng_other_allowances = QDoubleSpinBox()
        self.eng_other_allowances.setRange(0, 50000)
        self.eng_other_allowances.setSuffix(" دينار ليبي")
        
        self.eng_annual_increase = QDoubleSpinBox()
        self.eng_annual_increase.setRange(0, 50)
        self.eng_annual_increase.setValue(5)
        self.eng_annual_increase.setSuffix(" %")
        
        self.eng_annual_bonus = QDoubleSpinBox()
        self.eng_annual_bonus.setRange(0, 100000)
        self.eng_annual_bonus.setSuffix(" دينار ليبي")
        
        salary_layout.addRow("الراتب الأساسي:", self.eng_basic_salary)
        salary_layout.addRow("بدل سكن:", self.eng_housing_allowance)
        salary_layout.addRow("بدل مواصلات:", self.eng_transportation_allowance)
        salary_layout.addRow("بدل طبيعة عمل:", self.eng_nature_of_work_allowance)
        salary_layout.addRow("بدلات أخرى:", self.eng_other_allowances)
        salary_layout.addRow("نسبة الزيادة السنوية:", self.eng_annual_increase)
        salary_layout.addRow("المكافأة السنوية:", self.eng_annual_bonus)
        
        salary_group.setLayout(salary_layout)
        
        # ساعات العمل
        hours_group = QGroupBox("ساعات العمل")
        hours_layout = QFormLayout()
        
        self.eng_daily_hours = QSpinBox()
        self.eng_daily_hours.setRange(1, 12)
        self.eng_daily_hours.setValue(8)
        self.eng_daily_hours.setSuffix(" ساعة")
        
        self.eng_weekly_days = QSpinBox()
        self.eng_weekly_days.setRange(1, 7)
        self.eng_weekly_days.setValue(5)
        self.eng_weekly_days.setSuffix(" يوم")
        
        hours_layout.addRow("ساعات العمل اليومية:", self.eng_daily_hours)
        hours_layout.addRow("أيام العمل الأسبوعية:", self.eng_weekly_days)
        
        hours_group.setLayout(hours_layout)
        
        # أزرار التحكم
        control_layout = QHBoxLayout()
        
        self.eng_preview_button = QPushButton("معاينة العقد")
        self.eng_preview_button.clicked.connect(lambda: self.preview_contract("engineer_employment"))
        
        self.eng_clear_button = QPushButton("مسح البيانات")
        self.eng_clear_button.clicked.connect(self.clear_engineer_employment_fields)
        
        control_layout.addWidget(self.eng_preview_button)
        control_layout.addWidget(self.eng_clear_button)
        
        # إضافة كل المجموعات إلى التخطيط الرئيسي
        scroll_layout.addWidget(parties_group)
        scroll_layout.addWidget(job_group)
        scroll_layout.addWidget(duration_group)
        scroll_layout.addWidget(salary_group)
        scroll_layout.addWidget(hours_group)
        scroll_layout.addLayout(control_layout)
        
        scroll_content.setLayout(scroll_layout)
        scroll.setWidget(scroll_content)
        scroll.setWidgetResizable(True)
        
        layout.addWidget(scroll)
        self.engineer_employment_tab.setLayout(layout)
    
    def setup_employee_employment_tab(self):
        # إنشاء نموذج لعقد توظيف موظف
        layout = QVBoxLayout()
        scroll = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # بيانات الأطراف
        parties_group = QGroupBox("بيانات الأطراف")
        parties_layout = QFormLayout()
        
        self.emp_employer_name = QLineEdit()
        self.emp_employer_address = QLineEdit()
        self.emp_employer_commercial_reg = QLineEdit()
        self.emp_employer_license = QLineEdit()
        self.emp_employer_phone = QLineEdit()
        self.emp_employer_email = QLineEdit()
        self.emp_employer_representative = QLineEdit()
        
        self.emp_employee_name = QLineEdit()
        self.emp_employee_nationality = QLineEdit()
        self.emp_employee_birthdate = QDateEdit()
        self.emp_employee_birthdate.setCalendarPopup(True)
        self.emp_employee_birthdate.setDate(QDate.currentDate().addYears(-30))
        self.emp_employee_address = QLineEdit()
        self.emp_employee_id = QLineEdit()
        self.emp_employee_qualification = QLineEdit()
        self.emp_employee_specialization = QLineEdit()
        self.emp_employee_graduation_date = QDateEdit()
        self.emp_employee_graduation_date.setCalendarPopup(True)
        self.emp_employee_graduation_date.setDate(QDate.currentDate().addYears(-5))
        
        parties_layout.addRow("اسم الطرف الأول (صاحب العمل):", self.emp_employer_name)
        parties_layout.addRow("عنوان الطرف الأول:", self.emp_employer_address)
        parties_layout.addRow("رقم السجل التجاري:", self.emp_employer_commercial_reg)
        parties_layout.addRow("رقم الترخيص:", self.emp_employer_license)
        parties_layout.addRow("هاتف الطرف الأول:", self.emp_employer_phone)
        parties_layout.addRow("بريد إلكتروني الطرف الأول:", self.emp_employer_email)
        parties_layout.addRow("الممثل القانوني:", self.emp_employer_representative)
        
        parties_layout.addRow("اسم الطرف الثاني (الموظف):", self.emp_employee_name)
        parties_layout.addRow("الجنسية:", self.emp_employee_nationality)
        parties_layout.addRow("تاريخ الميلاد:", self.emp_employee_birthdate)
        parties_layout.addRow("محل الإقامة:", self.emp_employee_address)
        parties_layout.addRow("رقم الهوية/جواز السفر:", self.emp_employee_id)
        parties_layout.addRow("المؤهل العلمي:", self.emp_employee_qualification)
        parties_layout.addRow("التخصص:", self.emp_employee_specialization)
        parties_layout.addRow("تاريخ الحصول على المؤهل:", self.emp_employee_graduation_date)
        
        parties_group.setLayout(parties_layout)
        
        # بيانات الوظيفة
        job_group = QGroupBox("بيانات الوظيفة")
        job_layout = QFormLayout()
        
        self.emp_job_title = QLineEdit()
        self.emp_job_location = QLineEdit()
        
        job_layout.addRow("المسمى الوظيفي:", self.emp_job_title)
        job_layout.addRow("مكان العمل:", self.emp_job_location)
        
        job_group.setLayout(job_layout)
        
        # مدة العقد
        duration_group = QGroupBox("مدة العقد")
        duration_layout = QFormLayout()
        
        self.emp_contract_duration = QSpinBox()
        self.emp_contract_duration.setRange(1, 60)
        self.emp_contract_duration.setValue(12)
        self.emp_contract_duration.setSuffix(" شهر")
        
        self.emp_contract_start_date = QDateEdit()
        self.emp_contract_start_date.setCalendarPopup(True)
        self.emp_contract_start_date.setDate(QDate.currentDate())
        
        self.emp_contract_end_date = QDateEdit()
        self.emp_contract_end_date.setCalendarPopup(True)
        self.emp_contract_end_date.setDate(QDate.currentDate().addYears(1))
        
        self.emp_contract_date = QDateEdit()
        self.emp_contract_date.setCalendarPopup(True)
        self.emp_contract_date.setDate(QDate.currentDate())
        
        duration_layout.addRow("مدة العقد:", self.emp_contract_duration)
        duration_layout.addRow("تاريخ بدء العقد:", self.emp_contract_start_date)
        duration_layout.addRow("تاريخ انتهاء العقد:", self.emp_contract_end_date)
        duration_layout.addRow("تاريخ تحرير العقد:", self.emp_contract_date)
        
        duration_group.setLayout(duration_layout)
        
        # الأجر والمزايا المالية
        salary_group = QGroupBox("الأجر والمزايا المالية")
        salary_layout = QFormLayout()
        
        self.emp_basic_salary = QDoubleSpinBox()
        self.emp_basic_salary.setRange(0, 100000)
        self.emp_basic_salary.setSuffix(" دينار ليبي")
        
        self.emp_housing_allowance = QDoubleSpinBox()
        self.emp_housing_allowance.setRange(0, 50000)
        self.emp_housing_allowance.setSuffix(" دينار ليبي")
        
        self.emp_transportation_allowance = QDoubleSpinBox()
        self.emp_transportation_allowance.setRange(0, 10000)
        self.emp_transportation_allowance.setSuffix(" دينار ليبي")
        
        self.emp_other_allowances = QDoubleSpinBox()
        self.emp_other_allowances.setRange(0, 50000)
        self.emp_other_allowances.setSuffix(" دينار ليبي")
        
        self.emp_annual_increase = QDoubleSpinBox()
        self.emp_annual_increase.setRange(0, 50)
        self.emp_annual_increase.setValue(5)
        self.emp_annual_increase.setSuffix(" %")
        
        self.emp_annual_bonus = QDoubleSpinBox()
        self.emp_annual_bonus.setRange(0, 100000)
        self.emp_annual_bonus.setSuffix(" دينار ليبي")
        
        salary_layout.addRow("الراتب الأساسي:", self.emp_basic_salary)
        salary_layout.addRow("بدل سكن:", self.emp_housing_allowance)
        salary_layout.addRow("بدل مواصلات:", self.emp_transportation_allowance)
        salary_layout.addRow("بدلات أخرى:", self.emp_other_allowances)
        salary_layout.addRow("نسبة الزيادة السنوية:", self.emp_annual_increase)
        salary_layout.addRow("المكافأة السنوية:", self.emp_annual_bonus)
        
        salary_group.setLayout(salary_layout)
        
        # ساعات العمل
        hours_group = QGroupBox("ساعات العمل")
        hours_layout = QFormLayout()
        
        self.emp_daily_hours = QSpinBox()
        self.emp_daily_hours.setRange(1, 12)
        self.emp_daily_hours.setValue(8)
        self.emp_daily_hours.setSuffix(" ساعة")
        
        self.emp_weekly_days = QSpinBox()
        self.emp_weekly_days.setRange(1, 7)
        self.emp_weekly_days.setValue(5)
        self.emp_weekly_days.setSuffix(" يوم")
        
        hours_layout.addRow("ساعات العمل اليومية:", self.emp_daily_hours)
        hours_layout.addRow("أيام العمل الأسبوعية:", self.emp_weekly_days)
        
        hours_group.setLayout(hours_layout)
        
        # أزرار التحكم
        control_layout = QHBoxLayout()
        
        self.emp_preview_button = QPushButton("معاينة العقد")
        self.emp_preview_button.clicked.connect(lambda: self.preview_contract("employee_employment"))
        
        self.emp_clear_button = QPushButton("مسح البيانات")
        self.emp_clear_button.clicked.connect(self.clear_employee_employment_fields)
        
        control_layout.addWidget(self.emp_preview_button)
        control_layout.addWidget(self.emp_clear_button)
        
        # إضافة كل المجموعات إلى التخطيط الرئيسي
        scroll_layout.addWidget(parties_group)
        scroll_layout.addWidget(job_group)
        scroll_layout.addWidget(duration_group)
        scroll_layout.addWidget(salary_group)
        scroll_layout.addWidget(hours_group)
        scroll_layout.addLayout(control_layout)
        
        scroll_content.setLayout(scroll_layout)
        scroll.setWidget(scroll_content)
        scroll.setWidgetResizable(True)
        
        layout.addWidget(scroll)
        self.employee_employment_tab.setLayout(layout)
    
    def setup_preview_tab(self):
        # إنشاء تبويب معاينة العقد
        layout = QVBoxLayout()
        
        # منطقة عرض العقد
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        
        # أزرار التحكم
        control_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ العقد")
        self.save_button.clicked.connect(self.save_contract)
        
        self.print_button = QPushButton("طباعة العقد")
        self.print_button.clicked.connect(self.print_contract)
        
        control_layout.addWidget(self.save_button)
        control_layout.addWidget(self.print_button)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        layout.addWidget(self.preview_text)
        layout.addLayout(control_layout)
        
        self.preview_tab.setLayout(layout)
    
    def preview_contract(self, contract_type):
        self.current_contract_type = contract_type
        
        if contract_type == "design":
            # قراءة قالب عقد التصميم
            with open(self.design_template_path, 'r', encoding='utf-8') as file:
                template = file.read()
            
            # استبدال المتغيرات في القالب
            contract = template.replace("{{first_party_name}}", self.design_first_party_name.text())
            contract = contract.replace("{{first_party_address}}", self.design_first_party_address.text())
            contract = contract.replace("{{first_party_phone}}", self.design_first_party_phone.text())
            contract = contract.replace("{{first_party_email}}", self.design_first_party_email.text())
            
            contract = contract.replace("{{second_party_name}}", self.design_second_party_name.text())
            contract = contract.replace("{{second_party_address}}", self.design_second_party_address.text())
            contract = contract.replace("{{second_party_license}}", self.design_second_party_license.text())
            contract = contract.replace("{{second_party_phone}}", self.design_second_party_phone.text())
            contract = contract.replace("{{second_party_email}}", self.design_second_party_email.text())
            
            contract = contract.replace("{{project_name}}", self.design_project_name.text())
            contract = contract.replace("{{project_location}}", self.design_project_location.text())
            contract = contract.replace("{{project_area}}", str(self.design_project_area.value()))
            
            contract = contract.replace("{{contract_value}}", str(self.design_contract_value.value()))
            contract = contract.replace("{{contract_duration}}", str(self.design_contract_duration.value()))
            contract = contract.replace("{{contract_date}}", self.design_contract_date.date().toString("yyyy/MM/dd"))
            
        elif contract_type == "implementation":
            # قراءة قالب عقد التنفيذ
            with open(self.implementation_template_path, 'r', encoding='utf-8') as file:
                template = file.read()
            
            # استبدال المتغيرات في القالب
            contract = template.replace("{{first_party_name}}", self.impl_first_party_name.text())
            contract = contract.replace("{{first_party_address}}", self.impl_first_party_address.text())
            contract = contract.replace("{{first_party_phone}}", self.impl_first_party_phone.text())
            contract = contract.replace("{{first_party_email}}", self.impl_first_party_email.text())
            
            contract = contract.replace("{{second_party_name}}", self.impl_second_party_name.text())
            contract = contract.replace("{{second_party_address}}", self.impl_second_party_address.text())
            contract = contract.replace("{{second_party_license}}", self.impl_second_party_license.text())
            contract = contract.replace("{{second_party_phone}}", self.impl_second_party_phone.text())
            contract = contract.replace("{{second_party_email}}", self.impl_second_party_email.text())
            
            contract = contract.replace("{{project_name}}", self.impl_project_name.text())
            contract = contract.replace("{{project_location}}", self.impl_project_location.text())
            contract = contract.replace("{{project_area}}", str(self.impl_project_area.value()))
            
            contract = contract.replace("{{contract_value}}", str(self.impl_contract_value.value()))
            contract = contract.replace("{{contract_duration}}", str(self.impl_contract_duration.value()))
            contract = contract.replace("{{contract_date}}", self.impl_contract_date.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{start_date}}", self.impl_start_date.date().toString("yyyy/MM/dd"))
            
        elif contract_type == "engineer_employment":
            # قراءة قالب عقد توظيف مهندس
            with open(self.engineer_employment_template_path, 'r', encoding='utf-8') as file:
                template = file.read()
            
            # تحويل التاريخ إلى النص العربي
            contract_date_str = self.eng_contract_date.date().toString("yyyy/MM/dd")
            
            # استبدال المتغيرات في القالب
            contract = template
            
            # بيانات الطرف الأول (صاحب العمل)
            contract = contract.replace("{{employer_name}}", self.eng_employer_name.text())
            contract = contract.replace("{{employer_address}}", self.eng_employer_address.text())
            contract = contract.replace("{{employer_commercial_reg}}", self.eng_employer_commercial_reg.text())
            contract = contract.replace("{{employer_license}}", self.eng_employer_license.text())
            contract = contract.replace("{{employer_phone}}", self.eng_employer_phone.text())
            contract = contract.replace("{{employer_email}}", self.eng_employer_email.text())
            contract = contract.replace("{{employer_representative}}", self.eng_employer_representative.text())
            
            # بيانات الطرف الثاني (المهندس)
            contract = contract.replace("{{employee_name}}", self.eng_employee_name.text())
            contract = contract.replace("{{employee_nationality}}", self.eng_employee_nationality.text())
            contract = contract.replace("{{employee_birthdate}}", self.eng_employee_birthdate.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{employee_address}}", self.eng_employee_address.text())
            contract = contract.replace("{{employee_id}}", self.eng_employee_id.text())
            contract = contract.replace("{{employee_qualification}}", self.eng_employee_qualification.text())
            contract = contract.replace("{{employee_specialization}}", self.eng_employee_specialization.text())
            contract = contract.replace("{{employee_graduation_date}}", self.eng_employee_graduation_date.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{employee_license_number}}", self.eng_employee_license_number.text())
            contract = contract.replace("{{employee_license_date}}", self.eng_employee_license_date.date().toString("yyyy/MM/dd"))
            
            # بيانات الوظيفة
            contract = contract.replace("{{job_title}}", self.eng_job_title.text())
            contract = contract.replace("{{job_location}}", self.eng_job_location.text())
            
            # مدة العقد
            contract = contract.replace("{{contract_duration}}", str(self.eng_contract_duration.value()))
            contract = contract.replace("{{contract_start_date}}", self.eng_contract_start_date.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{contract_end_date}}", self.eng_contract_end_date.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{contract_date}}", contract_date_str)
            
            # الأجر والمزايا المالية
            contract = contract.replace("{{basic_salary}}", str(self.eng_basic_salary.value()))
            contract = contract.replace("{{housing_allowance}}", str(self.eng_housing_allowance.value()))
            contract = contract.replace("{{transportation_allowance}}", str(self.eng_transportation_allowance.value()))
            contract = contract.replace("{{nature_of_work_allowance}}", str(self.eng_nature_of_work_allowance.value()))
            contract = contract.replace("{{other_allowances}}", str(self.eng_other_allowances.value()))
            
            total_salary = (self.eng_basic_salary.value() + self.eng_housing_allowance.value() + 
                           self.eng_transportation_allowance.value() + self.eng_nature_of_work_allowance.value() + 
                           self.eng_other_allowances.value())
            contract = contract.replace("{{total_salary}}", str(total_salary))
            
            contract = contract.replace("{{annual_increase}}", str(self.eng_annual_increase.value()))
            contract = contract.replace("{{annual_bonus}}", str(self.eng_annual_bonus.value()))
            
            # ساعات العمل
            contract = contract.replace("{{daily_hours}}", str(self.eng_daily_hours.value()))
            contract = contract.replace("{{weekly_days}}", str(self.eng_weekly_days.value()))
            
        elif contract_type == "employee_employment":
            # قراءة قالب عقد توظيف موظف
            with open(self.employee_employment_template_path, 'r', encoding='utf-8') as file:
                template = file.read()
            
            # تحويل التاريخ إلى النص العربي
            contract_date_str = self.emp_contract_date.date().toString("yyyy/MM/dd")
            
            # استبدال المتغيرات في القالب
            contract = template
            
            # بيانات الطرف الأول (صاحب العمل)
            contract = contract.replace("{{employer_name}}", self.emp_employer_name.text())
            contract = contract.replace("{{employer_address}}", self.emp_employer_address.text())
            contract = contract.replace("{{employer_commercial_reg}}", self.emp_employer_commercial_reg.text())
            contract = contract.replace("{{employer_license}}", self.emp_employer_license.text())
            contract = contract.replace("{{employer_phone}}", self.emp_employer_phone.text())
            contract = contract.replace("{{employer_email}}", self.emp_employer_email.text())
            contract = contract.replace("{{employer_representative}}", self.emp_employer_representative.text())
            
            # بيانات الطرف الثاني (الموظف)
            contract = contract.replace("{{employee_name}}", self.emp_employee_name.text())
            contract = contract.replace("{{employee_nationality}}", self.emp_employee_nationality.text())
            contract = contract.replace("{{employee_birthdate}}", self.emp_employee_birthdate.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{employee_address}}", self.emp_employee_address.text())
            contract = contract.replace("{{employee_id}}", self.emp_employee_id.text())
            contract = contract.replace("{{employee_qualification}}", self.emp_employee_qualification.text())
            contract = contract.replace("{{employee_specialization}}", self.emp_employee_specialization.text())
            contract = contract.replace("{{employee_graduation_date}}", self.emp_employee_graduation_date.date().toString("yyyy/MM/dd"))
            
            # بيانات الوظيفة
            contract = contract.replace("{{job_title}}", self.emp_job_title.text())
            contract = contract.replace("{{job_location}}", self.emp_job_location.text())
            
            # مدة العقد
            contract = contract.replace("{{contract_duration}}", str(self.emp_contract_duration.value()))
            contract = contract.replace("{{contract_start_date}}", self.emp_contract_start_date.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{contract_end_date}}", self.emp_contract_end_date.date().toString("yyyy/MM/dd"))
            contract = contract.replace("{{contract_date}}", contract_date_str)
            
            # الأجر والمزايا المالية
            contract = contract.replace("{{basic_salary}}", str(self.emp_basic_salary.value()))
            contract = contract.replace("{{housing_allowance}}", str(self.emp_housing_allowance.value()))
            contract = contract.replace("{{transportation_allowance}}", str(self.emp_transportation_allowance.value()))
            contract = contract.replace("{{other_allowances}}", str(self.emp_other_allowances.value()))
            
            total_salary = (self.emp_basic_salary.value() + self.emp_housing_allowance.value() + 
                           self.emp_transportation_allowance.value() + self.emp_other_allowances.value())
            contract = contract.replace("{{total_salary}}", str(total_salary))
            
            contract = contract.replace("{{annual_increase}}", str(self.emp_annual_increase.value()))
            contract = contract.replace("{{annual_bonus}}", str(self.emp_annual_bonus.value()))
            
            # ساعات العمل
            contract = contract.replace("{{daily_hours}}", str(self.emp_daily_hours.value()))
            contract = contract.replace("{{weekly_days}}", str(self.emp_weekly_days.value()))
        
        # تحويل النص إلى HTML
        html = markdown.markdown(contract)
        
        # تخزين HTML للاستخدام في الحفظ والطباعة
        self.current_contract_html = html
        
        # عرض العقد في تبويب المعاينة
        self.preview_text.setHtml(html)
        self.tabs.setCurrentWidget(self.preview_tab)
    
    def save_contract(self):
        if not self.current_contract_html:
            QMessageBox.warning(self, "تنبيه", "يرجى معاينة العقد أولاً")
            return
        
        # تحديد نوع العقد للاستخدام في اسم الملف
        contract_type_name = {
            "design": "عقد_تصميم_هندسي",
            "implementation": "عقد_تنفيذ_أعمال_هندسية",
            "engineer_employment": "عقد_توظيف_مهندس",
            "employee_employment": "عقد_توظيف_موظف"
        }.get(self.current_contract_type, "عقد")
        
        # فتح مربع حوار لاختيار مكان حفظ الملف
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "حفظ العقد", 
            f"{contract_type_name}_{datetime.datetime.now().strftime('%Y%m%d')}.html", 
            "HTML Files (*.html);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.current_contract_html)
                QMessageBox.information(self, "تم الحفظ", f"تم حفظ العقد بنجاح في:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الملف:\n{str(e)}")
    
    def print_contract(self):
        if not self.current_contract_html:
            QMessageBox.warning(self, "تنبيه", "يرجى معاينة العقد أولاً")
            return
        
        # إنشاء مستند نصي
        document = QTextDocument()
        document.setHtml(self.current_contract_html)
        
        # إنشاء مربع حوار الطباعة
        printer = QPrinter()
        dialog = QPrintDialog(printer, self)
        
        if dialog.exec_() == QPrintDialog.Accepted:
            document.print_(printer)
    
    def clear_design_fields(self):
        # مسح حقول عقد التصميم
        self.design_first_party_name.clear()
        self.design_first_party_address.clear()
        self.design_first_party_phone.clear()
        self.design_first_party_email.clear()
        
        self.design_second_party_name.clear()
        self.design_second_party_address.clear()
        self.design_second_party_license.clear()
        self.design_second_party_phone.clear()
        self.design_second_party_email.clear()
        
        self.design_project_name.clear()
        self.design_project_location.clear()
        self.design_project_area.setValue(0)
        
        self.design_contract_value.setValue(0)
        self.design_contract_duration.setValue(1)
        self.design_contract_date.setDate(QDate.currentDate())
    
    def clear_implementation_fields(self):
        # مسح حقول عقد التنفيذ
        self.impl_first_party_name.clear()
        self.impl_first_party_address.clear()
        self.impl_first_party_phone.clear()
        self.impl_first_party_email.clear()
        
        self.impl_second_party_name.clear()
        self.impl_second_party_address.clear()
        self.impl_second_party_license.clear()
        self.impl_second_party_phone.clear()
        self.impl_second_party_email.clear()
        
        self.impl_project_name.clear()
        self.impl_project_location.clear()
        self.impl_project_area.setValue(0)
        
        self.impl_contract_value.setValue(0)
        self.impl_contract_duration.setValue(1)
        self.impl_contract_date.setDate(QDate.currentDate())
        self.impl_start_date.setDate(QDate.currentDate())
    
    def clear_engineer_employment_fields(self):
        # مسح حقول عقد توظيف مهندس
        self.eng_employer_name.clear()
        self.eng_employer_address.clear()
        self.eng_employer_commercial_reg.clear()
        self.eng_employer_license.clear()
        self.eng_employer_phone.clear()
        self.eng_employer_email.clear()
        self.eng_employer_representative.clear()
        
        self.eng_employee_name.clear()
        self.eng_employee_nationality.clear()
        self.eng_employee_birthdate.setDate(QDate.currentDate().addYears(-30))
        self.eng_employee_address.clear()
        self.eng_employee_id.clear()
        self.eng_employee_qualification.clear()
        self.eng_employee_specialization.clear()
        self.eng_employee_graduation_date.setDate(QDate.currentDate().addYears(-5))
        self.eng_employee_license_number.clear()
        self.eng_employee_license_date.setDate(QDate.currentDate().addYears(-3))
        
        self.eng_job_title.setText("مهندس")
        self.eng_job_location.clear()
        
        self.eng_contract_duration.setValue(12)
        self.eng_contract_start_date.setDate(QDate.currentDate())
        self.eng_contract_end_date.setDate(QDate.currentDate().addYears(1))
        self.eng_contract_date.setDate(QDate.currentDate())
        
        self.eng_basic_salary.setValue(0)
        self.eng_housing_allowance.setValue(0)
        self.eng_transportation_allowance.setValue(0)
        self.eng_nature_of_work_allowance.setValue(0)
        self.eng_other_allowances.setValue(0)
        self.eng_annual_increase.setValue(5)
        self.eng_annual_bonus.setValue(0)
        
        self.eng_daily_hours.setValue(8)
        self.eng_weekly_days.setValue(5)
    
    def clear_employee_employment_fields(self):
        # مسح حقول عقد توظيف موظف
        self.emp_employer_name.clear()
        self.emp_employer_address.clear()
        self.emp_employer_commercial_reg.clear()
        self.emp_employer_license.clear()
        self.emp_employer_phone.clear()
        self.emp_employer_email.clear()
        self.emp_employer_representative.clear()
        
        self.emp_employee_name.clear()
        self.emp_employee_nationality.clear()
        self.emp_employee_birthdate.setDate(QDate.currentDate().addYears(-30))
        self.emp_employee_address.clear()
        self.emp_employee_id.clear()
        self.emp_employee_qualification.clear()
        self.emp_employee_specialization.clear()
        self.emp_employee_graduation_date.setDate(QDate.currentDate().addYears(-5))
        
        self.emp_job_title.clear()
        self.emp_job_location.clear()
        
        self.emp_contract_duration.setValue(12)
        self.emp_contract_start_date.setDate(QDate.currentDate())
        self.emp_contract_end_date.setDate(QDate.currentDate().addYears(1))
        self.emp_contract_date.setDate(QDate.currentDate())
        
        self.emp_basic_salary.setValue(0)
        self.emp_housing_allowance.setValue(0)
        self.emp_transportation_allowance.setValue(0)
        self.emp_other_allowances.setValue(0)
        self.emp_annual_increase.setValue(5)
        self.emp_annual_bonus.setValue(0)
        
        self.emp_daily_hours.setValue(8)
        self.emp_weekly_days.setValue(5)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ContractApp()
    window.show()
    sys.exit(app.exec())
