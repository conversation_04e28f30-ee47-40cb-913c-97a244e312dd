#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime, date
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from متغيرات import *
from ستايل import *

class ModernCard(QFrame):
    """بطاقة عرض عصرية ومطورة للبيانات"""
    card_clicked = Signal(dict)
    card_double_clicked = Signal(dict)
    
    def __init__(self, data, card_type="project", parent=None):
        super().__init__(parent)
        self.data = data
        self.card_type = card_type
        self.setup_ui()
        self.apply_modern_styles()
        
    def setup_ui(self):
        """إعداد واجهة البطاقة العصرية مع أحجام محسنة للتوزيع الأفقي"""
        # تحديث أحجام البطاقة للتوزيع الأفقي المحسن
        if self.card_type == "project":
            self.setMinimumSize(270, 250)  # عرض ثابت للتوزيع المتسق
            self.setMaximumSize(270, 550)  # عرض ثابت
        else:
            self.setMinimumSize(260, 200)  # عرض ثابت للأنواع الأخرى
            self.setMaximumSize(260, 500)  # عرض ثابت

        self.setObjectName("ModernCard")

        # تطبيق الاتجاه العربي RTL على البطاقة
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # رأس البطاقة
        self.create_header(main_layout)
        
        # المحتوى الرئيسي
        self.create_content(main_layout)
        
        # الإجراءات السريعة
        self.create_actions(main_layout)
        
    def create_header(self, layout):
        """إنشاء رأس البطاقة"""
        header_layout = QHBoxLayout()
        
        # أيقونة ونوع البطاقة
        icon_label = QLabel()
        icon_label.setFixedSize(30, 30)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # تحديد الأيقونة حسب نوع البطاقة
        if self.card_type == "project":
            icon_label.setText("🏗️")
            title = self.data.get('اسم_المشروع', 'مشروع غير محدد')
        elif self.card_type == "client":
            icon_label.setText("👤")
            title = self.data.get('اسم_العميل', 'عميل غير محدد')
        elif self.card_type == "employee":
            icon_label.setText("👷")
            title = self.data.get('اسم_الموظف', 'موظف غير محدد')
        elif self.card_type == "expense":
            icon_label.setText("💰")
            title = self.data.get('البيان', 'مصروف غير محدد')
        elif self.card_type == "realestate":
            icon_label.setText("🏠")
            title = self.data.get('اسم_العقار', 'عقار غير محدد')
        elif self.card_type == "training":
            icon_label.setText("📚")
            title = self.data.get('اسم_الدورة', 'دورة غير محددة')
        else:
            icon_label.setText("📄")
            title = "عنصر غير محدد"
            
        icon_label.setObjectName("CardIcon")
        
        # عنوان البطاقة
        self.title_label = QLabel(title)
        self.title_label.setObjectName("CardTitle")
        self.title_label.setWordWrap(True)
        self.title_label.setMaximumHeight(50)
        
        # حالة/تصنيف
        status = self.get_status_or_classification()
        self.status_label = QLabel(status)
        self.status_label.setObjectName("CardStatus")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFixedSize(90, 30)
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(self.title_label, 1)
        header_layout.addWidget(self.status_label)
        
        layout.addLayout(header_layout)
        
    def get_status_or_classification(self):
        """الحصول على الحالة أو التصنيف حسب نوع البطاقة"""
        if self.card_type in ["project", "realestate", "training"]:
            return self.data.get('الحالة', 'غير محدد')
        else:
            return self.data.get('التصنيف', 'غير محدد')
            
    def create_content(self, layout):
        """إنشاء محتوى البطاقة"""
        content_frame = QFrame()
        content_frame.setObjectName("CardContent")
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(10)
        
        if self.card_type == "project":
            self.create_project_content(content_layout)
        elif self.card_type == "client":
            self.create_client_content(content_layout)
        elif self.card_type == "employee":
            self.create_employee_content(content_layout)
        elif self.card_type == "expense":
            self.create_expense_content(content_layout)
        elif self.card_type == "realestate":
            self.create_realestate_content(content_layout)
        elif self.card_type == "training":
            self.create_training_content(content_layout)
            
        layout.addWidget(content_frame)
    
    #محتوى بطاقة المشروع
    def create_project_content(self, layout):
        # نوع المشروع (التصنيف) مع تلوين الخلفية
        project_type = self.data.get('التصنيف', 'غير محدد')
        project_type_label = QLabel(f"📋 نوع المشروع: {project_type}")
        project_type_label.setObjectName("CardInfo")

        # تطبيق تلوين الخلفية حسب نوع المشروع
        type_color = self.get_project_type_color(project_type)
        project_type_label.setStyleSheet(f"""
            QLabel {{
                background-color: {type_color};
                color: white;
                padding: 5px;
                border-radius: 3px;
                font-weight: bold;
            }}
        """)
        layout.addWidget(project_type_label)

        # معلومات العميل
        client_name = self.data.get('اسم_العميل', 'غير محدد')
        client_label = QLabel(f"👤 العميل: {client_name}")
        client_label.setObjectName("CardInfo")
        layout.addWidget(client_label)

        # معلومات المسؤول/المهندس الرئيسي
        manager_name = self.data.get('اسم_المهندس_الرئيسي', 'غير محدد')
        manager_label = QLabel(f"👨‍💼 المسؤول: {manager_name}")
        manager_label.setObjectName("CardInfo")
        layout.addWidget(manager_label)

        # المعلومات المالية (بدون المبلغ الإجمالي)
        financial_grid = QGridLayout()

        paid_amount = self.data.get('المدفوع', 0)
        remaining_amount = self.data.get('الباقي', 0)

        financial_grid.addWidget(QLabel("✅ المدفوع:"), 0, 0)
        # تلوين المدفوع باللون الأخضر
        paid_label = QLabel(f"{paid_amount:,.0f} د.ل")
        paid_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        financial_grid.addWidget(paid_label, 0, 1)

        financial_grid.addWidget(QLabel("⏳ المتبقي:"), 1, 0)
        remaining_label = QLabel(f"{remaining_amount:,.0f} د.ل")
        remaining_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        financial_grid.addWidget(remaining_label, 1, 1)

        layout.addLayout(financial_grid)

        # الحالة والوقت المتبقي مدموجين
        status_time_info = self.get_combined_status_time()
        status_time_label = QLabel(f"⏰ {status_time_info}")
        status_time_label.setObjectName("TimeInfo")
        status_time_label.setAlignment(Qt.AlignCenter)

        # تطبيق تلوين الحالة
        status_color = self.get_status_color()
        status_time_label.setStyleSheet(f"""
            QLabel {{
                color: {status_color};
                font-weight: bold;
                padding: 8px;
                border-radius: 5px;
                background-color: rgba(255, 255, 255, 0.1);
                font-size: 12px;
            }}
        """)

        layout.addWidget(status_time_label)

    def get_combined_status_time(self):
        """دمج حالة المشروع مع معلومات التوقيت"""
        status = self.data.get('الحالة', 'غير محدد')

        # حساب الوقت المتبقي أو المدة الفعلية
        if self.data.get('تاريخ_التسليم'):
            try:
                end_date = datetime.strptime(str(self.data['تاريخ_التسليم']), '%Y-%m-%d')
                today = datetime.now()

                if status in ['تم التسليم', 'تم الإنجاز']:
                    # للمشاريع المكتملة - حساب المدة الفعلية للإنجاز
                    if self.data.get('تاريخ_الإستلام'):
                        try:
                            start_date = datetime.strptime(str(self.data['تاريخ_الإستلام']), '%Y-%m-%d')
                            actual_days = (end_date - start_date).days
                            if status == 'تم التسليم':
                                return f"تم التسليم - أُنجز في {actual_days} يوماً"
                            else:
                                return f"تم الإنجاز - أُنجز في {actual_days} يوماً"
                        except:
                            if status == 'تم التسليم':
                                return "تم التسليم"
                            else:
                                return "تم الإنجاز"
                    else:
                        if status == 'تم التسليم':
                            return "تم التسليم"
                        else:
                            return "تم الإنجاز"
                else:
                    # للمشاريع قيد الإنجاز - حساب الوقت المتبقي
                    remaining_days = (end_date - today).days
                    if remaining_days > 0:
                        return f"قيد الإنجاز - {remaining_days} أيام"
                    elif remaining_days == 0:
                        return "قيد الإنجاز - ينتهي اليوم"
                    else:
                        return f"متأخر - {abs(remaining_days)} أيام"
            except:
                return f"{status} - غير محدد"
        else:
            return f"{status} - غير محدد"

    def get_status_color(self):
        """تحديد لون الحالة حسب النوع"""
        status = self.data.get('الحالة', '')

        # حساب الوقت المتبقي للتلوين
        if self.data.get('تاريخ_التسليم'):
            try:
                end_date = datetime.strptime(str(self.data['تاريخ_التسليم']), '%Y-%m-%d')
                today = datetime.now()
                remaining_days = (end_date - today).days

                if status in ['تم التسليم', 'تم الإنجاز']:
                    return "#27ae60"  # أخضر للمكتمل
                elif remaining_days == 0:
                    return "#f39c12"  # برتقالي لموعد اليوم
                elif remaining_days < 0:
                    return "#e74c3c"  # أحمر للمتأخر
                elif status == 'معلق':
                    return "#3498db"  # أزرق للمعلق
                elif status == 'متوقف':
                    return "#ffcccb"  # أحمر فاتح للمتوقف
                else:
                    return "#f1c40f"  # أصفر للباقي
            except:
                pass

        # الألوان الافتراضية حسب الحالة
        if status in ['تم التسليم', 'تم الإنجاز']:
            return "#27ae60"  # أخضر
        elif status == 'قيد الإنجاز':
            return "#3498db"  # أزرق
        elif status == 'متوقف':
            return "#e74c3c"  # أحمر
        elif status == 'معلق':
            return "#9b59b6"  # بنفسجي
        else:
            return "#95a5a6"  # رمادي

    def get_project_type_color(self, project_type):
        """الحصول على لون نوع المشروع حسب التصنيف من قاعدة البيانات"""
        try:
            # محاولة جلب اللون من قاعدة البيانات للمشاريع أولاً
            from for_all import get_categories_with_colors
            categories_with_colors = get_categories_with_colors("المشاريع")

            # البحث عن اللون المطابق للتصنيف
            for name, color in categories_with_colors:
                if name == project_type:
                    return color

            # إذا لم نجد في المشاريع، نبحث في المقاولات
            contracting_categories = get_categories_with_colors("المقاولات")
            for name, color in contracting_categories:
                if name == project_type:
                    return color

        except Exception as e:
            print(f"خطأ في جلب لون التصنيف: {e}")

        # الألوان الافتراضية في حالة عدم وجود التصنيف في قاعدة البيانات
        color_map = {
            # تصنيفات المشاريع
            'تصميم معماري': '#3498db',  # أزرق
            'تصميم داخلي': '#9b59b6',   # بنفسجي
            'إشراف': '#f39c12',         # برتقالي
            'إشراف هندسي': '#f39c12',   # برتقالي
            'إعداد مقايسات': '#e67e22', # برتقالي داكن

            # تصنيفات المقاولات
            'تنفيذ': '#8b4513',          # بني
            'بناء عظم': '#a0522d',       # بني فاتح
            'تشطيب': '#cd853f',         # بني ذهبي
            'مقاولات عامة': '#8b4513',  # بني
            'مقاولات متخصصة': '#d2691e', # برتقالي بني
            'صيانة وترميم': '#bc8f8f',   # وردي بني
            'مقاولات': '#8b4513',       # بني (للتوافق مع النظام القديم)
        }
        return color_map.get(project_type, '#95a5a6')  # رمادي للأنواع الأخرى

    def get_category_color(self, category_name, section_name):
        """الحصول على لون التصنيف من قاعدة البيانات لأي قسم"""
        try:
            from for_all import get_categories_with_colors
            categories_with_colors = get_categories_with_colors(section_name)

            # البحث عن اللون المطابق للتصنيف
            for name, color in categories_with_colors:
                if name == category_name:
                    return color

        except Exception as e:
            print(f"خطأ في جلب لون التصنيف {category_name}: {e}")

        # لون افتراضي في حالة عدم العثور على التصنيف
        return '#3498db'
        
    def create_client_content(self, layout):
        """محتوى بطاقة العميل"""
        # نوع العميل مع تلوين الخلفية
        client_type = self.data.get('التصنيف', 'غير محدد')
        if client_type != 'غير محدد':
            type_label = QLabel(f"🏢 نوع العميل: {client_type}")
            type_label.setObjectName("CardInfo")

            # تطبيق تلوين الخلفية حسب نوع العميل
            type_color = self.get_category_color(client_type, "العملاء")
            type_label.setStyleSheet(f"""
                QLabel {{
                    background-color: {type_color};
                    color: white;
                    padding: 5px;
                    border-radius: 3px;
                    font-weight: bold;
                }}
            """)
            layout.addWidget(type_label)

        phone = self.data.get('رقم_الهاتف', 'غير محدد')
        phone_label = QLabel(f"📞 الهاتف: {phone}")
        phone_label.setObjectName("CardInfo")
        layout.addWidget(phone_label)
        
        address = self.data.get('العنوان', 'غير محدد')
        address_label = QLabel(f"📍 العنوان: {address}")
        address_label.setObjectName("CardInfo")
        address_label.setWordWrap(True)
        layout.addWidget(address_label)
        
        # إحصائيات العميل
        projects_count = self.data.get('عدد_المشاريع', 0)
        total_value = self.data.get('إجمالي_القيمة', 0)
        
        stats_grid = QGridLayout()
        stats_grid.addWidget(QLabel("📊 عدد المشاريع:"), 0, 0)
        stats_grid.addWidget(QLabel(str(projects_count)), 0, 1)
        stats_grid.addWidget(QLabel("💰 إجمالي القيمة:"), 1, 0)
        stats_grid.addWidget(QLabel(f"{total_value:,.0f} د.ل"), 1, 1)
        
        layout.addLayout(stats_grid)
        
    def create_employee_content(self, layout):
        """محتوى بطاقة الموظف"""
        phone = self.data.get('رقم_الهاتف', 'غير محدد')
        phone_label = QLabel(f"📞 الهاتف: {phone}")
        phone_label.setObjectName("CardInfo")
        layout.addWidget(phone_label)
        
        salary = self.data.get('الراتب', 0)
        balance = self.data.get('الرصيد', 0)
        
        financial_grid = QGridLayout()
        financial_grid.addWidget(QLabel("💰 الراتب:"), 0, 0)
        financial_grid.addWidget(QLabel(f"{salary:,.0f} د.ل"), 0, 1)
        financial_grid.addWidget(QLabel("💳 الرصيد:"), 1, 0)
        balance_label = QLabel(f"{balance:,.0f} د.ل")
        balance_label.setObjectName("BalanceAmount")
        financial_grid.addWidget(balance_label, 1, 1)
        
        layout.addLayout(financial_grid)
        
    def create_expense_content(self, layout):
        """محتوى بطاقة المصروف"""
        # نوع المصروف مع تلوين الخلفية
        expense_type = self.data.get('التصنيف', 'غير محدد')
        if expense_type != 'غير محدد':
            type_label = QLabel(f"📋 نوع المصروف: {expense_type}")
            type_label.setObjectName("CardInfo")

            # تطبيق تلوين الخلفية حسب نوع المصروف
            type_color = self.get_category_color(expense_type, "الحسابات")
            type_label.setStyleSheet(f"""
                QLabel {{
                    background-color: {type_color};
                    color: white;
                    padding: 5px;
                    border-radius: 3px;
                    font-weight: bold;
                }}
            """)
            layout.addWidget(type_label)

        amount = self.data.get('المبلغ', 0)
        date_str = self.data.get('التاريخ', 'غير محدد')

        amount_label = QLabel(f"💰 المبلغ: {amount:,.0f} د.ل")
        amount_label.setObjectName("ExpenseAmount")
        layout.addWidget(amount_label)
        
        date_label = QLabel(f"📅 التاريخ: {date_str}")
        date_label.setObjectName("CardInfo")
        layout.addWidget(date_label)
        
        description = self.data.get('الوصف', 'لا يوجد وصف')
        desc_label = QLabel(f"📝 الوصف: {description}")
        desc_label.setObjectName("CardInfo")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
    def create_realestate_content(self, layout):
        """محتوى بطاقة العقار"""
        # نوع العقار مع تلوين الخلفية
        property_type = self.data.get('التصنيف', 'غير محدد')
        if property_type != 'غير محدد':
            type_label = QLabel(f"🏠 نوع العقار: {property_type}")
            type_label.setObjectName("CardInfo")

            # تطبيق تلوين الخلفية حسب نوع العقار
            type_color = self.get_category_color(property_type, "العقارات")
            type_label.setStyleSheet(f"""
                QLabel {{
                    background-color: {type_color};
                    color: white;
                    padding: 5px;
                    border-radius: 3px;
                    font-weight: bold;
                }}
            """)
            layout.addWidget(type_label)

        price = self.data.get('السعر', 0)
        area = self.data.get('المساحة', 0)
        location = self.data.get('الموقع', 'غير محدد')

        price_label = QLabel(f"💰 السعر: {price:,.0f} د.ل")
        price_label.setObjectName("PropertyPrice")
        layout.addWidget(price_label)
        
        area_label = QLabel(f"📐 المساحة: {area} م²")
        area_label.setObjectName("CardInfo")
        layout.addWidget(area_label)
        
        location_label = QLabel(f"📍 الموقع: {location}")
        location_label.setObjectName("CardInfo")
        layout.addWidget(location_label)
        
    def create_training_content(self, layout):
        """محتوى بطاقة التدريب"""
        # نوع الدورة مع تلوين الخلفية
        course_type = self.data.get('التصنيف', 'غير محدد')
        if course_type != 'غير محدد':
            type_label = QLabel(f"📚 نوع الدورة: {course_type}")
            type_label.setObjectName("CardInfo")

            # تطبيق تلوين الخلفية حسب نوع الدورة
            type_color = self.get_category_color(course_type, "التدريب")
            type_label.setStyleSheet(f"""
                QLabel {{
                    background-color: {type_color};
                    color: white;
                    padding: 5px;
                    border-radius: 3px;
                    font-weight: bold;
                }}
            """)
            layout.addWidget(type_label)

        trainer = self.data.get('المدرب', 'غير محدد')
        duration = self.data.get('المدة', 'غير محدد')
        participants = self.data.get('عدد_المشاركين', 0)

        trainer_label = QLabel(f"👨‍🏫 المدرب: {trainer}")
        trainer_label.setObjectName("CardInfo")
        layout.addWidget(trainer_label)
        
        duration_label = QLabel(f"⏱️ المدة: {duration}")
        duration_label.setObjectName("CardInfo")
        layout.addWidget(duration_label)
        
        participants_label = QLabel(f"👥 المشاركين: {participants}")
        participants_label.setObjectName("CardInfo")
        layout.addWidget(participants_label)
        
    def create_actions(self, layout):
        """إنشاء أزرار الإجراءات السريعة"""
        actions_layout = QVBoxLayout()
        actions_layout.setSpacing(5)

        # إنشاء أزرار مختلفة حسب نوع البطاقة
        if self.card_type == "project":
            self.create_project_actions(actions_layout)
        else:
            self.create_default_actions(actions_layout)

        layout.addLayout(actions_layout)

    def create_project_actions(self, layout):
        """إنشاء أزرار موحدة للمشاريع والمقاولات (عرض، تعديل، حذف)"""
        # حاوية خارجية للتوسيط المثالي
        outer_container = QWidget()
        outer_layout = QHBoxLayout(outer_container)
        outer_layout.setContentsMargins(0, 0, 0, 0)

        # حاوية الأزرار الرئيسية مع ارتفاع ثابت
        actions_container = QWidget()
        actions_container.setObjectName("ActionsContainer")
        actions_container.setFixedHeight(50)  # ارتفاع أقل لصف واحد
        actions_container.setFixedWidth(240)  # عرض ثابت للتوسيط المثالي

        actions_layout = QHBoxLayout(actions_container)
        actions_layout.setContentsMargins(10, 8, 10, 8)
        actions_layout.setSpacing(8)  # مسافة بين الأزرار

        # حساب عرض الأزرار
        button_width = 65  # عرض الأزرار
        button_height = 32  # ارتفاع الأزرار

        # زر عرض
        view_btn = QPushButton("👁️ عرض")
        view_btn.setObjectName("ViewBtn")
        view_btn.setFixedSize(button_width, button_height)
        view_btn.setToolTip("عرض تفاصيل المشروع")
        view_btn.clicked.connect(self.view_project)

        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setObjectName("EditBtn")
        edit_btn.setFixedSize(button_width, button_height)
        edit_btn.setToolTip("تعديل")
        edit_btn.clicked.connect(self.edit_project)

        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setObjectName("DeleteBtn")
        delete_btn.setFixedSize(button_width, button_height)
        delete_btn.setToolTip("حذف المشروع")
        delete_btn.clicked.connect(self.delete_project)

        # إضافة الأزرار إلى التخطيط
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(delete_btn)

        # توسيط الحاوية في الحاوية الخارجية
        outer_layout.addStretch(1)  # مساحة يسار
        outer_layout.addWidget(actions_container)  # الحاوية الرئيسية
        outer_layout.addStretch(1)  # مساحة يمين

        # إضافة الحاوية الخارجية إلى التخطيط الأساسي
        layout.addWidget(outer_container)

    def create_default_actions(self, layout):
        """إنشاء أزرار موحدة للأقسام الأخرى (العملاء، الموظفين، الحسابات، العقارات، التدريب)"""
        # حاوية خارجية للتوسيط المثالي
        outer_container = QWidget()
        outer_layout = QHBoxLayout(outer_container)
        outer_layout.setContentsMargins(0, 0, 0, 0)

        # حاوية الأزرار الرئيسية مع ارتفاع ثابت
        actions_container = QWidget()
        actions_container.setObjectName("ActionsContainer")
        actions_container.setFixedHeight(50)  # ارتفاع ثابت لصف واحد
        actions_container.setFixedWidth(240)  # عرض ثابت للتوسيط المثالي

        actions_layout = QHBoxLayout(actions_container)
        actions_layout.setContentsMargins(10, 8, 10, 8)
        actions_layout.setSpacing(8)  # مسافة بين الأزرار

        # حساب عرض الأزرار
        button_width = 65  # عرض الأزرار
        button_height = 32  # ارتفاع الأزرار

        # زر عرض
        view_btn = QPushButton("👁️ عرض")
        view_btn.setObjectName("ViewBtn")
        view_btn.setFixedSize(button_width, button_height)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(self.show_details)

        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setObjectName("EditBtn")
        edit_btn.setFixedSize(button_width, button_height)
        edit_btn.setToolTip("تعديل")
        edit_btn.clicked.connect(self.edit_item)

        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setObjectName("DeleteBtn")
        delete_btn.setFixedSize(button_width, button_height)
        delete_btn.setToolTip("حذف")
        delete_btn.clicked.connect(self.delete_item)

        # إضافة الأزرار إلى التخطيط
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(delete_btn)

        # توسيط الحاوية في الحاوية الخارجية
        outer_layout.addStretch(1)  # مساحة يسار
        outer_layout.addWidget(actions_container)  # الحاوية الرئيسية
        outer_layout.addStretch(1)  # مساحة يمين

        # إضافة الحاوية الخارجية إلى التخطيط الأساسي
        layout.addWidget(outer_container)
        
    def apply_modern_styles(self):
        """تطبيق الأنماط العصرية على البطاقة"""
        # تحديد لون البطاقة حسب الحالة أو النوع
        border_color = self.get_border_color()
        
        self.setStyleSheet(f"""
            QFrame#ModernCard {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid {border_color};
                border-radius: 15px;
                margin: 5px;
                
            }}
            QFrame#ModernCard:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 3px solid {border_color};
                /*transform: translateY(-2px);*/
            }}
            QLabel#CardIcon {{
                font-size: 24px;
                background-color: {border_color};
                border-radius: 20px;
                color: white;
            }}
            QLabel#CardTitle {{
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0 10px;
            }}
            QLabel#CardStatus {{
                background-color: {border_color};
                color: white;
                border-radius: 15px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px;
            }}
            QFrame#CardContent {{
                background-color: rgba(255,255,255,0.8);
                border-radius: 10px;
                border: 1px solid #e9ecef;
            }}
            QLabel#CardInfo {{
                font-size: 13px;
                color: #495057;
                margin: 2px 0;
            }}
            QLabel#RemainingAmount {{
                font-weight: bold;
                color: #e74c3c;
            }}
            QLabel#BalanceAmount {{
                font-weight: bold;
                color: #27ae60;
            }}
            QLabel#ExpenseAmount {{
                font-weight: bold;
                color: #f39c12;
                font-size: 14px;
            }}
            QLabel#PropertyPrice {{
                font-weight: bold;
                color: #8e44ad;
                font-size: 14px;
            }}
            QLabel#TimeInfo {{
                font-size: 12px;
                color: #6c757d;
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 5px;
            }}
            QProgressBar#ProjectProgress {{
                border: 2px solid #dee2e6;
                border-radius: 8px;
                text-align: center;
                height: 20px;
            }}
            QProgressBar#ProjectProgress::chunk {{
                background-color: {border_color};
                border-radius: 6px;
            }}
            QPushButton#ActionBtn {{
                background-color: #6c757d;
                border: none;
                border-radius: 15px;
                font-size: 14px;
                color: white;
            }}
            QPushButton#ActionBtn:hover {{
                background-color: #5a6268;
            }}
            QPushButton#ActionBtn:pressed {{
                background-color: #495057;
            }}

            /* حاوية الأزرار المحسنة */
            QWidget#ActionsContainer {{
                background-color: rgba(248, 249, 250, 0.9);
                border: 1px solid #e9ecef;
                border-radius: 10px;
                margin: 3px;
                padding: 2px;
            }}

            /* أنماط الأزرار الموحدة الجديدة */
            QPushButton#ViewBtn {{
                background-color: #27ae60;
                border: none;
                border-radius: 16px;
                font-size: 12px;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
            }}
            QPushButton#ViewBtn:hover {{
                background-color: #219a52;
                border: 1px solid #ffffff;
                /*transform: translateY(-1px);*/
            }}
            QPushButton#ViewBtn:pressed {{
                background-color: #1e8449;
                border: 1px solid #cccccc;
            }}

            QPushButton#EditBtn {{
                background-color: #3498db;
                border: none;
                border-radius: 16px;
                font-size: 12px;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
            }}
            QPushButton#EditBtn:hover {{
                background-color: #2980b9;
                border: 1px solid #ffffff;
                /*transform: translateY(-1px);*/
            }}
            QPushButton#EditBtn:pressed {{
                background-color: #21618c;
                border: 1px solid #cccccc;
            }}

            QPushButton#DeleteBtn {{
                background-color: #e74c3c;
                border: none;
                border-radius: 16px;
                font-size: 12px;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
            }}
            QPushButton#DeleteBtn:hover {{
                background-color: #c0392b;
                border: 1px solid #ffffff;
               /* transform: translateY(-1px);*/
            }}
            QPushButton#DeleteBtn:pressed {{
                background-color: #a93226;
                border: 1px solid #cccccc;
            }}
       
    
        """)
        
    def get_border_color(self):
        """تحديد لون الحدود حسب الحالة أو النوع"""
        if self.card_type == "project":
            status = self.data.get('الحالة', '')
            if status == 'قيد الإنجاز':
                return "#3498db"  # أزرق
            elif status == 'تم التسليم' or status == 'منتهي':
                return "#27ae60"  # أخضر
            elif status == 'متوقف':
                return "#e74c3c"  # أحمر
            elif status == 'معلق':
                return "#f39c12"  # برتقالي
            else:
                return "#95a5a6"  # رمادي
        elif self.card_type == "client":
            return "#9b59b6"  # بنفسجي
        elif self.card_type == "employee":
            return "#1abc9c"  # تركوازي
        elif self.card_type == "expense":
            return "#e67e22"  # برتقالي داكن
        elif self.card_type == "realestate":
            return "#34495e"  # رمادي داكن
        elif self.card_type == "training":
            return "#16a085"  # أخضر مزرق
        else:
            return "#95a5a6"  # رمادي افتراضي
            
    def mousePressEvent(self, event):
        """معالجة النقر على البطاقة"""
        if event.button() == Qt.LeftButton:
            self.card_clicked.emit(self.data)
        super().mousePressEvent(event)
        
    def mouseDoubleClickEvent(self, event):
        """معالجة النقر المزدوج على البطاقة"""
        if event.button() == Qt.LeftButton:
            self.card_double_clicked.emit(self.data)
            self.view_project()
        super().mouseDoubleClickEvent(event)

        
        
    def show_details(self):
        """عرض تفاصيل العنصر"""
        self.card_clicked.emit(self.data)

    def edit_item(self):
        """تعديل العنصر"""
        self.card_double_clicked.emit(self.data)

    def print_item(self):
        """طباعة العنصر"""
        # يمكن إضافة منطق الطباعة هنا
        pass

    def delete_item(self):
        """حذف العنصر مع رسالة تأكيد"""
        try:
            from PySide6.QtWidgets import QMessageBox

            # تحديد نوع العنصر للرسالة
            item_type = "العنصر"
            if self.card_type == "client":
                item_type = "العميل"
            elif self.card_type == "employee":
                item_type = "الموظف"
            elif self.card_type == "expense":
                item_type = "الحساب"
            elif self.card_type == "realestate":
                item_type = "العقار"
            elif self.card_type == "training":
                item_type = "الدورة التدريبية"

            # رسالة التأكيد
            reply = QMessageBox.question(
                self.parent(),
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف {item_type}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # إرسال إشارة للحذف مع البيانات
                self.card_clicked.emit({"action": "حذف", "data": self.data})

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في حذف العنصر: {str(e)}")

    def edit_project(self):
        """تعديل المشروع"""
        try:
            # إرسال إشارة للتعديل مع البيانات
            self.card_clicked.emit({"action": "تعديل", "data": self.data})

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في تعديل المشروع: {str(e)}")

    def delete_project(self):
        """حذف المشروع مع رسالة تأكيد"""
        try:
            from PySide6.QtWidgets import QMessageBox

            project_name = self.data.get('اسم_المشروع', 'المشروع')

            # رسالة التأكيد
            reply = QMessageBox.question(
                self.parent(),
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المشروع:\n{project_name}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # إرسال إشارة للحذف مع البيانات
                self.card_clicked.emit({"action": "حذف", "data": self.data})

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في حذف المشروع: {str(e)}")

    # ==================== دوال الأزرار الجديدة للمشاريع ====================

    def view_project(self):
        """عرض تفاصيل المشروع"""
        try:
            from مراحل_المشروع import open_project_phases_window
            project_type = self.data.get('نوع_المشروع', 'تصميم')

            # فتح نافذة مراحل المشروع في وضع العرض
            self.project_phases_window = open_project_phases_window(
                self.parent(), self.data, project_type
            )

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في فتح نافذة عرض المشروع: {str(e)}")
            # في حالة الفشل، استخدم النافذة القديمة كبديل
            try:
                from مراحل_المشروع import open_project_phases_window
                project_type = self.data.get('التصنيف', 'تصميم معماري')
                self.project_management_window = open_project_phases_window(
                    self.parent(), self.data, project_type
                )
            except:
                pass

    def manage_project(self):
        """إدارة المشروع"""
        try:
            from مراحل_المشروع import open_project_phases_window
            project_type = self.data.get('نوع_المشروع', 'تصميم')

            # فتح نافذة مراحل المشروع
            self.project_phases_window = open_project_phases_window(
                self.parent(), self.data, project_type
            )

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في فتح نافذة إدارة المشروع: {str(e)}")
            # في حالة الفشل، استخدم النافذة القديمة كبديل
            try:
                from مراحل_المشروع import open_project_phases_window
                project_type = self.data.get('التصنيف', 'تصميم معماري')
                self.project_management_window = open_project_phases_window(
                    self.parent(), self.data, project_type
                )
            except:
                pass

    def manage_payments(self):
        """إدارة المدفوعات والدفعات"""
        try:
            project_id = self.data.get('id')
            if not project_id:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self.parent(), "خطأ", "معرف المشروع غير متوفر")
                return

            # محاولة استيراد وفتح نافذة الدفعات
            try:
                from دفعات_المشروع import ProjectPaymentsWindow
                window = ProjectPaymentsWindow(self.parent(), project_id)
                window.show()
            except ImportError:
                # في حالة عدم توفر النافذة المخصصة، استخدم النافذة العامة
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self.parent(), "دفعات المشروع",
                                      f"سيتم فتح نافذة دفعات المشروع رقم {project_id}")

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في فتح نافذة الدفعات: {str(e)}")

    def manage_expenses(self):
        """إدارة مصروفات المشروع"""
        try:
            project_id = self.data.get('id')
            if not project_id:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self.parent(), "خطأ", "معرف المشروع غير متوفر")
                return

            # محاولة استيراد وفتح نافذة المصروفات
            try:
                from مصروفات_المشروع import ProjectExpensesWindow
                window = ProjectExpensesWindow(self.parent(), project_id)
                window.show()
            except ImportError:
                # في حالة عدم توفر النافذة المخصصة، استخدم النافذة العامة
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self.parent(), "مصروفات المشروع",
                                      f"سيتم فتح نافذة مصروفات المشروع رقم {project_id}")

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في فتح نافذة المصروفات: {str(e)}")

    def manage_custody(self):
        """إدارة العهد المالية"""
        try:
            project_id = self.data.get('id')
            project_name = self.data.get('اسم_المشروع', f'مشروع رقم {project_id}')
            client_name = self.data.get('اسم_العميل', '')

            if not project_id:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self.parent(), "خطأ", "معرف المشروع غير متوفر")
                return

            # فتح نافذة إدارة العهد المالية
            from العهد_المالية import CustodyManagementWindow
            window = CustodyManagementWindow(
                self.parent(),
                project_id=project_id,
                project_name=project_name,
                client_name=client_name
            )
            window.show()

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في فتح نافذة العهد المالية: {str(e)}")

    def manage_status(self):
        """إدارة وتحديث حالة المشروع"""
        try:
            project_id = self.data.get('id')
            project_code = self.data.get('رقم_المشروع', str(project_id))
            year = self.data.get('السنة', QDate.currentDate().year())

            if not project_id:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self.parent(), "خطأ", "معرف المشروع غير متوفر")
                return

            # فتح نافذة تحديث حالة المشروع
            try:
                from منظومة_المهندس import ProjectStatusDialog
                dialog = ProjectStatusDialog(
                    self.parent(), project_id, project_code, year, self.data
                )
                if dialog.exec() == QDialog.Accepted:
                    # تحديث البيانات المحلية
                    # يمكن إضافة منطق تحديث البطاقة هنا
                    pass
            except ImportError:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self.parent(), "حالة المشروع",
                                      f"سيتم فتح نافذة حالة المشروع رقم {project_code}")

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent(), "خطأ", f"فشل في فتح نافذة حالة المشروع: {str(e)}")

#حاوية البطاقات العصرية مع إمكانيات البحث والفلترة
class ModernCardsContainer(QScrollArea):
    
    def __init__(self, card_type="project", parent=None):
        super().__init__(parent)
        self.card_type = card_type
        self.cards = []
        self.all_data = []

        # تهيئة متغيرات التحكم في التوزيع أولاً
        self.card_width = 270  # عرض البطاقة الأساسي
        self.card_spacing = 15  # المسافة بين البطاقات
        self.min_margin = 20   # الهامش الأدنى من الجوانب
        self.cards_per_row_cache = 1  # تخزين مؤقت لعدد البطاقات في الصف
        self._current_filter_data = []  # البيانات المفلترة الحالية

        self.setup_ui()

    #إعداد واجهة حاوية البطاقات المحسنة مع دعم RTL
    def setup_ui(self):
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # تطبيق الاتجاه العربي RTL
        self.setLayoutDirection(Qt.RightToLeft)

        # الحاوية الرئيسية
        main_widget = QWidget()
        main_widget.setLayoutDirection(Qt.RightToLeft)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)  # هوامش محسنة
        main_layout.setSpacing(8)

        # شريط البحث والفلترة
        self.create_search_bar(main_layout)

        # منطقة البطاقات المحسنة
        self.cards_widget = QWidget()
        self.cards_widget.setLayoutDirection(Qt.RightToLeft)

        # استخدام FlowLayout محسن بدلاً من GridLayout
        self.cards_layout = self.create_enhanced_flow_layout()
        self.cards_widget.setLayout(self.cards_layout)

        main_layout.addWidget(self.cards_widget)
        main_layout.addStretch()

        self.setWidget(main_widget)

    def create_enhanced_flow_layout(self):
        """إنشاء تخطيط تدفق محسن للبطاقات مع دعم RTL"""
        # استخدام QGridLayout محسن مع إعدادات RTL
        layout = QGridLayout()
        layout.setSpacing(self.card_spacing)
        layout.setContentsMargins(self.min_margin, 10, self.min_margin, 10)
        layout.setAlignment(Qt.AlignTop | Qt.AlignRight)  # محاذاة لليمين للـ RTL

        return layout

    def calculate_optimal_columns(self, container_width=None):
        """حساب العدد الأمثل للأعمدة مع دعم محسن للشاشات العريضة"""
        if container_width is None:
            container_width = self.width()

        # التأكد من وجود عرض صالح
        if container_width <= 0:
            container_width = 800  # قيمة افتراضية

        # التحقق من التخزين المؤقت لتجنب الحسابات المتكررة
        cache_key = f"{container_width}_{self.card_width}_{self.card_spacing}_{self.min_margin}_widescreen"
        if hasattr(self, '_columns_cache') and cache_key in self._columns_cache:
            return self._columns_cache[cache_key]

        # حساب العرض المتاح للبطاقات (مع خصم الهوامش وشريط التمرير)
        scrollbar_width = 20  # عرض شريط التمرير
        available_width = container_width - (2 * self.min_margin) - scrollbar_width

        # حساب عدد البطاقات التي يمكن وضعها في صف واحد
        single_card_space = self.card_width + self.card_spacing
        cards_per_row = max(1, available_width // single_card_space)

        # حساب العرض الفعلي المطلوب للتحقق من الدقة
        total_cards_width = cards_per_row * self.card_width
        total_spacing_width = max(0, (cards_per_row - 1) * self.card_spacing)
        required_width = total_cards_width + total_spacing_width

        # إذا كان العرض المطلوب أكبر من المتاح، قلل عدد البطاقات
        if required_width > available_width and cards_per_row > 1:
            cards_per_row -= 1

        # ===== تحسين خاص للشاشات العريضة =====
        # للشاشات العريضة (أكبر من 1900px)، فرض حد أدنى 6 بطاقات
        if container_width > 1900:
            min_cards_for_widescreen = 6

            if cards_per_row < min_cards_for_widescreen:
                # التحقق من إمكانية عرض 6 بطاقات على الأقل
                required_width_for_6 = (min_cards_for_widescreen * self.card_width) + \
                                     ((min_cards_for_widescreen - 1) * self.card_spacing)

                if required_width_for_6 <= available_width:
                    cards_per_row = min_cards_for_widescreen
                    print(f"🖥️ شاشة عريضة ({container_width}px): فرض عرض {min_cards_for_widescreen} بطاقات")
                else:
                    # إذا لم تكن هناك مساحة كافية لـ 6 بطاقات، استخدم الحساب الطبيعي
                    print(f"⚠️ شاشة عريضة ({container_width}px): مساحة غير كافية لـ {min_cards_for_widescreen} بطاقات، استخدام {cards_per_row}")

        # تخزين النتيجة في التخزين المؤقت
        if not hasattr(self, '_columns_cache'):
            self._columns_cache = {}
        self._columns_cache[cache_key] = cards_per_row

        # تحديث التخزين المؤقت للصفوف
        self.cards_per_row_cache = cards_per_row

        # إضافة معلومات تشخيصية
        self._last_calculation_info = {
            'container_width': container_width,
            'available_width': available_width,
            'cards_per_row': cards_per_row,
            'is_widescreen': container_width > 1900,
            'required_width': (cards_per_row * self.card_width) + ((cards_per_row - 1) * self.card_spacing)
        }

        return max(1, cards_per_row)

    def get_layout_info(self):
        """الحصول على معلومات تشخيصية حول تخطيط البطاقات"""
        if hasattr(self, '_last_calculation_info'):
            info = self._last_calculation_info.copy()
            info['card_width'] = self.card_width
            info['card_spacing'] = self.card_spacing
            info['min_margin'] = self.min_margin
            return info
        return None

    def is_widescreen_mode(self):
        """التحقق من وضع الشاشة العريضة"""
        return self.width() > 1900

    def get_widescreen_threshold(self):
        """الحصول على حد الشاشة العريضة"""
        return 1900

    def get_min_cards_for_widescreen(self):
        """الحصول على الحد الأدنى للبطاقات في الشاشات العريضة"""
        return 6

    #إنشاء شريط البحث والفلترة
    def create_search_bar(self, layout):
        
        search_frame = QFrame()
        search_frame.setObjectName("SearchFrame")
        search_layout = QHBoxLayout(search_frame)
        search_layout.setContentsMargins(15, 10, 15, 10)
        
        # أيقونة البحث
        search_icon = QLabel("🔍")
        search_icon.setFixedSize(30, 30)
        search_icon.setAlignment(Qt.AlignCenter)
        
        # مربع البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في البيانات...")
        self.search_input.setObjectName("SearchInput")
        self.search_input.textChanged.connect(self.filter_cards)
        
        # فلتر الحالة/التصنيف
        self.status_filter = QComboBox()
        self.status_filter.setObjectName("StatusFilter")
        self.status_filter.currentTextChanged.connect(self.filter_cards)
        
        # زر إعادة تعيين
        reset_btn = QPushButton("🔄")
        reset_btn.setObjectName("ResetBtn")
        reset_btn.setFixedSize(40, 35)
        reset_btn.setToolTip("إعادة تعيين الفلاتر")
        reset_btn.clicked.connect(self.reset_filters)
        
        # search_layout.addWidget(search_icon)
        # search_layout.addWidget(self.search_input, 1)
        # search_layout.addWidget(self.status_filter)
        # search_layout.addWidget(reset_btn)
        
        # layout.addWidget(search_frame)
        
        # تطبيق الأنماط
        search_frame.setStyleSheet("""
            QFrame#SearchFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 10px;
            }
            QLineEdit#SearchInput {
                border: 2px solid #ced4da;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit#SearchInput:focus {
                border-color: #80bdff;
                outline: none;
            }
            QComboBox#StatusFilter {
                border: 2px solid #ced4da;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                min-width: 150px;
            }
            QPushButton#ResetBtn {
                background-color: #6c757d;
                border: none;
                border-radius: 17px;
                font-size: 16px;
                color: white;
            }
            QPushButton#ResetBtn:hover {
                background-color: #5a6268;
            }
        """)
    
    #إضافة البطاقات من قائمة البيانات
    def add_cards(self, data_list):
        self.clear_cards()
        self.all_data = data_list
        # تحديث فلتر الحالة/التصنيف
        self.update_status_filter()
        
        # إضافة البطاقات
        self.display_cards(data_list)

    #عرض البطاقات في الشبكة المحسنة مع دعم RTL
    def display_cards(self, data_list):
        self.clear_cards()
        if not data_list:
            # عرض رسالة عدم وجود بيانات
            self.show_empty_state()
            return

        # حساب العدد الأمثل للأعمدة
        cols = self.calculate_optimal_columns()

        # عرض البطاقات مع التوزيع الأفقي المحسن
        for i, data in enumerate(data_list):
            card = ModernCard(data, self.card_type)
            card.card_clicked.connect(self.on_card_clicked)
            card.card_double_clicked.connect(self.on_card_double_clicked)

            # حساب الموضع مع الاتجاه العربي RTL
            row = i // cols
            col = i % cols

            # إضافة البطاقة إلى التخطيط
            self.cards_layout.addWidget(card, row, col)
            self.cards.append(card)

        # تطبيق التوزيع المتوازن للأعمدة
        self.apply_column_stretch(cols)

    def apply_column_stretch(self, cols):
        """تطبيق التوزيع المتوازن للأعمدة"""
        # إزالة أي stretch سابق
        for i in range(self.cards_layout.columnCount()):
            self.cards_layout.setColumnStretch(i, 0)

        # تطبيق stretch متساوي للأعمدة المستخدمة
        for i in range(cols):
            self.cards_layout.setColumnStretch(i, 1)

        # إضافة stretch إضافي للمساحة المتبقية إذا لزم الأمر
        if cols < 10:  # حد أقصى معقول للأعمدة
            self.cards_layout.setColumnStretch(cols, 0)

    #عرض حالة عدم وجود بيانات
    def show_empty_state(self):
        
        empty_widget = QWidget()
        empty_layout = QVBoxLayout(empty_widget)
        empty_layout.setAlignment(Qt.AlignCenter)
        
        empty_icon = QLabel("📭")
        empty_icon.setAlignment(Qt.AlignCenter)
        empty_icon.setStyleSheet("font-size: 64px; color: #6c757d; margin: 20px;")
        
        empty_text = QLabel("لا توجد بيانات للعرض")
        empty_text.setAlignment(Qt.AlignCenter)
        empty_text.setStyleSheet("font-size: 18px; color: #6c757d; font-weight: bold;")
        
        empty_layout.addWidget(empty_icon)
        empty_layout.addWidget(empty_text)
        
        self.cards_layout.addWidget(empty_widget, 0, 0, 1, -1)
    
    #عرض حالة عدم وجود بيانات
    def clear_cards(self):
        for card in self.cards:
            card.deleteLater()
        self.cards.clear()
        
        # مسح جميع العناصر من التخطيط
        while self.cards_layout.count():
            child = self.cards_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    #تحديث خيارات فلتر الحالة/التصنيف      
    def update_status_filter(self):
        
        self.status_filter.clear()
        
        if self.card_type in ["project", "realestate", "training"]:
            self.status_filter.addItem("كل الحالات")
            statuses = set()
            for data in self.all_data:
                status = data.get('الحالة', '')
                if status:
                    statuses.add(status)
            for status in sorted(statuses):
                self.status_filter.addItem(status)
        else:
            self.status_filter.addItem("كل التصنيفات")
            classifications = set()
            for data in self.all_data:
                classification = data.get('التصنيف', '')
                if classification:
                    classifications.add(classification)
            for classification in sorted(classifications):
                self.status_filter.addItem(classification)

    # تم نقل دالة filter_cards المحسنة إلى الأسفل

    #إعادة تعيين جميع الفلاتر - محسن
    def reset_filters(self):
        """إعادة تعيين جميع الفلاتر وعرض جميع البيانات"""
        self.search_input.clear()
        self.status_filter.setCurrentIndex(0)

        # إعادة تعيين البيانات المفلترة
        self._current_filter_data = self.all_data
        self.display_cards(self.all_data)

    #معالجة النقر على البطاقة
    def on_card_clicked(self, data):
        """معالجة النقر على البطاقة - يتضمن الإجراءات من الأزرار"""
        try:
            # التحقق من وجود إجراء محدد من الأزرار
            if isinstance(data, dict) and "action" in data:
                action = data["action"]
                actual_data = data["data"]

                # معالجة الإجراءات المختلفة
                if action == "عرض":
                    self.handle_view_action(actual_data)
                elif action == "تعديل":
                    self.handle_edit_action(actual_data)
                elif action == "حذف":
                    self.handle_delete_action(actual_data)
            else:
                # النقر العادي على البطاقة - عرض التفاصيل
                self.handle_view_action(data)

        except Exception as e:
            print(f"خطأ في معالجة النقر على البطاقة: {e}")

    #معالجة النقر المزدوج على البطاقة
    def on_card_double_clicked(self, data):
        """معالجة النقر المزدوج على البطاقة - فتح نافذة التعديل"""
        try:
            self.handle_edit_action(data)
        except Exception as e:
            print(f"خطأ في معالجة النقر المزدوج على البطاقة: {e}")

    def handle_view_action(self, data):
        """معالجة إجراء العرض"""
        try:
            # إرسال إشارة للنظام الرئيسي لمعالجة العرض
            if hasattr(self.parent(), 'handle_card_action'):
                self.parent().handle_card_action("عرض", self.card_type, data)
        except Exception as e:
            print(f"خطأ في معالجة إجراء العرض: {e}")

    def handle_edit_action(self, data):
        """معالجة إجراء التعديل"""
        try:
            # إرسال إشارة للنظام الرئيسي لمعالجة التعديل
            if hasattr(self.parent(), 'handle_card_action'):
                self.parent().handle_card_action("تعديل", self.card_type, data)
        except Exception as e:
            print(f"خطأ في معالجة إجراء التعديل: {e}")

    def handle_delete_action(self, data):
        """معالجة إجراء الحذف"""
        try:
            # إرسال إشارة للنظام الرئيسي لمعالجة الحذف
            if hasattr(self.parent(), 'handle_card_action'):
                self.parent().handle_card_action("حذف", self.card_type, data)
        except Exception as e:
            print(f"خطأ في معالجة إجراء الحذف: {e}")

    #إعادة ترتيب البطاقات عند تغيير حجم النافذة - محسن
    def resizeEvent(self, event):
        """إعادة ترتيب البطاقات تلقائياً عند تغيير حجم النافذة"""
        super().resizeEvent(event)

        # تأخير إعادة الترتيب لتجنب الاستدعاءات المتكررة
        if hasattr(self, '_resize_timer'):
            self._resize_timer.stop()

        self._resize_timer = QTimer()
        self._resize_timer.setSingleShot(True)
        self._resize_timer.timeout.connect(self._on_resize_timeout)
        self._resize_timer.start(150)  # تأخير 150ms

    def _on_resize_timeout(self):
        """معالجة تغيير الحجم بعد التأخير"""
        if hasattr(self, 'all_data') and self.all_data:
            # إعادة عرض البطاقات مع التوزيع الجديد
            current_filter = getattr(self, '_current_filter_data', self.all_data)
            self.display_cards(current_filter)

    def filter_cards(self):
        """فلترة البطاقات حسب النص والحالة - محسن"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()

        filtered_data = []

        for data in self.all_data:
            # فلترة النص
            text_match = True
            if search_text:
                # البحث في جميع القيم النصية
                text_match = any(
                    search_text in str(value).lower()
                    for value in data.values()
                    if isinstance(value, (str, int, float))
                )

            # فلترة الحالة/التصنيف
            status_match = True
            if status_filter not in ["كل الحالات", "كل التصنيفات"]:
                if self.card_type in ["project", "realestate", "training"]:
                    status_match = data.get('الحالة', '') == status_filter
                else:
                    status_match = data.get('التصنيف', '') == status_filter

            if text_match and status_match:
                filtered_data.append(data)

        # حفظ البيانات المفلترة الحالية
        self._current_filter_data = filtered_data
        self.display_cards(filtered_data)

    def sync_filters_from_main(self, search_text="", classification_filter="", status_filter=""):
        """مزامنة الفلاتر من الواجهة الرئيسية"""
        try:
            # تحديث نص البحث
            if search_text != self.search_input.text():
                self.search_input.setText(search_text)

            # تحديث فلتر التصنيف/الحالة
            current_status_filter = self.status_filter.currentText()

            # تحديد الفلتر المناسب حسب نوع البطاقة
            if self.card_type in ["project", "realestate", "training"]:
                # للمشاريع والعقارات والتدريب نستخدم فلتر الحالة
                if status_filter and status_filter != "كل الحالات" and status_filter != current_status_filter:
                    index = self.status_filter.findText(status_filter)
                    if index >= 0:
                        self.status_filter.setCurrentIndex(index)
            else:
                # للأقسام الأخرى نستخدم فلتر التصنيف
                if classification_filter and classification_filter != "كل التصنيفات" and classification_filter != current_status_filter:
                    index = self.status_filter.findText(classification_filter)
                    if index >= 0:
                        self.status_filter.setCurrentIndex(index)

            # تطبيق الفلاتر
            self.filter_cards()

        except Exception as e:
            print(f"خطأ في مزامنة الفلاتر: {e}")

    def apply_unified_filters(self, search_text="", classification_filter="", status_filter="", year_filter=""):
        """تطبيق الفلاتر الموحدة من النظام الرئيسي"""
        try:
            # حفظ الفلاتر الحالية
            self._unified_filters = {
                'search': search_text,
                'classification': classification_filter,
                'status': status_filter,
                'year': year_filter
            }

            filtered_data = []

            for data in self.all_data:
                # فلترة النص
                text_match = True
                if search_text:
                    text_match = any(
                        search_text.lower() in str(value).lower()
                        for value in data.values()
                        if isinstance(value, (str, int, float))
                    )

                # فلترة التصنيف
                classification_match = True
                if classification_filter and classification_filter != "كل التصنيفات":
                    classification_match = data.get('التصنيف', '') == classification_filter

                # فلترة الحالة
                status_match = True
                if status_filter and status_filter != "كل الحالات":
                    status_match = data.get('الحالة', '') == status_filter

                # فلترة السنة (إذا كانت متوفرة في البيانات)
                year_match = True
                if year_filter and hasattr(data, 'get'):
                    # محاولة استخراج السنة من تاريخ الإضافة أو أي حقل تاريخ
                    date_fields = ['تاريخ_الإضافة', 'تاريخ_البدء', 'التاريخ']
                    for date_field in date_fields:
                        if date_field in data and data[date_field]:
                            try:
                                # استخراج السنة من التاريخ
                                date_str = str(data[date_field])
                                if year_filter in date_str:
                                    break
                            except:
                                continue
                    else:
                        # إذا لم نجد تطابق في أي حقل تاريخ
                        if year_filter != str(datetime.now().year):
                            year_match = False

                if text_match and classification_match and status_match and year_match:
                    filtered_data.append(data)

            # حفظ البيانات المفلترة وعرضها
            self._current_filter_data = filtered_data
            self.display_cards(filtered_data)

        except Exception as e:
            print(f"خطأ في تطبيق الفلاتر الموحدة: {e}")

    def get_current_filters(self):
        """الحصول على الفلاتر الحالية"""
        return {
            'search': self.search_input.text(),
            'status': self.status_filter.currentText(),
            'unified_filters': getattr(self, '_unified_filters', {})
        }